// import { withActions } from "@storybook/addon-actions/decorator";
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { <PERSON>, IntroAward, useToggle } from "ui";
import { useState } from "react";

// More on how to set up stories at: https://storybook.js.org/docs/react/writing-stories/introduction
const meta: Meta<typeof Jury> = {
  title: "ui/Atomic/Molecules/Jury/Jury",
  component: Jury,
  tags: ["ui", "molecules", "common"],
  parameters: {},
  argTypes: {},
};

export default meta;

type Story = StoryObj<typeof Jury>;

export const Default: Story = {
  render: (args) => {
    return (
      <div className="w-screen h-screen flex justify-center bg-gray-300 items-center">
        <div className="max-w-3xl mx-auto w-full flex justify-center items-center">
          <Jury>
          </Jury>
        </div>
      </div>
    );
  },
  args: {
    // value: "E-mailadres",
  },
};

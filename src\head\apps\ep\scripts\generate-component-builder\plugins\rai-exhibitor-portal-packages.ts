import { ExternalPackageComponentInjectionPluginBase } from '../externalPackageComponentInjectionPluginBase';
import * as externalPackageComponents from 'rai-exhibitor-portal';

class RAIExhibitorPortalPackagesPlugin extends ExternalPackageComponentInjectionPluginBase {
  order = 100;

  packageName = 'rai-exhibitor-portal';

  protected getExternalPackage(): object {
    return externalPackageComponents;
  }
}

export const raiExhibitorPortalPackagesPlugin = new RAIExhibitorPortalPackagesPlugin();

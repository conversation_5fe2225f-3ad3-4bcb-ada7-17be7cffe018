function Resolve-Valid-Sitecore-License() {
    param(
        [string]
        [ValidateNotNullOrEmpty()]
        $LicenseXmlPath = "C:\license\license.xml"
    )
    if (-not $LicenseXmlPath.EndsWith("license.xml")) {
        Write-Error "Sitecore license file must be named 'license.xml'."
    }
    if (-not (Test-Path $LicenseXmlPath)) {
        Write-Error "Could not find Sitecore license file at path '$LicenseXmlPath'."
        exit
    }
    # We actually want the folder that it's in for mounting
    return (Get-Item $LicenseXmlPath).Directory.FullName
}

function Install-Sitecore-Powershell-Tools() {
    param(
    )
    ################################################
    # Retrieve and import SitecoreDockerTools module
    ################################################

    # Check for Sitecore Gallery
    Import-Module PowerShellGet
    $SitecoreGallery = Get-PSRepository | Where-Object { $_.SourceLocation -eq $RepositoryUrl }
    if (-not $SitecoreGallery) {
        Write-Host "Adding Sitecore PowerShell Gallery..." -ForegroundColor Green
        Unregister-PSRepository -Name $RepositoryName -ErrorAction SilentlyContinue
        Register-PSRepository -Name $RepositoryName -SourceLocation $RepositoryUrl -InstallationPolicy Trusted
        $SitecoreGallery = Get-PSRepository -Name $RepositoryName
    }

    # Install and Import SitecoreDockerTools
    $dockerToolsVersion = "10.2.7"
    Remove-Module SitecoreDockerTools -ErrorAction SilentlyContinue
    if (-not (Get-InstalledModule -Name SitecoreDockerTools -RequiredVersion $dockerToolsVersion -ErrorAction SilentlyContinue)) {
        Write-Host "Installing SitecoreDockerTools..." -ForegroundColor Green
        Install-Module SitecoreDockerTools -RequiredVersion $dockerToolsVersion -Scope CurrentUser -Repository $SitecoreGallery.Name
    }
    Write-Host "Importing SitecoreDockerTools..." -ForegroundColor Green
    Import-Module SitecoreDockerTools -RequiredVersion $dockerToolsVersion
    Write-SitecoreDockerWelcome

    dotnet tool restore
}
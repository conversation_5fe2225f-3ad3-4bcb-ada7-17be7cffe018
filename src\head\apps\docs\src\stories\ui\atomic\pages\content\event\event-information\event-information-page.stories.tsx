import type { <PERSON>a, StoryObj } from "@storybook/react";
import { EventInformationPage } from "ui";
const meta: Meta<typeof EventInformationPage> = {
  title: "ui/Atomic/Pages/Content/Event/EventInformation/EventInformationPage",
  component: EventInformationPage,
  tags: ["autodocs", "ui", "molecules", "home-page"],
  parameters: {},
  argTypes: {},
};
export default meta;
type Story = StoryObj<typeof EventInformationPage>;
export const Schema: Story = {
  render: (args) => {
    return (
      <EventInformationPage {...args} />
    );
  },
  args: {},
};

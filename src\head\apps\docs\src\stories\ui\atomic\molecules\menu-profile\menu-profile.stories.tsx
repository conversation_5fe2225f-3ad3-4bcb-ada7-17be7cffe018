import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { <PERSON>uProfile, SessionProvider, SidebarProvider } from "ui";
import { EPSidebarData1 } from "../../../data/ep-sidebar.data";
import { Session } from "inspector";
import { EPHeaderData } from "../../../data/ep-header.data";

const meta: Meta<typeof MenuProfile> = {
  title: "ui/Atomic/Molecules/MenuProfile/MenuProfile",
  component: MenuProfile,
  tags: ["autodocs", "ui", "molecules", "profile-menu"],
  parameters: {},
  argTypes: {},
};

export default meta;

type Story = StoryObj<typeof MenuProfile>;
export const Schema: Story = {
  render: (args) => {
    const siteNavigation = EPSidebarData1.fields.data.siteNavigation;
    return (
      <>
        <SessionProvider>
          <SidebarProvider siteNavigation={siteNavigation}>
            <div className="h-80">
              <div className="flex self-stretch flex-1 border-b-2 gap-x-4 lg:gap-x-6 bg-neutral-50 h-28">
                <div className="relative flex flex-1"></div>
                <div className="flex gap-x-4 lg:gap-x-6">
                  <div className="relative">
                    <MenuProfile {...args} />
                  </div>
                </div>
              </div>
            </div>
          </SidebarProvider>
        </SessionProvider>
      </>
    );
  },
  args: {
    menus: EPHeaderData.fields.data.siteNavigation.links.results[0].children.results,
  },
};

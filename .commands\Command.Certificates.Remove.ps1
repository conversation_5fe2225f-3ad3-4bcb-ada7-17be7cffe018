Push-Location $PSScriptRoot
# Define the script context
Push-Location -Path ..
$ContextDir = Get-Location
Pop-Location
# Define the script context
$CommandDir = Get-Location
# Import the used powershell functions
. $CommandDir/.powershell/Foundation.Certificates.ps1

Push-Location $ContextDir

Uninstall-Root-Certificate

# Delete certificates from disk
Remove-Item $ContextDir\docker\traefik\certs\* -Exclude ".gitkeep" -Recurse -Force

Pop-Location
Pop-Location
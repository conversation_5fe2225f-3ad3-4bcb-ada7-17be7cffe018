
. $PSScriptRoot/Foundation.Sitecore.XMCloud.Authentication.ps1
. $PSScriptRoot/Foundation.Sitecore.XMCloud.Organization.ps1
. $PSScriptRoot/Foundation.Sitecore.XMCloud.Project.ps1
. $PSScriptRoot/Foundation.Sitecore.XMCloud.Environment.ps1
. $PSScriptRoot/Foundation.Sitecore.XMCloud.Variable.ps1
. $PSScriptRoot/Foundation.Sitecore.XMCloud.Deploy.ps1

function Assert-Response() {
    param(
        [object]
        $JSON
    )
    if($JSON) {
        if($JSON.Status) {
            Write-Host $JSON.Message -ForegroundColor Red
            return $false
        } else {
            return $true
        }

    }
    return $false
}
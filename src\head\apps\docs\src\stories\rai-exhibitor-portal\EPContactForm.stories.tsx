import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { WithSitecoreContextDecorator } from "@uxbee/storybook-mock-sc";
import { EPContactFormComponent } from "rai-exhibitor-portal";
import { ContactFormData } from "./data/ep-contact-form.data";

const meta: Meta<typeof EPContactFormComponent.Default> = {
    title: "RAI/Exhibitor Portal/Canvas/EPContactFormComponent",
    component: EPContactFormComponent.Default,
    tags: ["autodocs", "rai", "ep"],
    argTypes: {},
    decorators: [
        (Story, context) =>
            WithSitecoreContextDecorator(
                () => (
                    <div className="h-[30rem] bg-[#fafafa]">
                        <div className="grid grid-cols-12 gap-4">
                            <Story />
                        </div>
                    </div>
                ),
                context,
                EPContactFormComponent.Default,
                false
            ),
    ],
};

export default meta;

type Story = StoryObj<typeof EPContactFormComponent.Default>;

export const Default: Story = {
    // args: LabelFooterData,
    args: ContactFormData
};

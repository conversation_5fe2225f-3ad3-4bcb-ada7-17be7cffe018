pr:
  branches:
    include:
      - main
      - develop
trigger:
  branches:
    include:
      - main

jobs:
- job: build_and_deploy_job
  displayName: Build and Deploy Job
  condition: or(eq(variables['Build.Reason'], 'Manual'),or(eq(variables['Build.Reason'], 'PullRequest'),eq(variables['Build.Reason'], 'IndividualCI')))
  pool:
    vmImage: windows-latest
  variables:
  - group: customerportal-storybook-variable-group
  steps:
  - checkout: self
    fetchDepth: 10
    submodules: true
    lfs: true
  - task: DownloadSecureFile@1
    displayName: Download .npmrc configuration file
    inputs:
      secureFile: '.npmrc'
  - task: UseNode@1  
    inputs: 
        version: '$(Node.Version)'
  - task: CopyFiles@2
    displayName: Copy .npmrc configuration file into root folder
    inputs:
      sourceFolder: "$(Agent.TempDirectory)"
      contents: ".npmrc"
      targetFolder: "$(Build.SourcesDirectory)"
  - task: npmAuthenticate@0
    inputs:
      workingFile: '$(Build.SourcesDirectory)/.npmrc'
      customEndpoint: 'Font Awesome (Private NPM), Uxbee Azure Artifacts (Private NPM)'
  - task: CopyFiles@2
    displayName: Copy .npmrc configuration file into frontend folder
    inputs:
      sourceFolder: "$(Agent.TempDirectory)"
      contents: ".npmrc"
      targetFolder: "$(Build.SourcesDirectory)/src/head"
  - task: npmAuthenticate@0
    displayName: NPM Authenticate into Uxbee's private NPM
    inputs:
      workingFile: '$(Build.SourcesDirectory)/src/head/.npmrc'
      customEndpoint: 'Font Awesome (Private NPM), Uxbee Azure Artifacts (Private NPM)'
  - task: PowerShell@2
    displayName: Install and build using turbo
    inputs:
      targetType: 'inline'
      workingDirectory: '$(Build.SourcesDirectory)/src/head/'
      script: |
        # script to installed the required modules and build the ui-sitecore package.    
        npm install turbo --global
        npm install -g ts-node
        npm install
        turbo build --filter docs
  - task: PowerShell@2
    displayName: Build and publish storybook using Chromatic CLI
    inputs:
      targetType: 'inline'
      workingDirectory: '$(Build.SourcesDirectory)/src/head/'
      script: |
        # build and publish storybook using chromatic cli   
        cd apps/docs    
        npx chromatic --project-token=$(CHROMATIC_PROJECT_TOKEN) --ci --allow-console-errors --exit-zero-on-changes
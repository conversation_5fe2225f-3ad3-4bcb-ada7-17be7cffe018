function Install-JSS-Software-Globally {
	npm install -g @sitecore-jss/sitecore-jss-cli
	dotnet new install Sitecore.DevEx.Templates --nuget-source https://sitecore.myget.org/F/sc-packages/api/v3/index.json
}
function Add-Turbo-Support-To-TSConfig() {
  param(
    [string]
    $TurboPath,
    [string]
    $TargetWorkspace
  )
  $AppsPath = Join-Path $TurboPath "apps"
  $WorkspacePath = Join-Path $AppsPath $TargetWorkspace
  $JSONFile = Join-Path $WorkspacePath "tsconfig.json"
  $JSON = Read-JSON-From-File -JSONFile $JSONFile
  if(!($JSON.extends)) {
    Write-Host "Adding turbo tsconfig compatibility"
    $JSON | Add-Member -MemberType NoteProperty -Name "extends" -Value "tsconfig/nextjs.json"
    $JSON | ConvertTo-Json -Depth 100 | Out-File -Encoding "UTF8" $JSONFile
  } else {
    Write-Host "tsconfig was already extended for turbo compatibility"
  }
}

function Register-APIKey-For-App() {
  param (
      [string]
      $ProjectPath,
      [string]
      $AppName = "web"
  )
  $ApiKey = New-Guid

  $APIKeysSerializationPath = Join-Path $ProjectPath "/src/items/Services.API-Keys/API Keys"
  $APIKeyTemplate = Join-Path $ProjectPath "/src/items/.templates/Services/API-Key.yml"
  $APIKeySerializationItem = Join-Path $APIKeysSerializationPath "$AppName.yml"

  # locate if a serialized item
  If(Test-Path -PathType Leaf -Path $APIKeySerializationItem) {
    Write-Host "Reading the API Key from the serialized item" -ForegroundColor DarkYellow
    # If exits read the id from the serailized item and return it
    $sitecoreApiKeyMatches = Select-String -AllMatches -Pattern '^ID: "(.*?)"$' -Path $APIKeySerializationItem
    $ApiKey = $sitecoreApiKeyMatches.Matches.groups[1].value
  } else {
    Write-Host "Creating a new API Key for the application" -ForegroundColor DarkYellow
    # Else when missing create an serialized api key for the application
    New-Item -ItemType File -Path $APIKeySerializationItem -Force
    Copy-Item $APIKeyTemplate $APIKeySerializationItem -Force
    # Set the API Key in the serialized item
    (Get-Content $APIKeySerializationItem) | ForEach-Object { $_.replace('${API_KEY}', $ApiKey) } | Set-Content  $APIKeySerializationItem
    # Set the item name in the serialized item
    (Get-Content $APIKeySerializationItem) | ForEach-Object { $_.replace('${ITEM_NAME}', $AppName) } | Set-Content $APIKeySerializationItem
  }
  return $ApiKey
}

function Register-RenderingHost-For-App() {
  param (
      [string]
      $ProjectPath,
      [string]
      $AppName = "web",
      [string]
      $RenderingHost = "rendering",
      [int]
      $RenderingPort = 3000
  )
  $RenderingHostsSerializationPath = Join-Path $ProjectPath "/src/items/Services.Rendering-Hosts/Rendering Hosts"
  $RenderingHostTemplate = Join-Path $ProjectPath "/src/items/.templates/Services/Rendering-Host.yml"
  $RenderingHostSerializationItem = Join-Path $RenderingHostsSerializationPath "$AppName.yml"

  If(!(Test-Path -PathType Leaf -Path $RenderingHostSerializationItem)) {
    Write-Host "Creating a new serialized rendering host for the application" -ForegroundColor DarkYellow
    New-Item -ItemType File -Path $RenderingHostSerializationItem -Force
    Copy-Item $RenderingHostTemplate $RenderingHostSerializationItem -Force
    $ItemId = New-Guid
    # Set the Item Id in the serialized item
    (Get-Content $RenderingHostSerializationItem) | ForEach-Object { $_.replace('${ITEM_ID}', $ItemId) } | Set-Content  $RenderingHostSerializationItem
    # Set the Item Name in the serialized item
    (Get-Content $RenderingHostSerializationItem) | ForEach-Object { $_.replace('${ITEM_NAME}', $AppName) } | Set-Content  $RenderingHostSerializationItem
    # Set the App Name in the serialized item
    (Get-Content $RenderingHostSerializationItem) | ForEach-Object { $_.replace('${APP_NAME}', $AppName) } | Set-Content  $RenderingHostSerializationItem
    # Set the rendering host in the serialized item
    (Get-Content $RenderingHostSerializationItem) | ForEach-Object { $_.replace('${RENDERING_HOST}', $RenderingHost) } | Set-Content $RenderingHostSerializationItem
    # Set the rendering port in the serialized item
    (Get-Content $RenderingHostSerializationItem) | ForEach-Object { $_.replace('${RENDERING_PORT}', $RenderingPort) } | Set-Content $RenderingHostSerializationItem
  } else {
    Write-Host "Serialized rendering host for the application was found, skipping the creation" -ForegroundColor DarkYellow
  }
}

function Register-DockerOverride-For-App() {
  param (
    [string]
    $ProjectPath,
    [string]
    $AppName = "web",
    [string]
    $PublicRenderingHost = "rendering-$AppName.xmcloud.local",
    [string]
    $PublicAppHost = "$AppName.xmcloud.local",
    [int]
    $RenderingPort = 3000,
    [string]
    $ApiKey
  )
  if($PublicRenderingHost -eq "rendering-web.xmcloud.local") {
    $PublicRenderingHost = "rendering-web.xmcloud.local"
  }
  if($PublicAppHost -eq "web.xmcloud.local") {
    $PublicAppHost = "www.xmcloud.local"
  }
  $DockerOverrideTemplatePath = Join-Path $ProjectPath "/docker/.templates/docker-compose.override.rendering.tpl"
  $DockerOverrideAppFile = Join-Path $ProjectPath "docker-compose.override.rendering-$AppName.yml"
  If(!(Test-Path -PathType Leaf -Path $DockerOverrideAppFile)) {
    Write-Host "Creating a new docker-compose.override file for the application" -ForegroundColor DarkYellow
    Copy-Item $DockerOverrideTemplatePath $DockerOverrideAppFile

    (Get-Content $DockerOverrideAppFile) | ForEach-Object { $_.replace('${APP_NAME}', $AppName) } | Set-Content  $DockerOverrideAppFile
    (Get-Content $DockerOverrideAppFile) | ForEach-Object { $_.replace('${APP_HOST}', $PublicAppHost) } | Set-Content  $DockerOverrideAppFile
    (Get-Content $DockerOverrideAppFile) | ForEach-Object { $_.replace('${RENDERING_HOST}', $PublicRenderingHost) } | Set-Content  $DockerOverrideAppFile
    (Get-Content $DockerOverrideAppFile) | ForEach-Object { $_.replace('${RENDERING_PORT}', $RenderingPort) } | Set-Content  $DockerOverrideAppFile
    (Get-Content $DockerOverrideAppFile) | ForEach-Object { $_.replace('${API_KEY}', $ApiKey) } | Set-Content  $DockerOverrideAppFile
  } else {
    Write-Host "Docker-compose.override file was already availble for the application, skipping creation" -ForegroundColor DarkYellow
  }

}
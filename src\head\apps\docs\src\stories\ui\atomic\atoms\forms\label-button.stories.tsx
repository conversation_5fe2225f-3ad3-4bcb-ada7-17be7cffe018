// import { withActions } from "@storybook/addon-actions/decorator";
import type { Meta, StoryObj } from "@storybook/react";
import { LabelButton } from "ui";

// More on how to set up stories at: https://storybook.js.org/docs/react/writing-stories/introduction
const meta: Meta<typeof LabelButton> = {
  title: "ui/Atomic/Atoms/Forms/LabelButton",
  component: LabelButton,
  tags: ["ui", "molecules", "forms"],
  parameters: {},
  argTypes: {
  },
};

export default meta;

type Story = StoryObj<typeof LabelButton>;

export const Default: Story = {
  render: (args) => {
    return (
      <div className="w-screen h-screen p-5 gap-y-[10px]">
        <LabelButton {...args}
        />
      </div>
    );
  },
  args: {
    value:"Categories"
  },
};
import { ExternalPackageComponentInjectionPluginBase } from '../externalPackageComponentInjectionPluginBase';
import * as externalPackageComponents from 'metadata';

class MetadataPackagesPlugin extends ExternalPackageComponentInjectionPluginBase {
  order = 102;

  packageName = 'metadata';

  protected getExternalPackage(): object {
    return externalPackageComponents;
  }
}

export const metadataPackagesPlugin = new MetadataPackagesPlugin();

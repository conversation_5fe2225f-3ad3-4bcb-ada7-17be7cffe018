import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import {
  AwardSummary,
  FormStep,
} from "ui";
import { AwardsSummarystructureData, QuestionnaireData } from "../../../data/awardsform.data";
import { QuestionnaireResponseAnswer } from "rai-profile-service";
import { FileUploadQuestionWithAnswers } from "company-profile-data";
import { FieldValues } from "react-hook-form";
// More on how to set up stories at: https://storybook.js.org/docs/react/writing-stories/introduction
const meta: Meta<typeof AwardSummary> = {
  title: "ui/Atomic/Molecules/AwardSummary/AwardSummary",
  component: AwardSummary,
  tags: ["ui", "molecules", "common"],
  parameters: {},
  argTypes: {},
};

export default meta;

type Story = StoryObj<typeof AwardSummary>;

export const Default: Story = {
  render: (args) => {
    const submitStep = async (
      answers: QuestionnaireResponseAnswer[],
      files: FileUploadQuestionWithAnswers[],
      stepId: string,
      isSummary: boolean
    ): Promise<boolean> => {
      await new Promise((resolve) => {
        setTimeout(resolve, 1000);
      });
  
      return true; //Return false if you want to test error messages
    };

    const shouldShowStep = (step: FormStep, data: FieldValues) => {
      if (step.onlyShowWhen?.questionName && step.onlyShowWhen?.answerName) {
        const answerName = step.onlyShowWhen?.answerName;
        const answers = data[step.onlyShowWhen.questionName];
  
        if (!answers) {
          return false;
        } else if (Array.isArray(answers)) {
          return answers.some((answer) => answer === answerName);
        } else if (!answers.includes(answerName)) {
          return false;
        }
      }
      return true;
    };

    return (
      <div className="w-screen h-screen flex justify-center items-center">
          <AwardSummary
          awardsForm={AwardsSummarystructureData}
          language={"en"}
          submitStep={submitStep}
          questionnaire={QuestionnaireData} 
          shouldShowStep={shouldShowStep}>          
          </AwardSummary>
      </div>
    );
  },
  args: {
  },
};

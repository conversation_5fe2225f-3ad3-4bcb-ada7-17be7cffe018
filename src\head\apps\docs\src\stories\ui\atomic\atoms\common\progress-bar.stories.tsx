import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { ProgressBar } from "ui";

// More on how to set up stories at: https://storybook.js.org/docs/react/writing-stories/introduction
const meta: Meta<typeof ProgressBar> = {
  title: "ui/Atomic/Atoms/Common/ProgressBar",
  component: ProgressBar,
  tags: ["autodocs", "ui", "atoms"],
  parameters: {
  },
  argTypes: {
  },
};

export default meta;

type Story = StoryObj<typeof ProgressBar>;

export const Default: Story = {
  args: {
    className: '',
    value: 75
  },
};

[CmdletBinding()]
param(
    [string]
    $ProjectName = $ENV:XMC_PROJECT_NAME
)
Push-Location $PSScriptRoot

$CommandDir = Get-Location
Push-Location -Path ..
$ContextDir = Get-Location
Pop-Location
. $CommandDir/.powershell/Foundation.Sitecore.XMCloud.ps1

$UserFile = Join-Path $ContextDir "/.sitecore/user.json"
# Ensure that the user is autneticated
$IsAuthenticated = Confirm-XMCloud-Login -UserFile $UserFile

if($IsAuthenticated) {
    if($ProjectName) {
        $projectId = Get-Project-Id-by-Name -ProjectName $ProjectName
        if($projectId) {
            dotnet sitecore cloud project info -id $projectId
        } else {
            Write-Host "The project could not be located in the organization" -ForegroundColor Red
        }
    } else {
        dotnet sitecore cloud project list
    }
} else {
    Write-Host "User is not authenticated, no actions can be executed" -ForegroundColor Red
}

Pop-Location
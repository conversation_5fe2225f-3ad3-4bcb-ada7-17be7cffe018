Push-Location $PSScriptRoot
# Define the script context
$CommandDir = Get-Location
Push-Location -Path ..
$ContextDir = Get-Location
Pop-Location

Push-Location $ContextDir
#check if docker is running
$hash = docker compose ps --services --filter "status=running"
#Even when a service is not running there is still one empty row that needs to be taken into consideration. That is why 2 is used here.
if($hash.count -ge 2) {
	Write-Host "--- Your environment is running, I am taking it down! ---"
	docker compose down
}

. $CommandDir/.powershell/Foundation.Env.ps1
Read-Local-Environment-Variables-From-File -EnvFilePath (Join-Path $ContextDir ".env")

Write-Host "Keeping XM Cloud Tools image up to date" -ForegroundColor Green
$ToolsImageVersion = -join($Env:TOOLS_IMAGE, ":", $Env:SITECORE_VERSION)
docker pull $ToolsImageVersion

# Pull the CM image
docker compose -f docker-compose.yml pull cm
# Build the CM image for the project
docker compose build cm
# Make a switch to bring docker back online
docker compose up -d
#Start Ngnix
$PSSciptPath = Join-Path $CommandDir "Command.Nginx.ps1"
& $PSSciptPath
Pop-Location
Pop-Location

# Open the CM host
$hostToOpen = -join('https://', $Env:CM_HOST, '/sitecore');
Start-Process $hostToOpen
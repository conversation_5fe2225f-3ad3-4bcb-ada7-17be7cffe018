import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { WithSitecoreContextDecorator } from "@uxbee/storybook-mock-sc";
import { SessionOverviewComponent } from "rai-event-label";

const meta: Meta<typeof SessionOverviewComponent.Default> = {
    title: "RAI/Event Label/Session/Overview",
    component: SessionOverviewComponent.Default,
    tags: ["autodocs", "rai", "ep"],
    argTypes: {},
    decorators: [
        (Story, context) =>
            WithSitecoreContextDecorator(
                () => (
                    <div className="min-h-screen bg-[#fafafa]">
                        <Story />
                    </div>
                ),
                context,
                SessionOverviewComponent.Default,
                false
            ),
    ],
};

export default meta;

type Story = StoryObj<typeof SessionOverviewComponent.Default>;

export const Default: Story = {
    args: {
        filters: [
            {
                groupName: "Locations", filters: [
                    "Hall 3",
                    "Underground 4"
                ]
            },
            {
                groupName: "Speaker", filters: [
                    "Mr. Yu<PERSON>",
                    "Checkbox"
                ]
            }
        ],
        loadData: async (filters) => {
            return [
                {
                    title: "Hello",
                    date: "1 january 2020",
                    time: {
                        start: "10:00",
                        end: "11:00"
                    },
                    location: "Hall 3",
                    calendarUrl: "https://rai.nl?calendar.ics",
                    topics: [
                        "Environment",
                        "Nauseia"
                    ],
                    url: "https://rai.nl",
                    description: "Lorem ipsum dolor sit amet consectetur adipisicing elit. Nesciunt saepe sit ipsam similique voluptatem vel unde sint numquam non debitis, vitae cum id placeat architecto dolore, nam soluta recusandae amet!"
                },
                {
                    title: "Hello",
                    date: "1 january 2020",
                    time: {
                        start: "10:00",
                        end: "11:00"
                    },
                    location: "Hall 3",
                    calendarUrl: "https://rai.nl?calendar.ics",
                    topics: [
                        "Environment",
                        "Nauseia"
                    ],
                    url: "https://rai.nl",
                    description: "Lorem ipsum dolor sit amet consectetur adipisicing elit. Nesciunt saepe sit ipsam similique voluptatem vel unde sint numquam non debitis, vitae cum id placeat architecto dolore, nam soluta recusandae amet!"
                },
                {
                    title: "Hello",
                    date: "1 january 2020",
                    time: {
                        start: "10:00",
                        end: "11:00"
                    },
                    location: "Hall 3",
                    calendarUrl: "https://rai.nl?calendar.ics",
                    topics: [
                        "Environment",
                        "Nauseia"
                    ],
                    url: "https://rai.nl",
                    description: "Lorem ipsum dolor sit amet consectetur adipisicing elit. Nesciunt saepe sit ipsam similique voluptatem vel unde sint numquam non debitis, vitae cum id placeat architecto dolore, nam soluta recusandae amet!"
                }
            ]
        }
    },
};

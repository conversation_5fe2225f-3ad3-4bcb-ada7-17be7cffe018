# escape=`

#
# Development-only image for running Next.js in a containerized environment.
# Assumes that the Next.js rendering host source is mounted to c:\app.
#

ARG PARENT_IMAGE
FROM ${PARENT_IMAGE} as debug

USER ContainerAdministrator

WORKDIR /head
RUN icacls.exe C:\head\ /grant "Authenticated Users":(F) /t

SHELL ["powershell", "-Command", "$ErrorActionPreference = 'Stop'; $ProgressPreference = 'SilentlyContinue';"]

EXPOSE 3000

RUN npm install --location=global npm@10.2.3

RUN npm install turbo@2.5.0 --location=global

RUN npm install shx --location=global

# Add entry point execution file
COPY .\tools\entrypoints\turbo\Development.ps1 C:\tools\entrypoints\turbo\Development.ps1

#ENTRYPOINT "npm install && npm install next@canary && npm run start:connected"
#ENTRYPOINT "npm install && npm run start:connected"
[CmdletBinding()]
param(
    [string]
    [ValidateNotNullOrEmpty()]
    $AppName = "web",
    [int]
    [ValidateNotNullOrEmpty()]
    $AppPort = 3000,
    [string]
    $AppHost = "$AppName.xmcloud.local",
    [string]
    $RHHost = "$AppName-rendering.xmcloud.local",
    [string]
    [ValidateNotNullOrEmpty()]
    $JSSVersion = "22.8.0", #TODO: set the variable from the config after this file was copied to the project
    [string]
    $Templates = "nextjs, nextjs-sxa, nextjs-multisite",
    [string]
    [ValidateSet("GraphQL")]
    $FetchWith = "GraphQL",
    [string]
    [ValidateSet("SSG","SSR")]
    $Prerender = "SSR",
    [string]
    $DeploySecret = "DEPLOYMENT_SECRET_SITECORE_JSS_APPS",
    [string]
    $ComponentBuilderPatternIdentifier = "ui"
)
# Define the script context
$CommandDir = Get-Location
Push-Location -Path ..
$ContextDir = Get-Location
Pop-Location
# Setting some variables
$TurboPath = Join-Path $ContextDir "src/head"
$AppsPath = Join-Path $TurboPath "apps"
$WorkspacePath = Join-Path $AppsPath $AppName
$PackageFile = Join-Path $WorkspacePath "package.json"
$JssConfigurationTemplate = Join-Path $TurboPath "templates/turbo/jss-template"
$EslintConfigurationOrigFile = Join-Path $WorkspacePath ".eslintrc"
$EnvFile = Join-Path $ContextDir ".env"

# Import the used powershell functions
. $CommandDir/.powershell/Foundation.JSS.ps1
. $CommandDir/.powershell/Foundation.IO.ps1
. $CommandDir/.powershell/Foundation.Certificates.ps1
. $CommandDir/.powershell/Foundation.Turbo.ps1
. $CommandDir/.powershell/Foundation.JSON.ps1
. $CommandDir/.powershell/Foundation.Env.ps1
. $CommandDir/.powershell/Foundation.Docker.ps1
. $CommandDir/.powershell/Foundation.Git.ps1
. $CommandDir/.powershell/Foundation.Project.ps1

# Fix to ensure a non interactive installation
if($Templates -ceq "nextjs") {
    $Templates = "nextjs,nextjs"
}

# Create the rendering host and matching SSL certificates
$AppHostLower = $AppHost.ToLower()
if($AppHostLower -eq "web.xmcloud.local") {
    $AppHostLower = "www.xmcloud.local"
}
$AppHostSSL = "https://" + $AppHostLower

$RHHostLower = $RHHost.ToLower()
if($RHHostLower -eq "web-rendering.xmcloud.local") {
    $RHHostLower = "rendering.xmcloud.local"
}
#$RHHostSSL = "https://" + $RHHostLower
$RunningContainers = Find-Running-Containers
if($RunningContainers -ge 2) {
    Write-Host "Your docker is currently running, application can not be installed!" -ForegroundColor DarkYellow
    $message = "Do you want to bring down your docker environment?"
    $confirmation = Read-Host $message
    if ($confirmation -eq 'y') {
        Push-Location $ContextDir
        Write-Host "I am taking down!" -ForegroundColor DarkYellow
        docker compose down
        Pop-Location
    } else {
        Write-Host "Application installation is cancelled due to a running docker enviornment" -ForegroundColor Red
        exit
    }
}

if(!(Test-Path -Path $EnvFile -PathType Leaf)) {
    Write-Host "Application could not be created, as the project has not been initialized" -ForegroundColor Red
    # Exit the application
    exit
}

If(Test-Path -PathType container -Path $WorkspacePath) {
    $message = "$AppName already exitst, would you like to reinstall?"
    $confirmation = Read-Host $message
    if ($confirmation -eq 'y') {
        Remove-Item $WorkspacePath -Recurse -Force
        #TODO: Also remove the docker file
        $DockerOverrideAppFile = Join-Path $ContextDir "docker-compose.override.rendering-$AppName.yml"
        Remove-Item $DockerOverrideAppFile
        # When the override is removed, the docker compose file needs to be updated again
        Update-Docker-Compose-Files -ProjectPath $ContextDir
    }
}

if(Test-Path -Path $EnvFile -PathType Leaf) {
    # Ensure that all files for the app are created and that the API key is retrieved
    # Locating the API key and creating it when the the API Key is missing, than save it as a serialized item
    $ApiKey = Register-APIKey-For-App -ProjectPath $ContextDir -AppName $AppName
    # Fix the issue caused by Powershell return statement
    if($ApiKey.GetType().ToString() -ieq "System.Object[]") {
        $ApiKey = $ApiKey[-1]
    }
    Write-Host "The API key ($ApiKey) for $AppName has been registered" -ForegroundColor DarkYellow
    # Check if the serialized rendering host is available, when missing create it.
    Register-RenderingHost-For-App -ProjectPath $ContextDir -AppName $AppName -RenderingHost "rendering" -RenderingPort $AppPort
    # Create a docker override file for the application
    Register-DockerOverride-For-App -ProjectPath $ContextDir -AppName $AppName -PublicRenderingHost $RHHostLower -PublicAppHost $AppHostLower -RenderingPort $AppPort -ApiKey $ApiKey
    # Ensure that the environment file is updated with all the application override files.
    Update-Docker-Compose-Files -ProjectPath $ContextDir
}

If(!(Test-Path -PathType container -Path $WorkspacePath)) {
    #Install JSS Software
    Install-JSS-Software-Globally

    Write-Host "Creating the $AppName JSS app" -ForegroundColor DarkYellow
    Push-Location $AppsPath
    npx -y create-sitecore-jss@$JSSVersion --templates $Templates --appName $AppName --fetchWith $FetchWith --destination $WorkspacePath --hostName $RHHostLower --prerender $Prerender --no-prePushHook
    Pop-Location
    Push-Location $TurboPath
    # Uninstall sxa related NPM packages
    npm uninstall "sass" --workspace $AppName
    npm uninstall "sass-alias" --workspace $AppName
    npm uninstall "font-awesome" --workspace $AppName
    npm uninstall "bootstrap" --workspace $AppName

    # Uninstall eslint related packages set by Sitecore
    Write-Host "Uninstalling eslint packages set by Sitecore" -ForegroundColor DarkYellow
    npm uninstall "eslint-config-next" --workspace $AppName
    npm uninstall "eslint-config-prettier" --workspace $AppName
    npm uninstall "eslint-plugin-prettier" --workspace $AppName
    npm uninstall "eslint-plugin-yaml" --workspace $AppName

    # Install the turbo packages
    Write-Host "Installing shared configuration packages for turbo, eslint, tailwinc and tsconfig" -ForegroundColor DarkYellow
    npm install "eslint-config-custom" --workspace $AppName --save-dev
    npm install "tailwind-config" --workspace $AppName --save-dev
    npm install "tsconfig" --workspace $AppName --save-dev

    # Install the component packages
    Write-Host "Installing shared components packages for Sitecore, ui and ui-sitecore" -ForegroundColor DarkYellow
    npm install "ui" --workspace $AppName
    npm install "ui-sitecore" --workspace $AppName

    # Add all the needed npm packages to the npm workspace
    Write-Host "Installing modules that are needed by the shared component packages." -ForegroundColor DarkYellow
    npm install tailwind-merge --workspace $AppName
    npm install clsx --workspace $AppName
    npm install class-variance-authority --workspace $AppName
    npm install react-aria --workspace $AppName
    npm install react-icons --workspace $AppName
    npm install @headlessui/react --workspace $AppName
    npm install tailwindcss --workspace $AppName --save-dev
    Pop-Location

    # Remove '/src/asets' folder and its usage at '_app.tsx'
    $AssetsFolder = Join-Path $WorkspacePath "/src/assets"
    $AppTsxFile = Join-Path $WorkspacePath "/src/pages/_app.tsx"
    Remove-Item $AssetsFolder -Force -Recurse
    Remove-Item (Join-Path $WorkspacePath "src/lib/next-config/plugins/sass.js")
    # These pages are removed as they need a physical connectio to SItecore when running a build
    Remove-Item (Join-Path $WorkspacePath "src/pages/404.tsx")
    Remove-Item (Join-Path $WorkspacePath "src/pages/500.tsx")
    Remove-Item (Join-Path $WorkspacePath "src/pages/_error.tsx")
    if(Test-Path -PathType Leaf -Path $AppTsxFile) {
        (Get-Content $AppTsxFile).replace("import 'assets/main.scss';", "") | Set-Content $AppTsxFile
    }
    
    # Remove 'src/components' folder
    $ComponentsFolder = Join-Path $WorkspacePath "/src/components"
    Get-ChildItem -Path $ComponentsFolder -Include * -File -Recurse | ForEach-Object { $_.Delete() }

    #Copy shared jss configuration template files
    Write-Host "Copying the needed template files so the jss app is compatible with turbo" -ForegroundColor DarkYellow
    Copy-Files -Source $JssConfigurationTemplate -Target $WorkspacePath
    
    Write-Host "Preparing turbo shared lint configuration for the application" -ForegroundColor DarkYellow
    #Remove original .eslintrc file
    Remove-Item $EslintConfigurationOrigFile -Force

    Push-Location $TurboPath
    npx -y @uxbee/add-uxbee-sitecore-headless-modules@latest --install-all-modules --workspace $AppName --workspace-path $WorkspacePath
    Pop-Location

    Push-Location $WorkspacePath
    # Fix potential Eslint issues
    npm run lint -- --fix
    Pop-Location

    #TODO: we used to copy the templates based on the old web application, currently this doesn't seem to be the best way forward.
    # From templates copy the turbo-workspace template into the new workspace
    #Write-Host "Copying the turbo workspace template files" -ForegroundColor DarkYellow
    #$TurboWorkspaceTemplatePath = Join-Path $TurboPath "templates/turbo-workspace-template"
    #Copy-Files -Source $TurboWorkspaceTemplatePath -Target $WorkspacePath

    Write-Host "Preparing turbo shared tsconfig configuration for the application" -ForegroundColor Red
    Write-Host "This has not been automated yet and has to be setup manually" -ForegroundColor Red
    #TODO: The script below was used to inject shared ts support. This has to be reviewd and altered when needed.
    #Add-Turbo-Support-To-TSConfig -TurboPath $TurboPath -TargetWorkspace $AppName

    Write-Host "Preparing turbo shared tailwind configuration for the application" -ForegroundColor Red
    Write-Host "This has not been automated yet and has to be setup manually" -ForegroundColor Red
    #TODO: Copy the files tailwind.config.ts and postcss.config.css
    #TODO: Create a global.css stylesheet
    #TODO: inject the created global.css it in a template

    Write-Host "Aligning turbo tasks for the application" -ForegroundColor Red
    Write-Host "This has not been automated yet and has to be setup manually" -ForegroundColor Red
    # Align the tasks
    #TODO: these action demolish the package.json file and have to be reviewed
    Update-JSON-Scalar-Value -JSONFile $PackageFile -Property "scripts.dev" -Value "npm-run-all --serial bootstrap --parallel next:dev start:watch-components"
    Update-JSON-Scalar-Value -JSONFile $PackageFile -Property "scripts.next:dev" -Value "cross-env WATCHPACK_POLLING='true' NODE_OPTIONS='--inspect' next dev -p $AppPort"
    Update-JSON-Scalar-Value -JSONFile $PackageFile -Property "scripts.lint-fix" -Value "next lint --fix"
    #Update-JSON-Scalar-Value -JSONFile $PackageFile -Property "scripts.dev" -Value "start:connected"
    #Update-JSON-Scalar-Value -JSONFile $PackageFile -Property "scripts.next:dev" -Value "cross-env WATCHPACK_POLLING='true' NODE_OPTIONS='--inspect' next dev"
    #Update-JSON-Scalar-Value -JSONFile $PackageFile -Property "scripts.lint-fix" -Value "next lint --fix"

    # Add the JSS Component builder to the application
    # Set some variables
    $PSSciptRootPath = Join-Path $ContextDir ".commands"
    $PSSciptPath = Join-Path $PSSciptRootPath "Command.Add.JSS.Component.Builder.ps1"
    # Create the package in the same way that and enduser can execute this command
    # To be able to execut this the copy-commands-to-project has to be executed first
    Push-Location $PSSciptRootPath
    & $PSSciptPath -AppName $AppName -PatternIdentifier $ComponentBuilderPatternIdentifier
    Pop-Location

    Write-Host "Resetting and rebuilding turbo" -ForegroundColor DarkYellow
    Reset-Turbo-Build -TurboPath $TurboPath
    Push-Location $TurboPath
    # Linting has already been addressed and during build the lint script is executed
    turbo build
    Pop-Location

    Push-Location $WorkspacePath
    # Update environment file
    Set-EnvFileVariable "PUBLIC_URL" -Value $AppHostSSL

    Write-Host "Staring to setup and configure JSS" -ForegroundColor DarkYellow
    jss setup --instancePath $WorkspacePath --layoutServiceHost $RHHostSSL --apiKey $ApiKey --deploySecret $DeploySecret --nonInteractive true
    jss build
    Pop-Location

    #TODO: Add the custom uxbee modules to the application trhough npm install
    #CSP
    #External Libraries

} else {
    Write-Host "JSS workspace for $AppName already exists, skipping creation" -ForegroundColor DarkYellow
}
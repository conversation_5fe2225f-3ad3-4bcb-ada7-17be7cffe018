{"name": "docs", "version": "1.0.0", "private": true, "scripts": {"dev": "storybook dev -p 6006", "build": "storybook build", "lint": "eslint ./src/**/*.tsx ./src/**/*.ts", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "lint-fix": "next lint --fix"}, "dependencies": {"@storybook/addon-a11y": "^8.6.3", "@storybook/addon-actions": "^8.6.3", "@storybook/addon-styling-webpack": "^1.0.0", "@storybook/addon-viewport": "^8.6.3", "@tailwindcss/forms": "^0.5.6", "@uxbee/storybook-mock-sc": "^0.7.4", "axios": "^0.30.0", "axios-mock-adapter": "^2.1.0", "company-profile": "*", "next": "^15.3.1", "next-localization": "^0.12.0", "rai-components": "*", "rai-event-label": "*", "programs": "*", "rai-exhibitor-portal": "*", "visitor-portal": "*", "rai-profile-service": "*", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.50.1", "tailwindcss-animate": "^1.0.7", "themes": "^0.0.0", "ui": "*", "ui-sitecore": "^0.0.0"}, "devDependencies": {"@next/eslint-plugin-next": "^15.1.6", "@storybook/addon-essentials": "^8.6.3", "@storybook/addon-interactions": "^8.6.3", "@storybook/addon-links": "^8.6.3", "@storybook/addon-onboarding": "^8.6.3", "@storybook/addon-themes": "^8.6.3", "@storybook/blocks": "^8.6.3", "@storybook/cli": "^8.6.3", "@storybook/nextjs": "^8.6.3", "@storybook/react": "^8.6.3", "@storybook/testing-library": "^0.2.1", "@types/node": "^22.9.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.3", "autoprefixer": "^10.4.13", "eslint": "^8.56.0", "eslint-config-custom": "^0.0.0", "eslint-config-turbo": "^2.3.4", "eslint-plugin-storybook": "^0.6.13", "postcss": "^8.4.20", "storybook": "^8.6.3", "tailwind-branding": "*", "tailwindcss": "3.3.2", "tsconfig": "*", "typescript": "~5.4.0"}}
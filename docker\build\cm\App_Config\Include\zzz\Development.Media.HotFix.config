<?xml version="1.0" encoding="utf-8"?>
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
    <sitecore>
        <settings>
            <!--  
                 
MediaResponse.AlwaysIncludeServerUrl must be true, to always include CDN
url when getting media item url.
                  Otherwise, the request will
not be served by CDN server.
            -->
            <setting name="Media.AlwaysIncludeServerUrl">
                <patch:attribute name="value">true</patch:attribute>
            </setting>
            <!--  
                 
MediaResponse.MediaLinkServerUrl must be set to url of the CDN endpoint.
                  Change it from http://example.com, to the url of the CDN
endpoint.
            -->
            <setting name="Media.MediaLinkServerUrl">
                <patch:attribute name="value">https://$(env:host)</patch:attribute>
            </setting>
            <!--  
                 
MediaResponse.AlwaysAppendRevision should be true to always append
revision when getting media item url.
                  The purpose of the value is
to generate a unique URL for every revision.
            -->            
            <setting name="Media.AlwaysAppendRevision">
                <patch:attribute name="value">true</patch:attribute>
            </setting>
        </settings>
    </sitecore>
</configuration>
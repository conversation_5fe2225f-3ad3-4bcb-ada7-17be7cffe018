    server {
        listen       443 ssl;
        server_name  $cm_hostname;

        ssl_certificate      ../../docker/traefik/certs/$cm_hostname-combined.crt;
        ssl_certificate_key  ../../docker/traefik/certs/$cm_hostname.key;

        ssl_session_cache    shared:SSL:1m;
        ssl_session_timeout  5m;

        ssl_ciphers  HIGH:!aNULL:!MD5;
        ssl_prefer_server_ciphers  on;
        underscores_in_headers     on;
        ssl_protocols TLSv1.2 TLSv1.3;

        location / {
          proxy_pass          http://localhost:80;
          proxy_set_header    Host              $host;
          proxy_set_header    X-Real-IP         $remote_addr;
          proxy_set_header    X-Forwarded-For   $proxy_add_x_forwarded_for;
          proxy_set_header    X-Client-Verify   SUCCESS;
          proxy_set_header    X-Client-DN       $ssl_client_s_dn;
          proxy_set_header    X-SSL-Subject     $ssl_client_s_dn;
          proxy_set_header    X-SSL-Issuer      $ssl_client_i_dn;
          proxy_set_header    X-Forwarded-Proto $scheme;
          proxy_http_version 1.1;
          proxy_set_header Upgrade $http_upgrade;
          proxy_set_header Connection "upgrade";
          proxy_read_timeout 1800;
          proxy_connect_timeout 1800;
          client_max_body_size 100M;
        }
    }
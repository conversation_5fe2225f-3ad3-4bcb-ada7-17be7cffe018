import { ConfigPlugin } from '..';
import { JssConfig } from 'lib/config';
import { DefaultSiteRootPathPluginCore } from '@uxbee/uxbee-sitecore-headless-sxa-multisite';

class DefaultSiteRootPathPlugin implements ConfigPlugin {
  order = 75;

  async exec(config: JssConfig): Promise<JssConfig> {
    const defaultSiteRootPathPluginCore = new DefaultSiteRootPathPluginCore({
      sitecoreSiteName: config.sitecoreSiteName,
      sitesString: config.sites,
    });

    let defaultSiteRootPath = await defaultSiteRootPathPluginCore.initializeDefaultSiteRootPath();
    if (!defaultSiteRootPath) {
      console.warn('Impossible to retrieve default site root path.');
      defaultSiteRootPath = '';
    }

    return Object.assign({}, config, { defaultSiteRootPath });
  }
}

export const defaultSiteRootPathPlugin = new DefaultSiteRootPathPlugin();

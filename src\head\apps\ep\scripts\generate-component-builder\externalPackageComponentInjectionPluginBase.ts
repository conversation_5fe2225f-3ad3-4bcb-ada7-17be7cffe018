import { PackageDefinition } from '@sitecore-jss/sitecore-jss-dev-tools';
import { ComponentBuilderPlugin, ComponentBuilderPluginConfig } from '.';

const ComponentPostfixNamingConvention = 'Component';

export abstract class ExternalPackageComponentInjectionPluginBase
  implements ComponentBuilderPlugin
{
  order = 0;

  packageName = '';

  exec(config: ComponentBuilderPluginConfig): ComponentBuilderPluginConfig {
    const externalPackageDefinition: PackageDefinition = {
      name: this.packageName,
      components: [],
    };

    const externalPackage = this.getExternalPackage();
    for (const component in externalPackage) {
      const componentName = component.toString();
      if (componentName.endsWith(ComponentPostfixNamingConvention)) {
        const lastIndexOfComponentInName = componentName.lastIndexOf(
          ComponentPostfixNamingConvention
        );
        const componentNameSitecoreMatch = componentName.substring(0, lastIndexOfComponentInName);

        externalPackageDefinition.components.push({
          componentName: componentNameSitecoreMatch,
          moduleName: componentName,
        });
      }
    }

    config.packages?.push(externalPackageDefinition);

    return config;
  }

  protected getExternalPackage(): object {
    throw 'Implement in derived class.';
  }
}

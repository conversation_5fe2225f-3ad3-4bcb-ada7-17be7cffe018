# Stap 1: <PERSON><PERSON> naar de server op poort 3000
$serverInfo = Get-NetTCPConnection | Where-Object { $_.LocalPort -eq 3000 }
# Stap 2: Beëindig de server als deze is gevonden
if ($serverInfo) {
    $serverPid = $serverInfo.OwningProcess
    Write-Host "Node.js-server gevonden met PID $serverPid op poort 3000."

    # Beëindig het proces met het gevonden PID
    Stop-Process -Id $serverPid -Force
    Write-Host "Node.js-server 3000 met PID $serverPid is beëindigd."
} else {
    Write-Host "Geen Node.js-server gevonden op poort 3000."
}

# Stap 1: Zoek naar de server op poort 3001
$serverInfo = Get-NetTCPConnection | Where-Object { $_.LocalPort -eq 3001 }
# Stap 2: Beëindig de server als deze is gevonden
if ($serverInfo) {
    $serverPid = $serverInfo.OwningProcess
    Write-Host "Node.js-server gevonden met PID $serverPid op poort 3001."

    # Beë<PERSON>ig het proces met het gevonden PID
    Stop-Process -Id $serverPid -Force
    Write-Host "Node.js-server met PID $serverPid is beëindigd."
} else {
    Write-Host "Geen Node.js-server gevonden op poort 3001."
}

# Stap 1: Zoek naar de server op poort 3002
$serverInfo = Get-NetTCPConnection | Where-Object { $_.LocalPort -eq 3002 }
# Stap 2: Beëindig de server als deze is gevonden
if ($serverInfo) {
    $serverPid = $serverInfo.OwningProcess
    Write-Host "Node.js-server gevonden met PID $serverPid op poort 3002."

    # Beëindig het proces met het gevonden PID
    Stop-Process -Id $serverPid -Force
    Write-Host "Node.js-server met PID $serverPid is beëindigd."
} else {
    Write-Host "Geen Node.js-server gevonden op poort 3002."
}

# Stap 1: Zoek naar de server op poort 6006
$serverInfo = Get-NetTCPConnection | Where-Object { $_.LocalPort -eq 6006 }
# Stap 2: Beëindig de server als deze is gevonden
if ($serverInfo) {
    $serverPid = $serverInfo.OwningProcess
    Write-Host "Node.js-server gevonden met PID $serverPid op poort 6006."

    # Beëindig het proces met het gevonden PID
    Stop-Process -Id $serverPid -Force
    Write-Host "Node.js-server met PID $serverPid is beëindigd."
} else {
    Write-Host "Geen Node.js-server gevonden op poort 6006."
}


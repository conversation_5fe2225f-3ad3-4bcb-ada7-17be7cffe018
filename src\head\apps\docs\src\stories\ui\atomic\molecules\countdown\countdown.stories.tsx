import { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { Card, DevViewportIndicator, Stand } from "ui";
import { Countdown } from "ui";

const meta: Meta<typeof Countdown> = {
  title: "ui/Atomic/Molecules/Countdown/Countdown",
  component: Countdown,
  tags: ["autodocs", "ui", "molecules", "countdown"],
  parameters: {},
  argTypes: {},
};

export default meta;

type Story = StoryObj<typeof Countdown>;
export const Schema: Story = {
  render: (args) => {
    return (
      <div className="px-4 py-6 lg:px-32 bg-neutral-50">
        <div className="grid gap-x-3 lg:grid-cols-6">
          <div className="lg:col-span-3 ">
            <Countdown {...args} />
          </div>
        </div>
      </div>
    );
  },

  args: {
    eventDate: "2024-10-25",
  },
};

function Update-Docker-Compose-Files {
	param(
		[string]
		$ProjectPath
	)
	Push-Location $ProjectPath
	$DevelopmentSetup = Get-EnvFileVariable "DEVELOPMENT_SETUP" -Path (Join-Path $ProjectPath ".env")
	if($DevelopmentSetup -eq "docker") {
		$dockerCompoFiles = "docker-compose.yml;docker-compose.override.yml;docker-compose.override.rendering.yml"
		Get-ChildItem -File -Filter docker-compose.override.rendering-*.yml |
			ForEach-Object {
				$dockerCompoFiles += ";" + $_.Name
			}
	} else {
		$dockerCompoFiles = "docker-compose.yml;docker-compose.override.local.yml"
	}
	Set-EnvFileVariable "COMPOSE_FILE" -Value $dockerCompoFiles
	Set-EnvFileVariable "COMPOSE_PATH_SEPARATOR" -Value ";"
	Pop-Location
	Write-Host "Docker-compose.override files were reinitialized into your environment file" -ForegroundColor DarkYellow
}

function Read-Local-Environment-Variables-From-File {
	param(
		[string]
		$EnvFilePath = (Join-Path $pwd, ".env")
	)
	if (Test-Path $EnvFilePath) {
		# Read the content of the .env file
		$envContent = Get-Content $EnvFilePath

		# Loop through each line in the file
		foreach ($line in $envContent) {
			# Split the line into key and value
			if($line.Contains("=")) {
				$key, $value = $line -split '=', 2

				# Trim leading and trailing whitespaces from the key and value
				$key = $key.Trim()
				$value = $value.Trim()
				#Write-Host "Adding variable $key with the value $value" -ForegroundColor DarkYellow
				# Set the environment variable
				[System.Environment]::SetEnvironmentVariable($key, $value, [System.EnvironmentVariableTarget]::Process)
			}
		}
	} else {
		Write-Host ".env file not found."
	}
}
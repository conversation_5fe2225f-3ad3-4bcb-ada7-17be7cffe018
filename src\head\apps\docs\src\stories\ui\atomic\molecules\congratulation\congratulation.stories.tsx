import type { Meta, StoryObj } from "@storybook/react";
import { Congratulation } from "ui";
const meta: Meta<typeof Congratulation> = {
  title: "ui/Atomic/Molecules/Congratulation/Congratulation",
  component: Congratulation,
  tags: ["autodocs", "ui", "molecules", "Congratulation"],
  parameters: {},
  argTypes: {},
};
export default meta;
type Story = StoryObj<typeof Congratulation>;
export const Schema: Story = {
  render: (args) => {

    const productName = 'THOA portable wireless dim!'
    const productDate = 'September 15 2024'
    const awardName = 'DAME Design Awards'

    return (
      <div className="h-screen w-screen flex justify-center items-center bg-gray-300">
        <div className="max-w-4xl mx-auto w-full flex justify-center items-center">
            <Congratulation {...args} awardName={awardName} productName={productName} productDate={productDate}/>
        </div>
      </div>
    );
  },
  args: {
  },
};

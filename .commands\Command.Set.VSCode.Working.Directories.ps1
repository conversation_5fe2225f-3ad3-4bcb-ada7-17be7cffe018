[CmdletBinding()]
param(
)
# Define the script context
$CommandDir = Get-Location
Push-Location -Path ..
$ContextDir = Get-Location
Pop-Location

. $CommandDir/.powershell/Foundation.JSON.ps1
# Ensure that the eslint working directories are set for each developer
$VSCodeSettingsFile = Join-Path $ContextDir ".vscode/settings.json"
if(Test-Path -Path $VSCodeSettingsFile -PathType Leaf) {
    Update-JSON-Array-Value -JSONFile $VSCodeSettingsFile -Property "eslint.workingDirectories" -Value "./src/head/" -MultiProperty $false
}
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { WithSitecoreContextDecorator } from "@uxbee/storybook-mock-sc";
import { GridContainer, GridItem } from "ui";
import { PortraitComponent } from "ui-sitecore";

const meta: Meta<typeof PortraitComponent.Default> = {
    title: "ui-sitecore/components/collection/PortraitCard",
    component: PortraitComponent.Default,
    tags: ["autodocs", "rai", "ep"],
    argTypes: {},
    decorators: [
        (Story, context) =>
            WithSitecoreContextDecorator(
                () => (
                    <div className="min-h-screen bg-gray-300">
                        <div className="container py-10">
                            <GridContainer rowSpacing="04" columnSpacing="04">
                                <GridItem xs="12" lg="4" className="bg-white rounded-2xl overflow-hidden">
                                    <Story />
                                </GridItem>
                                <GridItem xs="12" lg="4" className="bg-white rounded-2xl overflow-hidden">
                                    <Story />
                                </GridItem>
                                <GridItem xs="12" lg="4" className="bg-white rounded-2xl overflow-hidden">
                                    <Story />
                                </GridItem>
                            </GridContainer>
                        </div>
                    </div>
                ),
                context,
                PortraitComponent.Default,
                false
            ),
    ],
};

export default meta;

type Story = StoryObj<typeof PortraitComponent.Default>;

export const Default: Story = {
    args: {
        fields: {
            Name: { value: "Mr Harry Potter" },
            Role: { value: "CEO" },
            Email: { value: "<EMAIL>" },
            Phone: { value: "****** 32423 444" },
            Mobile: { value: "******* 3242338" },
            LinkedIn: { value: { text: "Harry Potter", href: "#" } },
            Image: { value: { src: "https://raicdn.nl/cdn-cgi/image/width=384,quality=75,format=auto,fit=cover,sharpen=1/https://edge.sitecorecloud.io/raiamsterda13f7-raidigitalpdb6c-productionf3f5-ef30/media/project/rai-amsterdam-xmc/interclean/interclean/isd/persons/team/robert-stelling.jpg" } }
        },
        params: {
        }
    }
};

import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { UxLinkList, UxLink, } from  "ui";
import type {UxLinkProps} from "ui"
// import {  } from '../../../../../../../../packages/ui/src/atomic/atoms/common/UxLink';

// More on how to set up stories at: https://storybook.js.org/docs/react/writing-stories/introduction
const meta: Meta<typeof UxLinkList> = {
  title: "ui/Atomic/Atoms/Common/LinkList",
  component: UxLinkList,
  tags: ["autodocs", "ui", "atoms", "common"],
  parameters: {
  },
  argTypes: {
  },
};

export default meta;

type Story = StoryObj<typeof UxLinkList>;

export const Default: Story = {
  render: (args) => {
    return (
      <ul>
        <UxLinkList links={args.links}  >          
          {(link:UxLinkProps) => <li className="mb-10"><UxLink {...link}> {link.children} </UxLink></li>}
        </UxLinkList>       
      </ul>
    );
  },
  args: {
    links:
      [
        {
          "id":"1",
          "children": "Service",
          "href": "https://example.com/link1",
          "iconName": "Service",
          "selected": false,
          "disabled": false
        },
        {
          "id":"2",
          "children": "Helpcentrum",
          "href": "https://example.com/link2",
          "iconName": "Headset",
          "selected": false,
          "disabled": false
        },
        {
          "id":"3",
          "children": "Mijn gegevens",
          "href": "https://example.com/link3",
          "iconName": "Person",
          "selected": false,
          "disabled": false
        },      
      ]
    ,
  },
};

export const Selected: Story = {
  render: (args) => {
    return (
      <ul>
        <UxLinkList links={args.links}  >          
          {(link:UxLinkProps) => <li className="mb-10"><UxLink {...link}> {link.children} </UxLink></li>}
        </UxLinkList>       
      </ul>
    );
  },
  args: {
    links:
      [
        {
          "children": "Service",
          "href": "https://example.com/link1",
          "iconName": "Service",
          "selected": false,
          "disabled": false
        },
        {
          "children": "Helpcentrum",
          "href": "https://example.com/link2",
          "iconName": "Headset",
          "selected": true,
          "disabled": false
        },
        {
          "children": "Mijn gegevens",
          "href": "https://example.com/link3",
          "iconName": "Person",
          "selected": false,
          "disabled": false
        },      
      ]
    ,
  },
};

export const Disabled: Story = {
  render: (args) => {
    return (
      <ul>
        <UxLinkList links={args.links}  >          
          {(link:UxLinkProps) => <li className="mb-10"><UxLink {...link}> {link.children} </UxLink></li>}
        </UxLinkList>       
      </ul>
    );
  },
  args: {
    links:
      [
        {
          "children": "Service",
          "href": "https://example.com/link1",
          "iconName": "Service",
          "selected": false,
          "disabled": true
        },
        {
          "children": "Helpcentrum",
          "href": "https://example.com/link2",
          "iconName": "Headset",
          "selected": true,
          "disabled": false
        },
        {
          "children": "Mijn gegevens",
          "href": "https://example.com/link3",
          "iconName": "Person",
          "selected": false,
          "disabled": true
        },      
      ]
    ,
  },
};


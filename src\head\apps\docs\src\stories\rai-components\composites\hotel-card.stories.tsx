import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { WithSitecoreContextDecorator } from "@uxbee/storybook-mock-sc";
import { HotelCardComponent } from "rai-components";

const meta: Meta<typeof HotelCardComponent.Default> = {
  title: "rai-components/composites/HotelCard",
  component: HotelCardComponent.Default,
  tags: ["autodocs", "rai", "ep"],
  argTypes: {},
  decorators: [
    (Story, context) =>
      WithSitecoreContextDecorator(
        () => (
          <div className="min-h-screen bg-[#fafafa]">
            <Story />
          </div>
        ),
        context,
        HotelCardComponent.Default,
        false
      ),
  ],
};

export default meta;

type Story = StoryObj<typeof HotelCardComponent.Default>;

export const Default: Story = {
  args: {
    fields: {
      data: {
        datasource: {
          Title: {
            jsonValue: {
              value: "Novotel",
            },
          },
          Image: {
            jsonValue: {
              value: {
                src: "https://xmcloudcm.localhost/-/media/project/rai-amsterdam-xmc/blueprints/blueprint_label/break-rules.jpg?h=1024&iar=0&w=1024&rev=420bb9958c754e6bb97048358f44ee5c&hash=C2332CAE0CBB30A7C472979B323D829C",
                alt: "break-rules",
                width: "1024",
                height: "1024",
              },
            },
          },
          Rating: {
            jsonValue: {
              value: "4",
            },
          },
          ServiceTitle: {
            jsonValue: {
              value: "free wifi",
            },
          },
          ServiceIcon: {
            jsonValue: {
              value: "fa-solid fa-wifi",
            },
          },
          Content: {
            jsonValue: {
              value:
                '<div class="ck-content"><p data-placeholder="[No text in field]">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec convallis sit amet ipsum ut finibus. Pellentesque malesuada dui eu quam elementum, eget egestas odio aliquam.&nbsp;</p></div>',
            },
          },
          children: {
            results: [
              {
                Specification: {
                  jsonValue: {
                    value: "Located next to RAI Amsterdam",
                  },
                },
                SpecificationIcon: {
                  jsonValue: {
                    value: "fa-solid fa-utensils",
                  },
                },
              },
              {
                Specification: {
                  jsonValue: {
                    value: "Selva restaurant with view over Amsterdam",
                  },
                },
                SpecificationIcon: {
                  jsonValue: {
                    value: "fa-solid fa-map-pin",
                  },
                },
              },
            ],
          },
        },
      },
    },
    params: {
      styles: " ",
    },
  },
};

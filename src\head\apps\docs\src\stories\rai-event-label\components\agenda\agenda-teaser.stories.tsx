import type { <PERSON>a, StoryObj } from "@storybook/react";
import { AgendaTeaserComponent } from 'rai-event-label';  // Adjust the import path according to your project structure
import { agendaListMock } from "./agenda.mock";

const meta: Meta<typeof AgendaTeaserComponent.Default> = {
    title: "RAI/Event Label/Agenda/Teaser",
  component: AgendaTeaserComponent.Default,
  tags: ["autodocs", "component"],
  argTypes: {},
  decorators: [
    (Story) => (
      <div className="min-h-screen bg-[#fafafa] p-4">
        <Story />
      </div>
    ),
  ],
};

export default meta;

type Story = StoryObj<typeof AgendaTeaserComponent.Default>;

export const Default: Story = {
  args: {
    agenda: 'Calendar',
    title: 'These events will be at <br> RAI Amsterdam soon',
    agendaList: agendaListMock,
    withTitle: true,
  },
};
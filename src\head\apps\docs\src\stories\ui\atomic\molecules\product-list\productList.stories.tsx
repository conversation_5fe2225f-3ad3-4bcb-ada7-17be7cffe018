import type { <PERSON>a, StoryObj } from "@storybook/react";
import { ProductList } from "ui";
import { ProductListData } from "../../../data/productlist.data";

const meta: Meta<typeof ProductList> = {
  title: "ui/Atomic/Molecules/product-list/ProductList",
  component: ProductList,
  tags: ["autodocs", "ui", "molecules", "productList"],
  parameters: {
  },
  argTypes: {},
};

export default meta;

type Story = StoryObj<typeof ProductList>;
export const Schema: Story = {
  render: (args) => {
    return (
      <div className="h-screen w-screen flex justify-center items-center  bg-neutral-50">
        <div className="max-w-4xl mx-auto min-w-[600px]">
          <div className="flex-container flex-wrap gap-size-x-4 gap-size-y-10">
            <div className="component flex-item col-12 mb-16 gap-y-12 lg:gap-x-3 lg:mb-14">
              <ProductList {...args} />
            </div>
          </div>
        </div>
      </div>
    );
  },

  args: {
    title: 'My innovative products',
    tooltip: 'My innovative products tooltip',
    addProductLinkText: 'Add product',
    addProductLink: '/company-profile?tab=products',
    noProductsMessage: 'No products found',
    products: ProductListData,
  },
};

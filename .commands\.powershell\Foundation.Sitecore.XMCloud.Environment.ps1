function Get-Environment-Id-by-Name() {
    param(
        [string]
        $ProjectName,
        [Parameter(ValueFromPipelineByPropertyName)]
        [ValidateSet(
            'test',
            'acceptation',
            'production'
        )]
        [string]
        $EnvironmentName,
        [bool]
        $AutoCreate = $false,
        [bool]
        $Force = $false
    )
    $key = (-join('XMC_PROJECT_',$ProjectName, '_Environment_', $EnvironmentName) -replace ('\W', '_')).ToUpper().Trim()
    $environmentId = $null
    if(!$Force) {
        $environmentId = [System.Environment]::GetEnvironmentVariable($key, [System.EnvironmentVariableTarget]::Process)
        Write-Debug "PresetId ($environmentId) is used"
    } else {
        [System.Environment]::SetEnvironmentVariable($key, $null, [System.EnvironmentVariableTarget]::Process)
    }
    if($environmentId) {
        Write-Debug "Matching environment id for the environment $EnvironmentName in project name ($ProjectName) was found in your local environment variables, this environment id ($EnvironmentId) is used."
        return $environmentId
    } else {
        $projectId = Get-Project-Id-by-Name -ProjectName $ProjectName
        if($projectId) {
            $Environments = dotnet sitecore cloud environment list --project-id $projectId --json | ConvertFrom-Json
            foreach ($environment in $Environments) {
                if($environment.name -eq $EnvironmentName) {        
                    $environmentId = $environment.id
                    [System.Environment]::SetEnvironmentVariable($key, $environmentId, [System.EnvironmentVariableTarget]::Process)
                    Write-Debug "For the $EnvironmentName environment the id ($environmentId) has been found."
                    return $environmentId
                }
            }
        }
    }
    # If all fails remove the varaible from the environment vaiables
    Write-Debug "No environment found with the name $EnvironmentName in the project $ProjectName of the active organization"

    if($AutoCreate) {
        Write-Debug "Auto creating a new environment $EnvironmentName"
        $environmentId = Add-Environment-For-Project -ProjectName $ProjectName -EnvironmentName $EnvironmentName
        [System.Environment]::SetEnvironmentVariable($key, $environmentId, [System.EnvironmentVariableTarget]::Process)
        return $environmentId
    }
}

function Add-Environment-For-Project() {
    param(
        [string]
        $ProjectName,
        [Parameter(ValueFromPipelineByPropertyName)]
        [ValidateSet(
            'test',
            'acceptation',
            'production'
        )]
        [string]
        $EnvironmentName
    )
    # Check if the environment already exits, ensure that autocreate is set to false to avoid loops
    $environmentId = Get-Environment-Id-by-Name -ProjectName $ProjectName -EnvironmentName $EnvironmentName -Force $true -AutoCreate $false
    if(!$environmentId) {
        # Environment was not detected, it can be created
        # Locate the project id where the environment should be created in
        $projectId = Get-Project-Id-by-Name -ProjectName $ProjectName
        if(!($EnvironmentName -eq 'production')) {
            $response = dotnet sitecore cloud environment create --project-id $projectid -n $EnvironmentName --json | ConvertFrom-Json
            if(Assert-Response($response)) {
                Write-Host "$EnvironmentName was sucessfully created for $ProjectName" -ForegroundColor DarkYellow
            }
        } else {
            $confirmation = Read-Host "An environment of the type $EnvironmentName should not be created from a local environment, are you sure you want to continue?"
            if ($confirmation -eq 'y') {
                $response = dotnet sitecore cloud environment create --project-id $projectid -n $EnvironmentName --prod | ConvertFrom-Json
                if(Assert-Response($response)) {
                    Write-Host "$EnvironmentName was sucessfully created for $ProjectName" -ForegroundColor DarkYellow
                }
            }
        }

        $environmentId = Get-Environment-Id-by-Name -ProjectName $ProjectName -EnvironmentName $EnvironmentName -Force $true -AutoCreate $false
    } else {
        Write-Debug "Environment already exits, creation is skipped"
    }
    return $environmentId
}

function Remove-Environment-From-Project() {
    param(
        [string]
        $ProjectName,
        [Parameter(ValueFromPipelineByPropertyName)]
        [ValidateSet(
            'test',
            'acceptation',
            'production'
        )]
        [string]
        $EnvironmentName
    )
    # Check if the environment already exits, ensure that autocreate is set to false to avoid loops
    $environmentId = Get-Environment-Id-by-Name -ProjectName $ProjectName -EnvironmentName $EnvironmentName -Force $true -AutoCreate $false
    if($environmentId -AND !($EnvironmentNamev-eq "production")) {
        # Environment was found or it can be removed by this script and can therefor be deleted
        Write-Host "Removing the $EnvironmentName environment with id ($environmentId)" -ForegroundColor DarkYellow
        $response = dotnet sitecore cloud environment delete --environment-id $environmentId --force --json | ConvertFrom-Json
        if(Assert-Response($response)) {
            Write-Host "$EnvironmentName was sucessfully deleted from $ProjectName"
            $environmentId = Get-Environment-Id-by-Name -ProjectName $ProjectName -EnvironmentName $EnvironmentName -Force $true -AutoCreate $false
        }
    } else {
        if($EnvironmentNamev-eq "production") {
            Write-Host "A production environment could not be deleted with this script." -ForegroundColor Red
        } else {
            Write-Host "Environment could not be found and could therefor not be deleted." -ForegroundColor Red
        }
    }
}
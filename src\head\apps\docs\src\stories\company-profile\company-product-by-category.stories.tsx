import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { CompanyProductByCategory } from "company-profile";

// More on how to set up stories at: https://storybook.js.org/docs/react/writing-stories/introduction
const meta: Meta<typeof CompanyProductByCategory> = {
  title: "RAI/Company Profile/CompanyProductByCategory",
  component: CompanyProductByCategory,
  tags: ["autodocs", "company-profile"],
  args: {
    segments: [
      {
        segmentName: "Segment",
        categoriesSelections: [
          {
            groupName: "AI and Robotics",
            categories: [
              {
                title: "Smart technologies",
                count: 10,
                id: "1001",
              },
            ],
          },
          {
            groupName: "Equipment, Climate, and Logistics",
            categories: [
              {
                title: "air condititoning",
                count: 10,
                id: "1002",
              },
              {
                title: "air tightehing",
                count: 10,
                id: "1004",
              },
              {
                title: "airfoil",
                count: 10,
                id: "1009",
              },
              {
                title: "thurst vectoring nozzle",
                count: 10,
                id: "2002",
              },
              {
                title: "full metal avionics system",
                count: 10,
                id: "2202",
              },
              {
                title: "Algea",
                count: 10,
                id: "2302",
              },
            ],
          },
        ],
      },
      {
        segmentName: "Crops",
        categoriesSelections: [
          {
            groupName: "Crop Types",
            categories: [
              {
                title: "Flower seeds",
                count: 10,
                id: "3001",
              },
            ],
          },
        ],
      },
    ],
    onRequestData: async (
      hostname: string,
      letter?: string,
      filters?: string[],
      sort?: string,
      search?: string,
      pageToLoad?: number
    ) => {
      console.log(
        "REQUEST DATA",
        hostname,
        letter,
        filters,
        sort,
        search,
        pageToLoad
      );

      await new Promise<void>((resolve) => {
        setTimeout(() => {
          resolve();
        }, 2000);
      });

      return {
        exhibitors: Array.from({ length: 10 }, (_, index) => ({
          url: "https://company.intercleanshow.com/2Pure-Products?Language=EN&eventid=30179&account=********-0",
          slug: "hello",
          detailUrl: "https://rai.nl/amsterdam/exhibitors/hello",
          logo: index % 3 === 0 ? undefined : "https://ep.rai.nl/mypages/en/Home/ShowLogo?lang=nl&docid=62816&width=190&height=120",
          name: `2Pure Products Exhibitor ${index} at Page ${pageToLoad}`,
          description: "Manufacturer of OdorBac Tec4",
        })),
        totalCount: 10,
        totalPages: 1,
      };
    },
    placeholderLogo:
      "https://assets-prd.raicore.com/amsterdam/-/media/project/rai-amsterdam/intertraffic/logo/itd-logo/internal/ita-intertraffic-logo.png?h=62&iar=0&w=300&rev=5a3701f6ef31471baf506db9c72e5660&hash=100134F472536BE089E36E3D354AB063",
  },
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/react/configure/story-layout
    actions: { argTypesRegex: "^on.*" },
    rootAttributesTooltip: true,
  },
};

export default meta;

type Story = StoryObj<typeof CompanyProductByCategory>;

export const Schema: Story = {
  render: (args) => {
    return (
      <>
        <div>
          <div className="flex flex-row  space-x-2 items-center">
            <CompanyProductByCategory {...args} />
          </div>
        </div>
      </>
    );
  },
};

export const EPHeaderData = {
  params: {
    name: "EPHeader",
    componentName: "EPHeader",
    tag: "h1",
  },
  rendering: {
    uid: "{00000000-0000-0000-0000-000000000000}",
    componentName: "EPHeader",
    dataSource: "{00000000-0000-0000-0000-000000000000}",
  },
  fields: {
    data: {
      siteNavigation: {
        logos: {
          results: [
            {
              name: "Corporate Logo",
              title: {
                value: "Corporate Logo",
              },
              link: {
                jsonValue: {
                  value: {
                    href: "/",
                    text: "Home",
                    linktype: "external",
                    url: "/",
                    anchor: "",
                    target: "",
                  },
                },
              },
              image: {
                jsonValue: {
                  value: {
                    src: "http://xmcloudcm.localhost/-/media/project/rai-amsterdam/shared/managed-images/ep/rai-logo.png?h=151&iar=0&w=220&hash=876C497694F0CECA7137B7B99D0382F0",
                    alt: "RAI Amsterdam",
                    width: "220",
                    height: "151",
                  },
                },
              },
            },
            {
              name: "Powered Logo",
              title: {
                value: "Powered By",
              },
              link: {
                jsonValue: {
                  value: {
                    href: "https://www.rai.nl",
                    linktype: "external",
                    url: "https://www.rai.nl",
                    anchor: "",
                    title: "Powered by RAI",
                    target: "_blank",
                  },
                },
              },
              image: {
                jsonValue: {
                  value: {
                    src: "http://xmcloudcm.localhost/-/media/project/rai-amsterdam/shared/managed-images/ep/rai-logo-small.png?h=87&iar=0&w=145&hash=41FFDD465B77491E14A211390763C67F",
                    alt: "Powered By",
                    width: "145",
                    height: "87",
                  },
                },
              },
            },
          ],
        },
        links: {
          results: [
            {
              name: "Profile",
              navigationDropDownStateClosedIconName: {
                value: "FaChevronDown",
              },
              navigationDropDownStateOpenedIconName: {
                value: "FaChevronUp",
              },
              children: {
                results: [
                  {
                    link: {
                      jsonValue: {
                        value: {
                          href: "https://ep.rai.amsterdam",
                          linktype: "external",
                          url: "https://ep.rai.amsterdam",
                          anchor: "",
                          target: "",
                        },
                      },
                    },
                    navigationTitle: {
                      value: "Switch Event",
                    },
                    navigationItemIconName: {
                      value: "fa-light fa-arrows-repeat",
                    },
                    children: {
                      results: [],
                    },
                  },
                ],
              },
            },
          ],
        },
      },
    },
  },
};

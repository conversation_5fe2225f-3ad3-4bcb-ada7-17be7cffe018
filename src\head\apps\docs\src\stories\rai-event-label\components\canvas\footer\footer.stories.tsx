import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { WithSitecoreContextDecorator } from "@uxbee/storybook-mock-sc";
import { RAIFooterComponent } from "rai-event-label";
import { LabelFooterData } from "../../../data/footer.data";

const meta: Meta<typeof RAIFooterComponent.Default> = {
  title: "RAI/Event Label/Canvas/Footer",
  component: RAIFooterComponent.Default,
  tags: ["autodocs", "rai", "ep"],
  argTypes: {},
  decorators: [
    (Story, context) =>
      WithSitecoreContextDecorator(
        () => (
          <div className="min-h-screen bg-[#fafafa]">
            <Story />
          </div>
        ),
        context,
        RAIFooterComponent.Default,
        false
      ),
  ],
};

export default meta;

type Story = StoryObj<typeof RAIFooterComponent.Default>;

export const Default: Story = {
  args: LabelFooterData,
};

﻿Import-Module PowerShellGet
$RepositoryName = 'SitecoreGallery'
$RepositoryUrl = 'https://nuget.sitecore.com/resources/v2'
$SitecoreGallery = Get-PSRepository | Where-Object { $_.SourceLocation -eq $RepositoryUrl }
if (-not $SitecoreGallery) {
    Write-Host "Adding Sitecore PowerShell Gallery..." -ForegroundColor Green
    Unregister-PSRepository -Name $RepositoryName -ErrorAction SilentlyContinue
    Register-PSRepository -Name $RepositoryName -SourceLocation $RepositoryUrl -InstallationPolicy Trusted
    $SitecoreGallery = Get-PSRepository -Name $RepositoryName
}

# Install and Import SitecoreDockerTools
$dockerToolsVersion = "10.2.7"
Remove-Module SitecoreDockerTools -ErrorAction SilentlyContinue
if (-not (Get-InstalledModule -Name SitecoreDockerTools -RequiredVersion $dockerToolsVersion -ErrorAction SilentlyContinue)) {
    Write-Host "Installing SitecoreDockerTools..." -ForegroundColor Green
    Install-Module SitecoreDockerTools -RequiredVersion $dockerToolsVersion -Scope CurrentUser -Repository $SitecoreGallery.Name
}
Write-Host "Importing SitecoreDockerTools..." -ForegroundColor Green
Import-Module SitecoreDockerTools -RequiredVersion $dockerToolsVersion
Write-SitecoreDockerWelcome

dotnet tool restore
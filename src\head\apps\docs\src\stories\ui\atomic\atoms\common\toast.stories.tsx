// import { withActions } from "@storybook/addon-actions/decorator";
import type { Meta, StoryObj } from "@storybook/react";
import { Toast } from "ui";

// More on how to set up stories at: https://storybook.js.org/docs/react/writing-stories/introduction
const meta: Meta<typeof Toast> = {
  title: "ui/Atomic/Atoms/Common/Toast",
  component: Toast,
  tags: ["ui", "molecules", "common"],
  parameters: {},
  argTypes: {},
};

export default meta;

type Story = StoryObj<typeof Toast>;

export const Default: Story = {
  render: (args) => {
    return (
      <div className="w-screen h-screen flex justify-center items-center">
        <Toast {...args} isOpen={true} value="sayonara!" />
      </div>
    );
  },
  args: {
    value: "Item moved successfully",
    icon: "fa-sharp fa-solid fa-badge-check",
  },
};

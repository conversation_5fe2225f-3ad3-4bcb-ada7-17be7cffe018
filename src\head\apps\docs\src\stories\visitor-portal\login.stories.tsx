import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { WithSitecoreContextDecorator } from "@uxbee/storybook-mock-sc/lib/WithSitecoreContextDecorator";
import { LoginComponent } from "visitor-portal";

// More on how to set up stories at: https://storybook.js.org/docs/react/writing-stories/introduction
const meta: Meta<typeof LoginComponent.Default> = {
  title: "RAI/Visitor Portal/Canvas/Login",
  component: LoginComponent.Default,
  tags: ["autodocs", "rai", "ep"],
  argTypes: {},
  args: {
    newAccountUrl: "#",
    newAccountDescription: `
    <div class="text-center text-gray-300">
    It's free and easy. Discover thousands of games to play with millions of new friends.
    <a href="#" class="text-gray-300 hover:text-gray-300">Learn more about Steam</a>
    <div>
    `,
    backgroundUrl: "https://assets-prd.raicore.com/-/media/project/rai-amsterdam/rai/header-home/newhomepagebanner.jpg?rev=1c27181b571448ac905e0bca800fcf57"
  },
  decorators: [
    (Story, context) =>
      WithSitecoreContextDecorator(
        () => (
          <div className="h-[40rem]">
            <Story />
          </div>
        ),
        context,
        LoginComponent.Default,
        false
      ),
  ],
};

export default meta;

type Story = StoryObj<typeof LoginComponent.Default>;

export const Default: Story = {
  args: {
    onLogin: async (username: string, password: string) => {
      console.log("Start login");
      await new Promise<void>((resolve) => {
        setTimeout(() => {
          resolve();
        }, 3000);
      });
      console.log("Success login");
    },
  },
};

export const Fail: Story = {
    args: {
      onLogin: async (username: string, password: string) => {
        console.log("Start login");
        await new Promise<void>((resolve) => {
          setTimeout(() => {
            resolve();
          }, 3000);
        });
        throw "Username or password is incorrect"
      },
    },
  };

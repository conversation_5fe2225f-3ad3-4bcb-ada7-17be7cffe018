import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { Footer, footerMock } from "ui";

// More on how to set up stories at: https://storybook.js.org/docs/react/writing-stories/introduction
const meta: Meta<typeof Footer> = {
  title: "ui/Atomic/Organisms/web/Footer/Footer",
  component: Footer,
  tags: ["autodocs", "ui"],
  args: {
    footer: footerMock
  },
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/react/configure/story-layout
    actions: { argTypesRegex: "^on.*" },
    rootAttributesTooltip: true,
  },
};

export default meta;

type Story = StoryObj<typeof Footer>;

export const Schema: Story = {
  render: (args) => {
    return (
      <>
        <div>
          <div className="flex flex-row  space-x-2 items-center">
            <Footer {...args} />
          </div>
        </div>
      </>
    );
  },
};
// import { withActions } from "@storybook/addon-actions/decorator";
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { InputField, Button, Icon, FileDropZone } from "ui";
import { useEffect, useState } from "react";
import { Label, FileInput } from "flowbite-react";
import { FormProvider, useForm } from "react-hook-form";
import { DropZoneProps } from "company-profile-data/src/types/forms";
// More on how to set up stories at: https://storybook.js.org/docs/react/writing-stories/introduction
const meta: Meta<typeof FileDropZone> = {
  title: "ui/Atomic/Atoms/forms/FileDropZone",
  component: FileDropZone,
  tags: ["ui", "molecules", "common"],
  parameters: {},
  argTypes: {},
};

export default meta;

type Story = StoryObj<typeof FileDropZone>;


export const Default: Story = {
  render: (args) => {
      // dummy, if you want to load data from sitecore
      const obj = [
      //   {
      //   lastModified: 1713513063866,
      //   name: "Dummy-1",
      //   size: 16516490000,
      //   type: "image/png",
      //   webkitRelativePath: '',
      //   imageDataUrl:'https://placehold.co/80x64',
      //   isValid:false,
      // },
      // {
      //   lastModified: 1713513063866,
      //   name: "Dummy-2",
      //   size: 1651649,
      //   type: "image/png",
      //   webkitRelativePath: '',
      //   imageDataUrl:'https://placehold.co/80x64',
      //   isValid:true,
      // },
    ]
    const [files, setFiles] = useState<DropZoneProps[]>(obj);
    const format = [
      {
        title:"Supported file formats .JPG, .JPEG, .PNG"
      },
      {
        title:"12 MB limit."
      },
      {
        title:"3 Image limit."
      },
    ];

    const methods = useForm({});
    const onSubmit = () => {
      console.log(' on submit');
    };
    return (
      <>
      <div className="w-screen h-screen flex items-center justify-center flex-col">
        <div className="w-full max-w-2xl">
        <FormProvider {...methods}>
        <form className="overflow-auto" onSubmit={methods.handleSubmit(onSubmit)} noValidate>
          <FileDropZone uploadedData={files} format={format} setFiles={setFiles} errorMessage={'Error Message'} limitSize={2} limitUpload={3}
            accept={['.jpg', '.jpeg', '.png', '.pdf']} name={"file"} isRequired={false} label={"Upload File"}/>
          </form>
        </FormProvider>
        </div>
      </div>
      </>
    );
  },
};

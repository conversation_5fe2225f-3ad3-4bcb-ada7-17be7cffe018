// import { withActions } from "@storybook/addon-actions/decorator";
import type { Meta, StoryObj } from "@storybook/react";
import { CallToAction, GridContainer, GridItem } from "ui";

// More on how to set up stories at: https://storybook.js.org/docs/react/writing-stories/introduction
const meta: Meta<typeof CallToAction> = {
  title: "ui/Atomic/Atoms/Common/CallToAction",
  component: CallToAction,
  tags: ["ui", "molecules", "common"],
  parameters: {},
  argTypes: {},
};

export default meta;

type Story = StoryObj<typeof CallToAction>;

export const Default: Story = {
  render: (args) => {
    return (
      <div className="w-screen h-56 bg-[#fafafa] bg-no-repeat bg-cover pt-40" style={{ backgroundImage: `url('https://assets-prd.raicore.com/-/media/project/rai-amsterdam/metstrade/metstrade/met/sliders/met_main-header_boat_1900x400.jpg?rev=cfdc2bdf81624001bcfa63ab3d0c0a7e')` }}>
        <GridContainer className="mx-auto justify-center px-8 lg:px-0">
          <GridItem xs="12" lg="3">
            <CallToAction />
          </GridItem>
          <GridItem xs="12" lg="3">
            <CallToAction />
          </GridItem>
          <GridItem xs="12" lg="3">
            <CallToAction />
          </GridItem>
        </GridContainer>
      </div>
    );
  },
  args: {
  },
};

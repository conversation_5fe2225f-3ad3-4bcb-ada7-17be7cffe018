[CmdletBinding()]
param(
    [string]
    $ProjectName,
    [Parameter(ValueFromPipelineByPropertyName)]
    [ValidateSet(
        'test',
        'acceptation',
        'production'
    )]
    [string]$EnvironmentName
    
)
Push-Location $PSScriptRoot

$CommandDir = Get-Location
Push-Location -Path ..
$ContextDir = Get-Location
Pop-Location
. $CommandDir/.powershell/Foundation.Sitecore.XMCloud.ps1

$UserFile = Join-Path $ContextDir "/.sitecore/user.json"

$IsAuthenticated = Confirm-XMCloud-Login -UserFile $UserFile

if($IsAuthenticated) {
    $environmentId = Get-Environment-Id-by-Name -ProjectName $ProjectName -EnvironmentName $EnvironmentName -Force $True
    if($environmentId -And ($EnvironmentName -eq 'test')) {
        # test should be removed and recreated, so there is no clutter left...
        $message = "A test environment was found, it is best practice to delete the environment and recreate it to have a clean environment. Type 'delete test' to confirm the deletion of the test envioronment."
        $confirmation = Read-Host $message
        if ($confirmation -eq 'delete test') {
            Write-Host 'Deleting the test environment.' -ForegroundColor DarkYellow
            Remove-Environment-From-Project -ProjectName $ProjectName -EnvironmentName $EnvironmentName

            # Forcably creates and read the new enviornment id
            $environmentId = Get-Environment-Id-by-Name -ProjectName $ProjectName -EnvironmentName $EnvironmentName -Force $True -AutoCreate $True
        }
    } else {
        $environmentId = Get-Environment-Id-by-Name -ProjectName $ProjectName -EnvironmentName $EnvironmentName -Force $True -AutoCreate $True
    }
    if($environmentId) {
        # Getting the CM url to start the push operations
        $EnvironmentInfo = (dotnet sitecore cloud environment info --environment-id $environmentId --json) | ConvertFrom-Json
        $EnvironmentCMHost = $EnvironmentInfo.host
        if($EnvironmentCMHost) {
            # Validates the user to the environment, so that the content can be deployed. For now a interactive login is still needed.
            # Change this code to use the Command.XMCloud.CLI.Authenticate
            $CMURL = -join("https://", $EnvironmentCMHost)
            $CLIEvironmentName = -join("xmcloud.cli.", $EnvironmentName)
            dotnet sitecore login --environment-name $CLIEvironmentName --authority "https://auth.sitecorecloud.io" --cm $CMURL --audience "https://api.sitecorecloud.io" --client-id "Chi8EwfFnEejksk3Sed9hlalGiM9B2v7" --allow-write true
        }
        dotnet sitecore ser validate --fix
        
        Push-Local-Variables-For-Enviroment -ProjectName $ProjectName -EnvironmentName $EnvironmentName        
        
        dotnet sitecore cloud deployment create --environment-id $environmentId --upload $true --working-dir $ContextDir

        if($EnvironmentCMHost) {
            # Pushed all content that is expected by the code with deploy once
            if($EnvironmentName -eq 'test') {
                # Pushes all the example development content that is related to the application to the environment
                dotnet sitecore ser push -n $CLIEvironmentName -i development.*
            }
        }        
    }
} else {
    Write-Host "User is not authenticated, no actions can be executed" -ForegroundColor Red
}
Pop-Location
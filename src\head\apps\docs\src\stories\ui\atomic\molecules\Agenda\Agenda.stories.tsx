// import { withActions } from "@storybook/addon-actions/decorator";
import type { Meta, StoryObj } from "@storybook/react";
import { Agenda } from "ui";
import { agendaDataHorizontal, agendaDataVertical } from "../../../data/agenda.data";

// More on how to set up stories at: https://storybook.js.org/docs/react/writing-stories/introduction
const meta: Meta<typeof Agenda> = {
  title: "ui/Atomic/Molecules/Agenda/Agenda",
  component: Agenda,
  tags: ["ui", "molecules", "common"],
  parameters: {},
  argTypes: {},
};

export default meta;

type Story = StoryObj<typeof Agenda>;

export const Default: Story = {
  render: (args) => {
    return (
      <div className="w-screen h-screen flex flex-col justify-center bg-neutral-50 items-center">
        <div className="max-w-3xl mx-auto w-full flex justify-center items-center">
          <Agenda agenda={agendaDataHorizontal} language="en">
          </Agenda>
        </div>
        <div className="max-w-3xl mx-auto w-full flex justify-center items-center pt-12">
          <Agenda agenda={agendaDataVertical} language="en">
          </Agenda>
        </div>
      </div>
    );
  },
  args: {
    // value: "E-mailadres",
  },
};

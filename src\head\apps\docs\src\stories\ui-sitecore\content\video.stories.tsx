import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { WithSitecoreContextDecorator } from "@uxbee/storybook-mock-sc";
import { VideoComponent } from "ui-sitecore";
import { VideoData } from "../data/video.data";
import { GridContainer } from "ui";

const meta: Meta<typeof VideoComponent.Default> = {
  title: "ui-sitecore/components/content/Video",
  component: VideoComponent.Default,
  tags: ["autodocs", "rai", "ep"],
  argTypes: {},
  decorators: [
    (Story, context) =>
      WithSitecoreContextDecorator(
        () => (
          <div className="h-[30rem] bg-[#fafafa]">
            <GridContainer>
              <Story />
            </GridContainer>
          </div>
        ),
        context,
        VideoComponent.Default,
        false
      ),
  ],
};

export default meta;

type Story = StoryObj<typeof VideoComponent.Default>;

export const Default: Story = {
  // args: LabelFooterData,
  args: VideoData
};

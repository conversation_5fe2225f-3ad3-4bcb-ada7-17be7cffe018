$DefautGateway = Get-WmiObject -Class Win32_IP4RouteTable |
  Where-Object { $_.destination -eq '0.0.0.0' -and $_.mask -eq '0.0.0.0'} |
  Sort-Object metric1 | Select-Object nexthop, metric1, interfaceindex
$HostDockerInternal = $DefautGateway.NextHop
$file = "C:\Windows\System32\drivers\etc\hosts"
$hostfile = Get-Content $file
$hostfile += "$HostDockerInternal   host.docker.internal"
$hostfile += "$HostDockerInternal   rendering"
Set-Content -Path $file -Value $hostfile -Force

& C:/tools/entrypoints/iis/XmCloudDevelopment.ps1
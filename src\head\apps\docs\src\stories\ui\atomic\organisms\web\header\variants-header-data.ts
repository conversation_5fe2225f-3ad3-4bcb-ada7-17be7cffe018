export const variantsHeaderData = [
    {
      startPaths: {
        targetItems: [
          {
            path: "/sitecore/content/RAI Amsterdam/Blueprints/Blueprint_Label/Home/Card",
          },
          {
            path: "/sitecore/content/RAI Amsterdam/Blueprints/Blueprint_Label/Home/List",
          },
        ],
      },
      variant: {
        jsonValue: {
          value: "",
        },
      },
      showCountdown: {
        boolValue: false,
      },
      countdownDate: {
        dateValue: -62135596800000,
      },
      topbarText: {
        jsonValue: {
          value: "",
        },
      },
      showSearch: {
        boolValue: false,
      },
      showLanguageSwitch: {
        boolValue: false,
      },
      fullHeight: {
        boolValue: false,
      },
      serviceNavigationSplitterIcon: {
        jsonValue: {
          value: "",
        },
      },
      logos: {
        results: [
          {
            name: "Corporate Logo",
            title: {
              value: "Corporate Logo",
            },
            link: {
              jsonValue: {
                value: {
                  text: "",
                  anchor: "",
                  linktype: "internal",
                  class: "",
                  title: "",
                  target: "",
                  querystring: "",
                  id: "{C321D9E1-4BA7-4C73-8209-563A6A34833E}",
                  href: "/",
                },
              },
            },
            image: {
              jsonValue: {
                value: {
                  src: "https://xmcloudcm.localhost/-/media/project/rai-amsterdam-xmc/shared/managed-images/ep/rai-logo-small.png?h=1143&iar=0&w=1894&rev=04fb8232cd804d8b9fe80a3d3fe32782&ttc=63880990775&tt=85246A7ACAFEB650AF9E0A28713D7DBD&hash=E3CFCE261278181D69850C43CB717CEA",
                  alt: "Powered By",
                  width: "1894",
                  height: "1143",
                },
              },
            },
          },
        ],
      },
      links: {
        results: [
          {
            name: "Main Navigation",
            navigationDropDownStateClosedIconName: {
              value: "FaChevronDown",
            },
            navigationDropDownStateOpenedIconName: {
              value: "FaChevronUp",
            },
            children: {
              results: [
                {
                  id: "B40A80F2860649E4BEA0DD4B8CE7FDF2",
                  link: {
                    jsonValue: {
                      value: {
                        href: "http://#",
                        linktype: "external",
                        url: "#",
                        anchor: "",
                        target: "",
                      },
                    },
                  },
                  navigationTitle: {
                    value: "Find Exhibitors",
                  },
                  navigationItemIconName: {
                    value: "",
                  },
                  children: {
                    total: 3,
                    results: [
                      {
                        id: "D08C1876F1394FE2AE15415F92217A8B",
                        navigationTitle: {
                          value: "Exhibitors",
                        },
                        link: {
                          jsonValue: {
                            value: {
                              href: "",
                            },
                          },
                        },
                        icon: {
                          value: "",
                        },
                        children: {
                          results: [
                            {
                              id: "0C3A674570D14DA78896B51CB09C6E32",
                              template: {
                                name: "Navigation Item",
                              },
                              link: {
                                jsonValue: {
                                  value: {
                                    href: "",
                                  },
                                },
                              },
                              navigationTitle: {
                                value: "Exhibitors A-Z",
                              },
                              icon: {
                                value: "",
                              },
                            },
                          ],
                        },
                      },
                      {
                        id: "3619653671514C9495EA7F522704BF67",
                        navigationTitle: {
                          value: "Networking",
                        },
                        link: {
                          jsonValue: {
                            value: {
                              href: "",
                            },
                          },
                        },
                        icon: {
                          value: "",
                        },
                        children: {
                          results: [],
                        },
                      },
                      {
                        id: "2BB2B926AEC2471C974139C81148C4A2",
                        template: {
                          name: "XBE_CTA",
                        },
                        link: {
                          jsonValue: {
                            value: {
                              href: "https://www.intertraffic.com/amsterdam",
                              text: "REGISTER TO OUR NEWSLETTER FOR INTERESTING INDUSTRY INSIGHTS",
                              linktype: "external",
                              url: "https://www.intertraffic.com/amsterdam",
                              anchor: "",
                              target: "",
                            },
                          },
                        },
                        navigationTitle: {
                          jsonValue: {
                            value: "Subcribe to uxbee now",
                          },
                        },
                        icon: {
                          value: "fa-light fa-chevrons-right",
                        },
                      },
                    ],
                  },
                },
                {
                  id: "689EFF38A2E34A648133B1C4FDBBA0FF",
                  link: {
                    jsonValue: {
                      value: {
                        href: "http://#",
                        linktype: "external",
                        url: "#",
                        anchor: "",
                        target: "",
                      },
                    },
                  },
                  navigationTitle: {
                    value: "Visit",
                  },
                  navigationItemIconName: {
                    value: "",
                  },
                  children: {
                    total: 1,
                    results: [
                      {
                        id: "8727F537DA7E44C8960DBB7801353CF8",
                        navigationTitle: {
                          value: "Visitor information",
                        },
                        link: {
                          jsonValue: {
                            value: {
                              href: "",
                            },
                          },
                        },
                        icon: {
                          value: "",
                        },
                        children: {
                          results: [
                            {
                              id: "EB76BF8EDC6B4B9696AC2C1CE786F087",
                              template: {
                                name: "Navigation Item",
                              },
                              link: {
                                jsonValue: {
                                  value: {
                                    href: "",
                                  },
                                },
                              },
                              navigationTitle: {
                                value: "About us",
                              },
                              icon: {
                                value: "fa-regular fa-square-info",
                              },
                            },
                          ],
                        },
                      },
                    ],
                  },
                },
                {
                  id: "9E90B18D25ED4057ACE4EF305299DD83",
                  link: {
                    jsonValue: {
                      value: {
                        href: "http://#",
                        linktype: "external",
                        url: "#",
                        anchor: "",
                        target: "",
                      },
                    },
                  },
                  navigationTitle: {
                    value: "News",
                  },
                  navigationItemIconName: {
                    value: "",
                  },
                  children: {
                    total: 1,
                    results: [
                      {
                        id: "D0D153C3608E48A0BEF978CC5F141384",
                        navigationTitle: {
                          value: "Articles",
                        },
                        link: {
                          jsonValue: {
                            value: {
                              href: "",
                            },
                          },
                        },
                        icon: {
                          value: "",
                        },
                        children: {
                          results: [
                            {
                              id: "F3ABE48BA18041E8B9B92024A34F80AB",
                              template: {
                                name: "Navigation Item",
                              },
                              link: {
                                jsonValue: {
                                  value: {
                                    text: "",
                                    anchor: "",
                                    linktype: "internal",
                                    class: "",
                                    title: "",
                                    target: "",
                                    querystring: "",
                                    id: "{DDA214D3-8A91-40B5-B13B-21BD0825BA4A}",
                                    href: "/news",
                                  },
                                },
                              },
                              navigationTitle: {
                                value: "All news",
                              },
                              icon: {
                                value: "",
                              },
                            },
                          ],
                        },
                      },
                    ],
                  },
                },
                {
                  id: "117E83117A174D5F9B600134095743AF",
                  link: {
                    jsonValue: {
                      value: {
                        href: "http://#",
                        linktype: "external",
                        url: "#",
                        anchor: "",
                        target: "",
                      },
                    },
                  },
                  navigationTitle: {
                    value: "Press and Media",
                  },
                  navigationItemIconName: {
                    value: "",
                  },
                  children: {
                    total: 2,
                    results: [
                      {
                        id: "F8B1AC7EFA604DE8928B48131865748C",
                        navigationTitle: {
                          value: "Press",
                        },
                        link: {
                          jsonValue: {
                            value: {
                              href: "",
                            },
                          },
                        },
                        icon: {
                          value: "",
                        },
                        children: {
                          results: [
                            {
                              id: "6EE7154380F14111A7DE17476893AA00",
                              template: {
                                name: "Navigation Item",
                              },
                              link: {
                                jsonValue: {
                                  value: {
                                    href: "",
                                  },
                                },
                              },
                              navigationTitle: {
                                value: "Press releases",
                              },
                              icon: {
                                value: "",
                              },
                            },
                          ],
                        },
                      },
                      {
                        id: "3AA6C8D42FB5422E9DF30FA11250D130",
                        navigationTitle: {
                          value: "Media",
                        },
                        link: {
                          jsonValue: {
                            value: {
                              href: "",
                            },
                          },
                        },
                        icon: {
                          value: "",
                        },
                        children: {
                          results: [
                            {
                              id: "6146B739621C433EB808246B5B5FE7C2",
                              template: {
                                name: "Navigation Item",
                              },
                              link: {
                                jsonValue: {
                                  value: {
                                    href: "",
                                  },
                                },
                              },
                              navigationTitle: {
                                value: "Logos",
                              },
                              icon: {
                                value: "",
                              },
                            },
                          ],
                        },
                      },
                    ],
                  },
                },
                {
                  id: "DB6D8D022A904670A42CF61B0D0B17C3",
                  link: {
                    jsonValue: {
                      value: {
                        href: "http://#",
                        linktype: "external",
                        url: "#",
                        anchor: "",
                        target: "",
                      },
                    },
                  },
                  navigationTitle: {
                    value: "FAQ",
                  },
                  navigationItemIconName: {
                    value: "",
                  },
                  children: {
                    total: 0,
                    results: [],
                  },
                },
              ],
            },
          },
        ],
      },
      subnavigation: {
        results: [
          {
            id: "424CE708EEA2499E9D6411E249AA0A11",
            name: "Spinoffs",
            children: {
              results: [
                {
                  id: "DC56DFEA37D342CFA106C226EF7C80C8",
                  displayName: "Amsterdam",
                  link: {
                    jsonValue: {
                      value: {
                        text: "Amsterdam",
                        anchor: "",
                        linktype: "internal",
                        class: "",
                        title: "",
                        target: "",
                        querystring: "",
                        id: "{C321D9E1-4BA7-4C73-8209-563A6A34833E}",
                        href: "/",
                      },
                    },
                  },
                  icon: {
                    value: "",
                  },
                },
                {
                  id: "6876904E60C643129027306CA5724EA9",
                  displayName: "China",
                  link: {
                    jsonValue: {
                      value: {
                        text: "China",
                        anchor: "",
                        linktype: "internal",
                        class: "",
                        title: "",
                        target: "",
                        querystring: "",
                        id: "{C321D9E1-4BA7-4C73-8209-563A6A34833E}",
                        href: "/",
                      },
                    },
                  },
                  icon: {
                    value: "",
                  },
                },
              ],
            },
          },
          {
            id: "0927B131DDEC4239B1DA6E45DC8DA920",
            name: "Service Navigation",
            children: {
              results: [
                {
                  id: "D16084B52A0D4B30971B449B2D86DFAD",
                  displayName: "Contact",
                  link: {
                    jsonValue: {
                      value: {
                        href: "",
                      },
                    },
                  },
                  icon: {
                    value: "fa-solid fa-info",
                  },
                },
              ],
            },
          },
          {
            id: "2C09CCC6846848C79D8C67FA6BCC4F35",
            name: "Actions",
            children: {
              results: [
                {
                  id: "B11D8E52FA3E4DB4B1384BC3E90F2B6E",
                  displayName: "Search",
                  link: {
                    jsonValue: {
                      value: {
                        text: "",
                        anchor: "",
                        linktype: "internal",
                        class: "",
                        title: "",
                        target: "|Custom",
                        querystring: "",
                        id: "{DA2C2323-EC0A-4257-835A-4ED77F3A56BE}",
                        href: "/search",
                      },
                    },
                  },
                  icon: {
                    value: "fa-solid fa-magnifying-glass",
                  },
                },
                {
                  id: "066714555F474799A2091A84F583354E",
                  displayName: "SIGN UP FOR OUR NEWS LETTER",
                  link: {
                    jsonValue: {
                      value: {
                        text: "",
                        anchor: "",
                        linktype: "internal",
                        class: "",
                        title: "",
                        target: "|Custom",
                        querystring: "",
                        id: "{84E0C954-167E-4ABA-9A21-0C7404E49C42}",
                        href: "/newsletter",
                      },
                    },
                  },
                  icon: {
                    value: "fa-sharp fa-light fa-envelope",
                  },
                },
              ],
            },
          },
        ],
      },
      sidebarLinks: {
        results: [
          {
            name: "Sidebar",
            navigationDropDownStateClosedIconName: {
              value: "fa-light fa-angle-down",
            },
            navigationDropDownStateOpenedIconName: {
              value: "fa-light fa-angle-up",
            },
            children: {
              results: [
                {
                  link: {
                    jsonValue: {
                      value: {
                        text: "",
                        anchor: "",
                        linktype: "internal",
                        class: "",
                        title: "",
                        target: "",
                        querystring: "",
                        id: "{800DE29F-B1D5-465C-8D3F-0FC452F8A28B}",
                        href: "/account/dashboard",
                      },
                    },
                  },
                  navigationTitle: {
                    value: "Dashboard",
                  },
                  navigationItemIconName: {
                    value: "fa-light fa-house",
                  },
                  children: {
                    results: [],
                  },
                },
                {
                  link: {
                    jsonValue: {
                      value: {
                        text: "",
                        anchor: "",
                        linktype: "internal",
                        class: "",
                        title: "",
                        target: "",
                        querystring: "",
                        id: "{D7F1DFF9-9B91-42ED-91C5-4FBBBC586E7F}",
                        href: "/account/my-account",
                      },
                    },
                  },
                  navigationTitle: {
                    value: "My Account",
                  },
                  navigationItemIconName: {
                    value: "fa-light fa-user",
                  },
                  children: {
                    results: [],
                  },
                },
                {
                  link: {
                    jsonValue: {
                      value: {
                        text: "Exhibitors",
                        anchor: "",
                        linktype: "internal",
                        class: "",
                        title: "",
                        target: "",
                        querystring: "",
                        id: "{FE600DE5-5874-452F-9243-687360627BA5}",
                        href: "/account/my-ticket",
                      },
                    },
                  },
                  navigationTitle: {
                    value: "My Tickets",
                  },
                  navigationItemIconName: {
                    value: "fa-light fa-ticket",
                  },
                  children: {
                    results: [],
                  },
                },
                {
                  link: {
                    jsonValue: {
                      value: {
                        href: "#test",
                        text: "test",
                        linktype: "anchor",
                        url: "test",
                        anchor: "test",
                        title: "",
                        class: "",
                      },
                    },
                  },
                  navigationTitle: {
                    value: "Event Information",
                  },
                  navigationItemIconName: {
                    value: "fa-sharp fa-light fa-font-awesome",
                  },
                  children: {
                    results: [
                      {
                        link: {
                          jsonValue: {
                            value: {
                              href: "",
                              text: "",
                              anchor: "",
                              linktype: "internal",
                              class: "",
                              title: "",
                              target: "",
                              querystring: "",
                              id: "{11300574-051B-4536-AEBF-7A10F4C2D813}",
                            },
                          },
                        },
                        navigationTitle: {
                          value: "About",
                        },
                      },
                      {
                        link: {
                          jsonValue: {
                            value: {
                              text: "",
                              anchor: "",
                              linktype: "internal",
                              class: "",
                              title: "",
                              target: "",
                              querystring: "",
                              id: "{2CB8C2F5-503A-4528-B236-A8A81A840009}",
                              href: "/agenda",
                            },
                          },
                        },
                        navigationTitle: {
                          value: "Floor Plan",
                        },
                      },
                    ],
                  },
                },
                {
                  link: {
                    jsonValue: {
                      value: {
                        text: "",
                        anchor: "",
                        linktype: "internal",
                        class: "",
                        title: "",
                        target: "",
                        querystring: "",
                        id: "{14B4E8CA-324B-4183-A249-C93591C7C021}",
                        href: "/account/my-agenda",
                      },
                    },
                  },
                  navigationTitle: {
                    value: "My Agenda",
                  },
                  navigationItemIconName: {
                    value: "fa-light fa-calendar-days",
                  },
                  children: {
                    results: [],
                  },
                },
                {
                  link: {
                    jsonValue: {
                      value: {
                        text: "",
                        anchor: "",
                        linktype: "internal",
                        class: "",
                        title: "",
                        target: "",
                        querystring: "",
                        id: "{E38779E3-2D59-44E4-84F0-C1BE16DF6193}",
                        href: "/account/my-articles",
                      },
                    },
                  },
                  navigationTitle: {
                    value: "My Articles",
                  },
                  navigationItemIconName: {
                    value: "fa-light fa-newspaper",
                  },
                  children: {
                    results: [],
                  },
                },
                {
                  link: {
                    jsonValue: {
                      value: {
                        text: "",
                        anchor: "",
                        linktype: "internal",
                        class: "",
                        title: "",
                        target: "",
                        querystring: "",
                        id: "{8D1E854C-9D08-45C2-BDF4-46C5FAF01836}",
                        href: "/account/my-contacts",
                      },
                    },
                  },
                  navigationTitle: {
                    value: "My Contacts",
                  },
                  navigationItemIconName: {
                    value: "fa-light fa-address-book",
                  },
                  children: {
                    results: [],
                  },
                },
                {
                  link: {
                    jsonValue: {
                      value: {
                        text: "",
                        anchor: "",
                        linktype: "internal",
                        class: "",
                        title: "",
                        target: "",
                        querystring: "",
                        id: "{4058C23C-DD7A-443F-A0D2-48A491789FD2}",
                        href: "/account/my-parking",
                      },
                    },
                  },
                  navigationTitle: {
                    value: "My Parking",
                  },
                  navigationItemIconName: {
                    value: "fa-light fa-square-parking",
                  },
                  children: {
                    results: [],
                  },
                },
                {
                  link: {
                    jsonValue: {
                      value: {
                        text: "",
                        anchor: "",
                        linktype: "internal",
                        class: "",
                        title: "",
                        target: "",
                        querystring: "",
                        id: "{7188D835-7E39-49C9-B1B9-D8B49307F92A}",
                        href: "/account/my-accommodation",
                      },
                    },
                  },
                  navigationTitle: {
                    value: "My Accommodation",
                  },
                  navigationItemIconName: {
                    value: "fa-light fa-bell-concierge",
                  },
                  children: {
                    results: [],
                  },
                },
                {
                  link: {
                    jsonValue: {
                      value: {
                        text: "",
                        anchor: "",
                        linktype: "internal",
                        class: "",
                        title: "",
                        target: "",
                        querystring: "",
                        id: "{CFAAF93F-8B1B-49A8-86BD-27BB6D05487E}",
                        href: "/account/my-downloads",
                      },
                    },
                  },
                  navigationTitle: {
                    value: "My Downloads",
                  },
                  navigationItemIconName: {
                    value: "fa-light fa-download",
                  },
                  children: {
                    results: [],
                  },
                },
              ],
            },
          },
        ],
      },
    },
  ];
  
import type { <PERSON>a, StoryObj } from "@storybook/react";
import { CompanyDownload } from "company-profile";
import { CompanyProfileModelMock } from 'company-profile-data';

// More on how to set up stories at: https://storybook.js.org/docs/react/writing-stories/introduction
const meta: Meta<typeof CompanyDownload> = {
  title: "RAI/Company Profile/CompanyDownload",
  component: CompanyDownload,
  tags: ["autodocs", "company-profile"],
  args: {
    company: CompanyProfileModelMock
  },
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/react/configure/story-layout
    actions: { argTypesRegex: "^on.*" },
    rootAttributesTooltip: true,
  },
};

export default meta;

type Story = StoryObj<typeof CompanyDownload>;

export const Schema: Story = {
  render: (args) => {
    return (
      <>
        <div>
          <div className="flex flex-row  space-x-2 items-center">
            <CompanyDownload {...args} />
          </div>
        </div>
      </>
    );
  },
};
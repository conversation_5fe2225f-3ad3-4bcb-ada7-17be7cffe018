[CmdletBinding()]
param()

Push-Location $PSScriptRoot
# Define the script context
$CommandDir = Get-Location
Push-Location -Path ..
$ContextDir = Get-Location
Pop-Location

. $CommandDir/.powershell/Foundation.Git.ps1
. $CommandDir/.powershell/Foundation.Env.ps1

$TurboPath = Join-Path $ContextDir "/src/head/"
$LocalEnvFile = Join-Path $TurboPath ".env.local"
$GlobalEnvFile = Join-Path $ContextDir ".env"
$ContextEnvFile = $GlobalEnvFile
$CanUseVTST = $false
if ($IsWindows -or $ENV:OS) {
    $CanUseVTST = $true
}

# First determine where the local env file is located
if(Test-Path -PathType Leaf -Path $LocalEnvFile) {
    $ContextEnvFile = $LocalEnvFile
}

if(Test-Path -PathType Leaf -Path $ContextEnvFile) {
    Write-Host "Getting the environment variables from the environment file located at $ContextEnvFile" -ForegroundColor DarkYellow
    if(!($CanUseVTST)) {
        $UxbeeRegistryUserName = Get-EnvFileVariable "XMV_UXBEE_REGISTRY_USERNAME" -Path $ContextEnvFile
        $UxbeeRegistryEmail = Get-EnvFileVariable "XMV_UXBEE_REGISTRY_EMAIL" -Path $ContextEnvFile
        $UxbeeRegistryPatKey = Get-EnvFileVariable "XMV_UXBEE_REGISTRY_PATKEY" -Path $ContextEnvFile
    }

    Add-Scoped-Repository -ContextFolder $TurboPath -UserName $UxbeeRegistryUserName -Email $UxbeeRegistryEmail -PatKey $UxbeeRegistryPatKey -EnvFileLocation $ContextEnvFile
    Read-Local-Environment-Variables-From-File -EnvFilePath $GlobalEnvFile
} else {
    Write-Host "A valid environment file can not be determined, please ensure that you have a environment file." -ForegroundColor red
}

Pop-Location
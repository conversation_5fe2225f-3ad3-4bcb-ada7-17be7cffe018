// import { withActions } from "@storybook/addon-actions/decorator";
import type { Meta, StoryObj } from "@storybook/react";
import { Tooltip } from "ui";

// More on how to set up stories at: https://storybook.js.org/docs/react/writing-stories/introduction
const meta: Meta<typeof Tooltip> = {
  title: "ui/Atomic/Atoms/Forms/Tooltip",
  component: Tooltip,
  tags: ["ui", "molecules", "forms"],
  parameters: {},
  argTypes: {
  },
};

export default meta;

type Story = StoryObj<typeof Tooltip>;

export const Default: Story = {
  render: (args) => {
    return (
        <div className="w-screen h-screen flex justify-center items-center">
            <Tooltip content={args.content}/>
        </div>
    );
  },
  args: {
    content: "E-mailadres",
  },
};

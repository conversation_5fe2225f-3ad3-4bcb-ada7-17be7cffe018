import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { WithSitecoreContextDecorator } from "@uxbee/storybook-mock-sc/lib/WithSitecoreContextDecorator";
import { MyAccountComponent } from "visitor-portal";

// More on how to set up stories at: https://storybook.js.org/docs/react/writing-stories/introduction
const meta: Meta<typeof MyAccountComponent.Default> = {
  title: "RAI/Visitor Portal/Canvas/MyAccount",
  component: MyAccountComponent.Default,
  tags: ["autodocs", "rai", "ep"],
  argTypes: {},
  args: {
  },
  decorators: [
    (Story, context) =>
      WithSitecoreContextDecorator(
        () => (
          <div className="h-screen">
            <Story />
          </div>
        ),
        context,
        MyAccountComponent.Default,
        false
      ),
  ],
};

export default meta;

type Story = StoryObj<typeof MyAccountComponent.Default>;

export const Default: Story = {
  args: {
    MyAccountTitle: { value: "My Account Information" },
    EmailSubscriptionTitle: { value: "Email Subscription" },
    RemoveAccountTitle: { value: "Remove Account" },
    RemoveAccountDescription: { value: "I no longer use my account. By deleting this account all data will be removed." },
    data: MyAccountComponent.MyAccountModelDataDefault,
  },
};
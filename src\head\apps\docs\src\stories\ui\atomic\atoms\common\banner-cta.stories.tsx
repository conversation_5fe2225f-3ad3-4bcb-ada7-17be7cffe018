// import { withActions } from "@storybook/addon-actions/decorator";
import type { Meta, StoryObj } from "@storybook/react";
import { BannerCTA } from "ui";

// More on how to set up stories at: https://storybook.js.org/docs/react/writing-stories/introduction
const meta: Meta<typeof BannerCTA> = {
  title: "ui/Atomic/Atoms/Common/BannerCTA",
  component: BannerCTA,
  tags: ["ui", "molecules", "common"],
  parameters: {},
  argTypes: {},
};

export default meta;

type Story = StoryObj<typeof BannerCTA>;

export const Default: Story = {
  render: (args) => {
    return (
      <BannerCTA />
    );
  },
  args: {
  },
};

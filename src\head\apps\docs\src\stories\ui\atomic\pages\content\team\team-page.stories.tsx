import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { TeamPage } from "ui";
const meta: Meta<typeof TeamPage> = {
  title: "ui/Atomic/Pages/Content/Team/TeamPage",
  component: TeamPage,
  tags: ["autodocs", "ui", "molecules", "home-page"],
  parameters: {},
  argTypes: {},
};
export default meta;
type Story = StoryObj<typeof TeamPage>;
export const Schema: Story = {
  render: (args) => {
    return (
      <div className="h-[40rem]">
        <TeamPage {...args} />
      </div>
    );
  },
  args: {},
};

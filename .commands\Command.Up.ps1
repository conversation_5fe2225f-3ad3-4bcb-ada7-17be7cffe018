[CmdletBinding(DefaultParameterSetName = "no-arguments")]
param(
    [switch]
    $Restart
)

Push-Location $PSScriptRoot
# Define the script context
Push-Location -Path ..
$ContextDir = Get-Location
Pop-Location

. $PSScriptRoot/.powershell/Foundation.Docker.ps1

$ApplicationName = "web"
Write-Host "=============== Starting up Develop Environment =====================" -ForegroundColor Green

Push-Location $ContextDir
try {
    ./Project.Init.ps1
    if($Restart) {
        $RunningContainers = Find-Running-Containers
        if($RunningContainers -ge 2) {
            Write-Host "Your docker is currently running, application can not be installed!" -ForegroundColor DarkYellow
            $message = "Do you want to bring down your docker environment?"
            $confirmation = Read-Host $message
            if ($confirmation -eq 'y') {
                Push-Location $ContextDir
                Write-Host "- bring down docker environment" -ForegroundColor Yellow
                docker compose down
                Write-Host "- reset docker" -ForegroundColor Yellow
                .\execute.ps1 docker.fix
                Pop-Location
            }
        }
    }
    $RunningContainers = Find-Running-Containers
    if($RunningContainers -lt 2) {
        Write-Host "- bring docker environment up" -ForegroundColor Yellow
        docker network rm xmcloud-foundation_default
        docker compose up -d
    }

    Write-Host "- Start nginx" -ForegroundColor Yellow
    .\execute.ps1 Nginx
    if($Restart) {
        Write-Host "- Authenticating Sitecore" -ForegroundColor Green
        .\execute.ps1 XMCloud.CLI.Authenticate
    }

    $EnvPath = Join-Path $ContextDir ".env"
    try {
        $ApplicationName = Get-EnvFileVariable "DEVELOPMENT_START_APP" -Path $EnvPath
    } catch {
        $ApplicationName = "web"
    }

    Write-Host "- Starting frontend servers" -ForegroundColor Green
    $FilteredApplicationName = -join($ApplicationName, "...")
    $TabTitle = -join($ApplicationName, " + Design System")
    wt new-tab --title $TabTitle --suppressApplicationTitle --tabColor '#f59218' -d "./src/head" powershell.exe -NoExit -c turbo dev --filter $FilteredApplicationName --filter docs... --concurrency 22`; 

    Write-Host "- Development environment Started" -ForegroundColor Green
    Write-Host "- Opening CMS, design and portal in webbrowser" -ForegroundColor Green


    

    # try {
    #     Push-Location -Path .\src\head       
    #     turbo dev
    # }
    # finally {
    #     Pop-Location    
    # }
    

#     $urls = @("https://xmcloudcm.localhost", "https://design.xmcloud.local", "https://portal.xmcloud.local")

#     foreach ($url in $urls) {
#         Start-Process $url
#     }
}
finally {
    
}
Pop-Location
Pop-Location


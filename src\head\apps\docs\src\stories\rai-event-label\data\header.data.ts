export const LabelHeaderData = {
  params: {
    name: "Header",
    componentName: "Header",
    tag: "h1",
  },
  rendering: {
    uid: "{00000000-0000-0000-0000-000000000000}",
    componentName: "Header",
    dataSource: "{00000000-0000-0000-0000-000000000000}",
  },
  fields: {
    data: {
      siteNavigation: {
        logos: {
          results: [
            {
              name: "Corporate Logo",
              title: {
                value: "Corporate Logo",
              },
              link: {
                jsonValue: {
                  value: {
                    href: "",
                  },
                },
              },
              image: {
                jsonValue: {
                  value: {
                    src: "https://xmcloudcm.localhost/-/media/project/rai-amsterdam-xmc/shared/managed-images/ep/rai-logo-small.png?h=1143&iar=0&w=1894&rev=e6ddd877078744d9bd3f567d33b3b960&hash=F61B09EDFDE41E49C8CA3C3130FBAF04",
                    alt: "Powered By",
                    width: "1894",
                    height: "1143",
                  },
                },
              },
            },
          ],
        },
        links: {
          results: [
            {
              name: "Main Navigation",
              navigationDropDownStateClosedIconName: {
                value: "FaChevronDown",
              },
              navigationDropDownStateOpenedIconName: {
                value: "FaChevronUp",
              },
              children: {
                results: [
                  {
                    id: "CED6425372AF438880BB22F4E34A0D73",
                    link: {
                      jsonValue: {
                        value: {
                          href: "http://#",
                          linktype: "external",
                          url: "#",
                          anchor: "",
                          target: "",
                        },
                      },
                    },
                    navigationTitle: {
                      value: "FAQ",
                    },
                    navigationItemIconName: {
                      value: "",
                    },
                    children: {
                      total: 0,
                      results: [],
                    },
                  },
                  {
                    id: "E756E76487DC4C5C89D22AD4B5C4F6DB",
                    link: {
                      jsonValue: {
                        value: {
                          href: "http://#",
                          linktype: "external",
                          url: "#",
                          anchor: "",
                          target: "",
                        },
                      },
                    },
                    navigationTitle: {
                      value: "Find Exhibitors",
                    },
                    navigationItemIconName: {
                      value: "fa-solid fa-location",
                    },
                    children: {
                      total: 3,
                      results: [
                        {
                          id: "0CEACE8236024071B2157F427605CFCD",
                          navigationTitle: {
                            value: "Exhibitors",
                          },
                          children: {
                            results: [
                              {
                                id: "50133F7D480640568BB86A8B69180D55",
                                template: {
                                  name: "Navigation Item",
                                },
                                link: {
                                  jsonValue: {
                                    value: {
                                      href: "",
                                    },
                                  },
                                },
                                navigationTitle: {
                                  value: "Exhibitors A-Z",
                                },
                                icon: {
                                  value: "fa-regular fa-arrow-up-a-z",
                                },
                              },
                            ],
                          },
                        },
                        {
                          id: "DC7C7698E09E423EBB2FA0CF36F816C6",
                          navigationTitle: {
                            value: "Networking",
                          },
                          children: {
                            results: [],
                          },
                        },
                        {
                          id: "2AC83A13DFD64346A6B63CD97475BF5C",
                          template: {
                            name: "XBE_CTA",
                          },
                          link: {
                            jsonValue: {
                              value: {
                                text: "",
                                anchor: "",
                                linktype: "internal",
                                class: "",
                                title: "",
                                target: "",
                                querystring: "",
                                id: "{84E0C954-167E-4ABA-9A21-0C7404E49C42}",
                                href: "/newsletter",
                              },
                            },
                          },
                          navigationTitle: {
                            jsonValue: {
                              value: "Subscribe",
                            },
                          },
                          icon: {
                            value: "fa-solid fa-angle-right",
                          },
                        },
                      ],
                    },
                  },
                  {
                    id: "3176C1E3697844B9A097FF83EA780EE7",
                    link: {
                      jsonValue: {
                        value: {
                          href: "http://#",
                          linktype: "external",
                          url: "#",
                          anchor: "",
                          target: "",
                        },
                      },
                    },
                    navigationTitle: {
                      value: "Visit",
                    },
                    navigationItemIconName: {
                      value: "",
                    },
                    children: {
                      total: 1,
                      results: [
                        {
                          id: "8702E39FA917463BBA107E3C8BAB9959",
                          navigationTitle: {
                            value: "Visitor information",
                          },
                          children: {
                            results: [
                              {
                                id: "1766D16C413242B0A827C61DB109E8B1",
                                template: {
                                  name: "Navigation Item",
                                },
                                link: {
                                  jsonValue: {
                                    value: {
                                      href: "",
                                    },
                                  },
                                },
                                navigationTitle: {
                                  value: "About us",
                                },
                                icon: {
                                  value: "",
                                },
                              },
                            ],
                          },
                        },
                      ],
                    },
                  },
                  {
                    id: "1B4986A1763746069D2D94294E1A1BB9",
                    link: {
                      jsonValue: {
                        value: {
                          href: "http://#",
                          linktype: "external",
                          url: "#",
                          anchor: "",
                          target: "",
                        },
                      },
                    },
                    navigationTitle: {
                      value: "News",
                    },
                    navigationItemIconName: {
                      value: "",
                    },
                    children: {
                      total: 1,
                      results: [
                        {
                          id: "35238F6F05374B088E94FA21398FD159",
                          navigationTitle: {
                            value: "Articles",
                          },
                          children: {
                            results: [
                              {
                                id: "77C1CB10989844EEB8FD66F0534617A8",
                                template: {
                                  name: "Navigation Item",
                                },
                                link: {
                                  jsonValue: {
                                    value: {
                                      href: "",
                                    },
                                  },
                                },
                                navigationTitle: {
                                  value: "All news",
                                },
                                icon: {
                                  value: "",
                                },
                              },
                            ],
                          },
                        },
                      ],
                    },
                  },
                  {
                    id: "3CD2CEF1D27641D7BB37FB36DC5900C1",
                    link: {
                      jsonValue: {
                        value: {
                          href: "http://#",
                          linktype: "external",
                          url: "#",
                          anchor: "",
                          target: "",
                        },
                      },
                    },
                    navigationTitle: {
                      value: "Press and Media",
                    },
                    navigationItemIconName: {
                      value: "",
                    },
                    children: {
                      total: 2,
                      results: [
                        {
                          id: "B3D0CD46CA4F49BE93CA7BE14D3057A4",
                          navigationTitle: {
                            value: "Press",
                          },
                          children: {
                            results: [
                              {
                                id: "272600701565414AA154FD55E302F48E",
                                template: {
                                  name: "Navigation Item",
                                },
                                link: {
                                  jsonValue: {
                                    value: {
                                      href: "",
                                    },
                                  },
                                },
                                navigationTitle: {
                                  value: "Press releases",
                                },
                                icon: {
                                  value: "",
                                },
                              },
                            ],
                          },
                        },
                        {
                          id: "7CB5CE7CD60D42A4B7557B5BAEC867C2",
                          navigationTitle: {
                            value: "Media",
                          },
                          children: {
                            results: [
                              {
                                id: "6B4C06B5A81B48D197BD7DAD370C64E8",
                                template: {
                                  name: "Navigation Item",
                                },
                                link: {
                                  jsonValue: {
                                    value: {
                                      href: "",
                                    },
                                  },
                                },
                                navigationTitle: {
                                  value: "Logos",
                                },
                                icon: {
                                  value: "",
                                },
                              },
                            ],
                          },
                        },
                      ],
                    },
                  },
                ],
              },
            },
          ],
        },
        subnavigation: {
          results: [
            {
              id: "FC324C7BE02B4A439585B108DE25E62D",
              name: "Service Navigation",
              children: {
                results: [
                  {
                    id: "5AF582185F0D41408D3DE8D1519D2771",
                    displayName: "Contact",
                    link: {
                      jsonValue: {
                        value: {
                          href: "",
                        },
                      },
                    },
                    icon: {
                      value: "fa-solid fa-info",
                    },
                  },
                  {
                    id: "F0AC2244C28944A389FAFC3C9D61700A",
                    displayName: "Search",
                    link: {
                      jsonValue: {
                        value: {
                          href: "",
                        },
                      },
                    },
                    icon: {
                      value: "fa-solid fa-magnifying-glass",
                    },
                  },
                ],
              },
            },
            {
              id: "FE8BC95FBF69479DB2A55A04AB6045B8",
              name: "Actions",
              children: {
                results: [
                  {
                    id: "3616652DE8964337B9A574955B164AE3",
                    displayName: "SIGN UP FOR OUR NEWS LETTER",
                    link: {
                      jsonValue: {
                        value: {
                          text: "",
                          anchor: "",
                          linktype: "internal",
                          class: "",
                          title: "",
                          target: "|Custom",
                          querystring: "",
                          id: "{84E0C954-167E-4ABA-9A21-0C7404E49C42}",
                          href: "/newsletter",
                        },
                      },
                    },
                    icon: {
                      value: "fa-sharp fa-light fa-envelope",
                    },
                  },
                ],
              },
            },
          ],
        },
      },
    },
  },
};

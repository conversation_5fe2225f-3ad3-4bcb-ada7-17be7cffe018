import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { LanguageSwitcher } from "ui";

// More on how to set up stories at: https://storybook.js.org/docs/react/writing-stories/introduction
const meta: Meta<typeof LanguageSwitcher> = {
    title: "ui/Atomic/Atoms/Common/LanguageSwitcher",
    component: LanguageSwitcher,
    tags: ["autodocs", "ui", "atoms"],
    parameters: {
    },
    argTypes: {
    },
};

export default meta;

type Story = StoryObj<typeof LanguageSwitcher>;

export const Default: Story = {
    render: (args) => {
        return (
            <>
                <div className="bg-brand-primary-1">
                    <LanguageSwitcher {...args} />
                </div>
            </>
        );
    }, args: {
        textSizeClass: 'text-service-menu',
        currentLanguageCode: "nl",
        languages: [
            {
                code: "en",
                url: "/en",
                label: "English"
            },
            {
                code: "nl",
                url: "/",
                label: "Nederland"
            }
        ]
    },
};

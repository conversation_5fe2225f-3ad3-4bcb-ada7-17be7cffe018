import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { TasksOverview } from "ui";
import { tasksInfoData } from "../../../data/tasks-info.data";
const meta: Meta<typeof TasksOverview> = {
  title: "ui/Atomic/Molecules/TasksOverview/TasksOverview",
  component: TasksOverview,
  tags: ["autodocs", "ui", "molecules", "tasks-overview"],
  parameters: {},
  argTypes: {},
};
export default meta;
type Story = StoryObj<typeof TasksOverview>;
export const Schema: Story = {
  render: (args) => {
    return (
      <div className="px-4 py-6 lg:px-32 bg-neutral-50">
        <div className="grid gap-x-3 lg:grid-cols-6">
          <div className="lg:col-span-6 ">
            <TasksOverview {...args} />
          </div>
        </div>
      </div>
    );
  },
  args: {
    tasks: tasksInfoData,
  },
};

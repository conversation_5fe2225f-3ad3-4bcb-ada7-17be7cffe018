import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { WithSitecoreContextDecorator } from "@uxbee/storybook-mock-sc";
import { Speaker, SpeakerDetailInternal } from "programs";
import { SessionMockData } from "../data/session-mock-data";
import { SpeakerMockData } from "../data/speaker-mock-data";


const meta: Meta<typeof SpeakerDetailInternal> = {
    title: "RAI/Programs/SpeakerDetail",
    component: SpeakerDetailInternal,
    tags: ["autodocs", "rai", "ep"],
    argTypes: {},
    decorators: [
        (Story, context) =>
            WithSitecoreContextDecorator(
                () => (
                    <div className="min-h-screen bg-[#fafafa]">
                        <Story />
                    </div>
                ),
                context,
                SpeakerDetailInternal,
                false
            ),
    ],
};

export default meta;

type Story = StoryObj<typeof SpeakerDetailInternal>;

export const Default: Story = {
    args: {
        speaker: SpeakerMockData[0],
        sessions: SessionMockData.slice(0, 3),
        language: "en",
        buildSessionDetailUrl: () => 'session_detail',
        buildSpeakerDetailUrl: () => 'speaker_detail',
        buildCalendarIcsFileUrl: () => '/session_ics',
        texts: {
            seeLess: 'See less',
            seeMore: 'See more',
            topicsHeader: 'Topics:',
            sessionSeeMore: 'See more'
        },
        timezoneOffset: 0
    },
};

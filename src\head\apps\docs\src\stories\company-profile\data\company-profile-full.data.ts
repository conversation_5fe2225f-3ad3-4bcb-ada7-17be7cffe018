import { CompanyProfileModelMock } from "company-profile-data"

export const CompanyProfileFullDefaultData1 = {
  context: {
      site: {
          name: "Blueprint_Company"
      },
      event: {
          name: "Blueprint",
          id: "21",
          registrationStartDate: "20231213T084900Z",
          registrationEndDate: "20250228T084900Z",
          registrationBaseUrl: "https://rai.nl/registration?account="
      },
      baseUrl: "https://www.rai.local",
      pagePath: "/sitecore/content/RAI Amsterdam/Blueprints/Blueprint_Company/Home",
      favicon: "/-/media/project/rai-amsterdam-xmc/blueprints/blueprint_company/blueprint/rai-favicon.png"
  },
  params: {
    name: "CompanyProfileFull",
    componentName: "CompanyProfileFull",
    DatasourceId: "da6291db-a157-40e8-abb5-288179f77680"
  },
  rendering: {
    uid: "{********-0000-0000-0000-************}",
    componentName: "CompanyProfileFull",
    dataSource: "{********-0000-0000-0000-************}",
  },
  company: CompanyProfileModelMock,
  fields: {
  },
};

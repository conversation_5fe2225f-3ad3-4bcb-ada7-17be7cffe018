export const ContactFormData = {
    params: {
        name: "EPContactForm",
        componentName: "EPContactForm",
        tag: "h1",
        GridParameters: "col-span-12 xl:col-span-6 lg:col-span-6"
    },
    rendering: {
        uid: "{00000000-0000-0000-0000-000000000000}",
        componentName: "EPContactForm",
        dataSource: "{00000000-0000-0000-0000-000000000000}",
    },
    fields: {
        SendEmailTo: {
            value: "<EMAIL>"
        },
        NameField: {
            value: "John Doe"
        },
        EmailField: {
            value: "<EMAIL>"
        },
        CompanyField: {
            value: "Acme Corporation"
        },
        MessageField: {
            value: "Hello, I'm interested in your products."
        },
        SubmitButton: {
            value: "Submit"
        },
        SuccesMessage: {
            value: "Your message has been sent successfully."
        },
        ErrorMessage: {
            value: "An error occurred while sending your message. Please try again later."
        }
    }
}
export const CallToActionDefaultData1 = {
  params: {
    name: "CallToAction",
    componentName: "CallToAction",
    DatasourceId: "da6291db-a157-40e8-abb5-288179f77680"
  },
  rendering: {
    uid: "{00000000-0000-0000-0000-000000000000}",
    componentName: "ContentBlock",
    dataSource: "{00000000-0000-0000-0000-000000000000}",
  },
  fields: {
    Title: {
      value: "Title of the article, campaign or promotion",
    },
    Subtitle: {
      value: "SUBHEADING",
    },
    Content: {
      value:
        "Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Cum sociis natoque penatibus et <a href='#'>magnis</a> dis parturient montes.",
    },
    "Image": {
      "value": {
        "src": "https://via.placeholder.com/600x400",
        "alt": "placeholder image",
        "width": "600",
        "height": "400"
      }
    },
    "Link": {
      "value": {
        "text": "DISCOVER MORE",
        "anchor": "",
        "linktype": "internal",
        "class": "",
        "title": "",
        "target": "",
        "querystring": "",
        "id": "{00000000-0000-0000-0000-000000000000}",
        "href": "#"
      }
    }
  },
};

export const CallToActionDefaultDataNoDatasource = {
  params: {
    name: "CallToAction",
    componentName: "CallToAction",
  },
  rendering: {
    uid: "{00000000-0000-0000-0000-000000000000}",
    componentName: "CallToAction",
  },
};

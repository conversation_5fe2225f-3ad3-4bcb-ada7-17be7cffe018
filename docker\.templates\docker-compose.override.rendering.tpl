services:
  rendering:
    ports:
      - "${RENDERING_PORT}:${RENDERING_PORT}"
    environment:
      SITECORE_API_KEY: "${API_KEY}"
    labels:
      - "traefik.http.routers.rh-${APP_NAME}-secure.entrypoints=websecure"
      - "traefik.http.routers.rh-${APP_NAME}-secure.rule=Host(`${RENDERING_HOST}`) || Host(`${APP_HOST}`)"
      - "traefik.http.routers.rh-${APP_NAME}-secure.tls=true"
      - "traefik.http.routers.rh-${APP_NAME}-secure.middlewares=force-STS-Header, cors"
      - "traefik.http.routers.rh-${APP_NAME}-secure.service=service-${APP_NAME}"
      - "traefik.http.services.service-${APP_NAME}.loadbalancer.server.port=${RENDERING_PORT}"
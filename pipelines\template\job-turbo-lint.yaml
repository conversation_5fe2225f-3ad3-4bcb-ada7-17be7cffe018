parameters:
- name: VmImage
  type: string
  default: 'macos-latest'
- name: BuildConfiguration
  type: string
  default: 'Release'
- name: Solution
  type: string
  default: '**/*.sln'
- name: BuildPlatform
  type: string
  default: 'Any CPU'
- name: SourceCode_Repository
  type: string
- name: XmcVariableGroup
  type: string
- name: NpmPrivateEndpoint
  type: string
- name: TurboProjectPath
  type: string
  default: '$(Build.SourcesDirectory)/src/head'
- name: TargetEnvironment
  type: string
  default: ''
- name: JobName
  type: string
  default: ''
- name: JobDisplayName
  type: string
  default: 'Deploying'
- name: IsDryRun
  type: boolean
  default: false
- name: DependsOn
  type: object
  default: []
- name: Storybooks
  type: object
  default: 
    - Name: 'docs'
      Path: 'apps/docs'
- name: TurboApps
  type: object
  default:
    - Name: 'ep'
      Path: 'apps/ep'
      EnvironmentVariables:
        PUBLIC_URL: 'next.ep.publicUrl'
        SITECORE_API_HOST: 'xmc.ep.host'
        SITECORE_API_KEY: 'xmc.ep.apiKey'
        GRAPH_QL_ENDPOINT: 'xmc.ep.graphQLEndpoint'
        XMV_FA_AUTH_TOKEN: 'xmv.fa.authToken'
        KEYCLOAK_CLIENT_ID: 'xmc.ep.keycloak.clientId'
        KEYCLOAK_CLIENT_SECRET: 'xmc.ep.keycloak.clientSecret'
        KEYCLOAK_ISSUER: 'xmc.ep.keycloak.issuer'
        NEXTAUTH_SECRET: 'xmc.ep.nextauth.secret'
        NEXTAUTH_URL: 'xmc.ep.nextauth.url'
        NEXT_PUBLIC_RAI_PROFILE_SERVICE_PROXY_HOSTNAME: 'next.ep.publicUrl'
        NEXT_PUBLIC_WEB_API_PROXY_HOSTNAME: 'next.ep.publicUrl'
        RAI_PROFILE_SERVICE_API: 'xmc.rai.profile.service.api'
    - Name: 'web'
      Path: 'apps/web'
      EnvironmentVariables:
        PUBLIC_URL: 'next.web.publicUrl'
        SITECORE_API_HOST: 'xmc.web.host'
        SITECORE_API_KEY: 'xmc.web.apiKey'
        GRAPH_QL_ENDPOINT: 'xmc.web.graphQLEndpoint'
        XMV_FA_AUTH_TOKEN: 'xmv.fa.authToken'
        RAI_PROFILE_SERVICE_API: 'xmc.rai.profile.service.api'
        RAI_PROFILE_SERVICE_USERNAME: 'xmc.rai.profile.service.username'
        RAI_PROFILE_SERVICE_PASSWORD: 'xmc.rai.profile.service.password'

jobs:
- job: ${{ parameters.JobName }}
  displayName: ${{ parameters.JobDisplayName }}
  
  ${{ if parameters.DependsOn }}:
    dependsOn: '${{ parameters.DependsOn }}'

  pool:
    vmImage: ${{ parameters.VmImage }}

  variables:
  - group: ${{ parameters.XmcVariableGroup }}

  steps:
  - checkout: ${{ parameters.SourceCode_Repository }}
    fetchDepth: 10
    submodules: true
    lfs: true

  - task: DownloadSecureFile@1
    displayName: Download config.json file
    inputs:
      secureFile: 'config.json'

  - task: CopyFiles@2
    displayName: Copy config.json file into root folder
    inputs:
      sourceFolder: "$(Agent.TempDirectory)"
      contents: "config.json"
      targetFolder: "${{ parameters.TurboProjectPath }}/.turbo/"

  - task: UseNode@1  
    inputs: 
        version: '$(node.version)'

  - task: npmAuthenticate@0
    displayName: NPM Authenticate into Uxbee's private NPM
    inputs:
      workingFile: '${{ parameters.TurboProjectPath }}/.npmrc'
      customEndpoint: '${{ parameters.NpmPrivateEndpoint }}'
  
  ## disable cache as it took more time than npm install
  # - task: Cache@2
  #   inputs:
  #     key: 'npm | "$(Agent.OS)" | package-lock.json'
  #     restoreKeys: |
  #       npm | "$(Agent.OS)"
  #     path: '${{ parameters.TurboProjectPath }}'
  #   displayName: Cache npm

  # - script: npm ci
  #   workingDirectory: '${{ parameters.TurboProjectPath }}'
  #   displayName: npm ci

  - script: |
      npm install  
    displayName: npm install
    workingDirectory: '${{ parameters.TurboProjectPath }}'

  - script: |
      npm install turbo --global   
      npm install tsup --global 
    displayName: Install turbo and tsup
    workingDirectory: '${{ parameters.TurboProjectPath }}'

  - script: |
      turbo run lint
    displayName: Turbo lint
    workingDirectory: '${{ parameters.TurboProjectPath }}'


import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { GridContainer } from "ui";
import { GridItem } from "ui";
const meta: Meta<typeof GridContainer> = {
  title: "ui/Atomic/Atoms/Layouts/GridContainer",
  component: GridContainer,
  tags: ["autodocs", "ui", "molecules", "layouts", "grid-container"],
  parameters: {},
  argTypes: {
    direction: {
      control: { type: 'select' },
      options: ["row", "row-reverse"],
    },
    justifyContent: {
        control: { type: 'select' },
        options: ["flex-start", "flex-end", "center", "space-between", "space-around", "space-evenly"],
    },
    alignItems: {
      control: { type: 'select' },
      options: ["flex-start", "flex-end", "center", "strech", "baseline"],
    },
    columnSpacing: {
      control: { type: 'select' },
      options: ["0", "01" , "02" , "03" , "04" , "05" , "06" , "07" , "08" , "09" , "10" , "20" , "40" , "60" , "80"],
    },
    rowSpacing: {
      control: { type: 'select' },
      options: ["0", "01" , "02" , "03" , "04" , "05" , "06" , "07" , "08" , "09" , "10" , "20" , "40" , "60" , "80"],
    },
  },
};
export default meta;
type Story = StoryObj<typeof GridContainer>;
export const Schema: Story = {
  render: (args) => {
    return (
      <>
        <h2 className="text-lg text-center my-14">We decided move to Flex from Grid, because flex offers high flexibilty over than Grid</h2>
        <h2 className="text-xl text-center my-14">Layout Example 1 you can test to change the options to see how it works</h2>

        <GridContainer 
          justifyContent={args.justifyContent} 
          direction={args.direction} 
          alignItems={args.alignItems}
          rowSpacing={args.rowSpacing}
          columnSpacing={args.columnSpacing}
        >
          <GridItem xs="12" lg="2">
            <div className="bg-blue-200 border-2 border-black h-20">
              <h2 className="text-4xl text-center">1</h2>
            </div>
          </GridItem>
          <GridItem xs="12" lg="2">
            <div className="bg-blue-200 border-2 border-black h-40">
              <h2 className="text-4xl text-center">2</h2>
            </div>
          </GridItem>
          <GridItem xs="12" lg="2">
            <div className="bg-blue-200 border-2 border-black h-60">
              <h2 className="text-4xl text-center">3</h2>
            </div>
          </GridItem>
        </GridContainer>

        {/*======== Another Example How to Use Flex Layout here ======== */}
        <hr className="border-2 border-black my-8" />
        <h2 className="text-xl text-center my-14">Layout Example 2 it's not adjustable (check it on code)</h2>

        <GridContainer
          justifyContent="space-between"
          rowSpacing={{ xs: "10", sm: "20", md: "40", lg: "10", xl: "10" }}
          columnSpacing={{ xs: "05", sm: "05", md: "10", lg: "05", xl: "05" }}
        >
          <GridItem xs="12" lg="2">
            <div className="bg-blue-200 border-2 border-black h-40">
              <h2 className="text-4xl text-center">1</h2>
            </div>
          </GridItem>
          <GridItem xs="12" lg="2">
            <div className="bg-blue-200 border-2 border-black h-40">
              <h2 className="text-4xl text-center">2</h2>
            </div>
          </GridItem>
          <GridItem xs="12" lg="2">
            <div className="bg-blue-200 border-2 border-black h-40">
              <h2 className="text-4xl text-center">3</h2>
            </div>
          </GridItem>
        </GridContainer>
      </>
    );
  },
  args: {
    justifyContent: "flex-start",
    direction: "row",
    alignItems: "flex-start",
    columnSpacing: "01",
    rowSpacing: "01",
  },
};

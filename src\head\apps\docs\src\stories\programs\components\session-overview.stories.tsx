import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { WithSitecoreContextDecorator } from "@uxbee/storybook-mock-sc";
import { SessionsOverviewInternal, Speaker } from "programs";
import { SpeakerMockData } from "../data/speaker-mock-data";
import { SessionMockData } from "../data/session-mock-data";

const meta: Meta<typeof SessionsOverviewInternal> = {
    title: "RAI/Programs/SessionOverview",
    component: SessionsOverviewInternal,
    tags: ["autodocs", "rai", "ep"],
    argTypes: {},
    decorators: [
        (Story, context) =>
            WithSitecoreContextDecorator(
                () => (
                    <div className="min-h-screen bg-[#fafafa]">
                        <Story />
                    </div>
                ),
                context,
                SessionsOverviewInternal,
                false
            ),
    ],
};

export default meta;

type Story = StoryObj<typeof SessionsOverviewInternal>;

export const Default: Story = {
    args: {
        language: "en",
        sessions: SessionMockData,
        isLoading: false,
        facets: [
            {
                name: "location",
                label: "Location",
                value: [
                    {
                        id: "facetid_eyJ0eXBlIjoiZXEiLCJuYW1lIjoibG9jYXRpb24iLCJ2YWx1ZSI6IkludGVydHJhZmZpYyBTdW1taXQgVGhlYXRyZSAxIn0=",
                        text: "Intertraffic Summit Theatre 1",
                        count: 34
                    },
                    {
                        id: "facetid_eyJ0eXBlIjoiZXEiLCJuYW1lIjoibG9jYXRpb24iLCJ2YWx1ZSI6IkRlbW8gQXJlYSAoaGFsbCA3KSJ9",
                        text: "Demo Area (hall 7)",
                        count: 28
                    },
                    {
                        id: "facetid_eyJ0eXBlIjoiZXEiLCJuYW1lIjoibG9jYXRpb24iLCJ2YWx1ZSI6IkludGVydHJhZmZpYyBTdW1taXQgVGhlYXRyZSAzIn0=",
                        text: "Intertraffic Summit Theatre 3",
                        count: 27
                    },
                ]
            },
            {
                name: "speakers",
                label: "Speakers",
                value: [
                    {
                        id: "facetid_eyJ0eXBlIjoiZXEiLCJuYW1lIjoic3BlYWtlcnMiLCJ2YWx1ZSI6InNwa2ludGVydHJhZmZpY2FtczIwMjQwMjcyIn0=",
                        text: "spkintertrafficams20240272",
                        count: 4
                    },
                    {
                        id: "facetid_eyJ0eXBlIjoiZXEiLCJuYW1lIjoic3BlYWtlcnMiLCJ2YWx1ZSI6InNwa2ludGVydHJhZmZpY2FtczIwMjQwMjczIn0=",
                        text: "spkintertrafficams20240273",
                        count: 4
                    },
                    {
                        id: "facetid_eyJ0eXBlIjoiZXEiLCJuYW1lIjoic3BlYWtlcnMiLCJ2YWx1ZSI6InNwa2ludGVydHJhZmZpY2FtczIwMjQwMDMzIn0=",
                        text: "spkintertrafficams20240033",
                        count: 3
                    },
                ]
            },
            {
                name: "topic",
                label: "Topic",
                value: [
                    {
                        id: "facetid_eyJ0eXBlIjoiZXEiLCJuYW1lIjoidG9waWMiLCJ2YWx1ZSI6IlNtYXJ0IFNhZmUgYW5kIFN1c3RhaW5hYmxlIE1vYmlsaXR5In0=",
                        text: "Smart Safe and Sustainable Mobility",
                        count: 47
                    },
                    {
                        id: "facetid_eyJ0eXBlIjoiZXEiLCJuYW1lIjoidG9waWMiLCJ2YWx1ZSI6IkNvbm5lY3RlZCBhbmQgQXV0b21hdGVkIERyaXZpbmcifQ==",
                        text: "Connected and Automated Driving",
                        count: 41
                    },
                    {
                        id: "facetid_eyJ0eXBlIjoiZXEiLCJuYW1lIjoidG9waWMiLCJ2YWx1ZSI6IlNtYXJ0IFBhcmtpbmcifQ==",
                        text: "Smart Parking",
                        count: 19
                    },
                ]
            }
        ],
        selectedFacets: [],
        timezoneOffset: 0,
        hasNext: false,
        buildSessionDetailUrl: () => '/session_detail',
        buildSpeakerDetailUrl: () => '/speaker_detail',
        buildCalendarIcsFileUrl: () => '/session_ics',
        onUpdateSearchText: () => {},
        onToggleFacets: () => {},
        onDateSelect: () => {},
        onClearAll: () => {},
        loadMore: () => {},
        resetPagedResult: () => {},
        texts: {
            clearAll: "CLEAR ALL",
            search: "Search",
            seeLess: "See less",
            seeMore: "See more",
            topicFilter: "Filter by topic",
            speakerFilter: "Filter by speaker",
            topicsHeader: "Topics:",
            sessionSeeMore: "See more",
        }
    },
};

import { Meta, StoryObj } from "@storybook/react";
import { GridContainer } from "ui";
import { Gallery } from "ui";

const meta: Meta<typeof Gallery> = {
  title: "ui/Atomic/Molecules/Gallery/Gallery",
  component: Gallery,
  tags: ["autodocs", "ui", "molecules", "countdown"],
  parameters: {},
  argTypes: {},
};

export default meta;

type Story = StoryObj<typeof Gallery>;
export const Schema: Story = {
  render: (args) => {
    return (
      <div className="bg-neutral-50">
        <GridContainer rowSpacing="04" columnSpacing="04">
          <Gallery />
        </GridContainer>
      </div>
    );
  },

  args: {
  },
};

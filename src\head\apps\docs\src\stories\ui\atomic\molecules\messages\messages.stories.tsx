import type { <PERSON>a, StoryObj } from "@storybook/react";
import { Messages } from "ui";
import { messagesData } from "../../../data/messages.data";

const meta: Meta<typeof Messages> = {
  title: "ui/Atomic/Molecules/Messages/Messages",
  component: Messages,
  tags: ["autodocs", "ui", "molecules", "messages"],
  parameters: {
  },
  argTypes: {},
};

export default meta;

type Story = StoryObj<typeof Messages>;
export const Schema: Story = {
  render: (args) => {
    return (
        <div className="h-[28rem] px-4 lg:px-36 py-6 bg-neutral-50">
          <Messages {...args} />
        </div>
    );
  },

  args: {
    messages: messagesData,
  },
};

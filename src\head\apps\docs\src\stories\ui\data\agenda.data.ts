import { AgendaData } from "ui";

export const agendaDataVertical: AgendaData =
{
  title: {
    value: "Important dates of the awards 2024"
  },
  tooltip: {
    value: "Tooltip for awards"
  },
  isHorizontal: {
    value: false
  },
  agendaitems: {
    results: [
      {
        title: {
          value: "Entry open to all exhibitors",
        },
        date: {
          value: "20240514T235900Z",
        },
        xBE_Icon: {
          value: "fa-light fa-shop",
        },
      },
      {
        title: {
          value: "Final deadline for registration",
        },
        date: {
          value: "20240915T235900Z",
        },
        xBE_Icon: {
          value: "fa-light fa-calendar",
        },
      },
      {
        title: {
          value: "Send in your product(s)",
        },
        date: {
          value: "20240921T235900Z",
        },
        xBE_Icon: {
          value: "fa-light fa-cube",
        },
      },
      {
        title: {
          value: "All products at RAI",
        },
        date: {
          value: "20241006T235900Z",
        },
        xBE_Icon: {
          value: "fa-light fa-clipboard-list-check",
        },
      },
      {
        title: {
          value: "Nominated products",
        },
        date: {
          value: "20241025T235900Z",
        },
        xBE_Icon: {
          value: "fa-light fa-comments",
        },
      },
      {
        title: {
          value: "Winner Dame 2024",
        },
        date: {
          value: "20241115T235900Z",
        },
        xBE_Icon: {
          value: "fa-light fa-trophy",
        },
      }
    ]
  }
};

export const agendaDataHorizontal: AgendaData =
{
  title: {
    value: "Important dates of the awards 2024"
  },
  tooltip: {
    value: "Tooltip for awards"
  },
  isHorizontal: {
    value: true
  },
  agendaitems: {
    results: [
      {
        title: {
          value: "Entry open to all exhibitors",
        },
        date: {
          value: "20240514T235900Z",
        },
        xBE_Icon: {
          value: "fa-light fa-shop",
        },
      },
      {
        title: {
          value: "Final deadline for registration",
        },
        date: {
          value: "20240915T235900Z",
        },
        xBE_Icon: {
          value: "fa-light fa-calendar",
        },
      },
      {
        title: {
          value: "Send in your product(s)",
        },
        date: {
          value: "20240921T235900Z",
        },
        xBE_Icon: {
          value: "fa-light fa-cube",
        },
      },
      {
        title: {
          value: "All products at RAI",
        },
        date: {
          value: "20241006T235900Z",
        },
        xBE_Icon: {
          value: "fa-light fa-clipboard-list-check",
        },
      },
      {
        title: {
          value: "Nominated products",
        },
        date: {
          value: "20241025T235900Z",
        },
        xBE_Icon: {
          value: "fa-light fa-comments",
        },
      },
      {
        title: {
          value: "Winner Dame 2024",
        },
        date: {
          value: "20241115T235900Z",
        },
        xBE_Icon: {
          value: "fa-light fa-trophy",
        },
      }
    ]
  }
};

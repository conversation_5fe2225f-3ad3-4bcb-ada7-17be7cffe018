import type { Meta, StoryObj } from "@storybook/react";
import { Card } from "ui";
const meta: Meta<typeof Card> = {
  title: "ui/Atomic/Molecules/Card/Card",
  component: Card,
  tags: ["autodocs", "ui", "molecules", "card"],
  parameters: {},
  argTypes: {},
};
export default meta;
type Story = StoryObj<typeof Card>;
export const Schema: Story = {
  render: (args) => {
  return (
      <div className="bg-brand-body-bg-primary-1 grid grid-cols-6 gap-x-8 p-6">
          <div className="col-span-3">
            <Card {...args}/>
          </div>
          <div className="col-span-3">
            <Card {...args}/>
          </div>
      </div>
    );
  },
  args: {
    card: {
      id: "1",
      date: "2021-10-25",
      title: "Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
      content:
        "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam vitae ultricies justo. Sed nec ante at elit aliquam consectetur. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia curae; Donec vitae quam non nulla faucibus aliquam. Donec sit amet nisi sit amet nulla aliquet tincidunt. Donec euismod, nunc vitae aliquam aliquam, nisl ipsum ultricies odio, nec aliquet nisl arcu eget eros. Etiam vitae ultricies justo. Sed nec ante at elit aliquam consectetur. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia curae; Donec vitae quam non nulla faucibus aliquam. Donec sit amet nisi sit amet nulla aliquet tincidunt. Donec euismod, nunc vitae aliquam aliquam, nisl ipsum ultricies odio, nec aliquet nisl arcu eget eros.",
      imgSrc: "https://picsum.photos/seed/picsum/200/300",
    },
  },
};

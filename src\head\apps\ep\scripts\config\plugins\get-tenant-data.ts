import { ConfigPlugin } from '..';
import { JssConfig } from 'lib/config';
import { TenantPluginCore } from '@uxbee/uxbee-sitecore-headless-sxa-multisite';
import { createGraphQLClientFactory } from 'lib/graphql-client-factory/create';

class GetTenantDataPlugin implements ConfigPlugin {
  order = 260;

  async exec(config: JssConfig): Promise<JssConfig> {
    const pluginCore = new TenantPluginCore({
      clientFactory: createGraphQLClientFactory(config),
      fetch: fetch,
    });

    let tenantData = await pluginCore.getTenants();
    if (!tenantData) {
      console.warn('Impossible to retrieve tenant data.');
      tenantData = [];
    }

    return Object.assign({}, config, {
      tenants: JSON.stringify(tenantData),
    });
  }
}

export const getTenantDataPlugin = new GetTenantDataPlugin();

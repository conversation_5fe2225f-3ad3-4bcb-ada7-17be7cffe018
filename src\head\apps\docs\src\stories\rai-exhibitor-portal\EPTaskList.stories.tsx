import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { WithSitecoreContextDecorator } from "@uxbee/storybook-mock-sc";
import { TaskListComponent } from "rai-exhibitor-portal";
import { TaskListData } from "./data/ep-tasklist.data";

const meta: Meta<typeof TaskListComponent.Default> = {
    title: "RAI/Exhibitor Portal/Canvas/TaskListComponent",
    component: TaskListComponent.Default,
    tags: ["autodocs", "rai", "ep"],
    argTypes: {},
    decorators: [
        (Story, context) =>
            WithSitecoreContextDecorator(
                () => (
                    <div className="h-[30rem] bg-[#fafafa]">
                        <div className="grid grid-cols-12 gap-4">
                            <Story />
                        </div>
                    </div>
                ),
                context,
                TaskListComponent.Default,
                false
            ),
    ],
};

export default meta;

type Story = StoryObj<typeof TaskListComponent.Default>;

export const Default: Story = {
    // args: LabelFooterData,
    args: TaskListData
};

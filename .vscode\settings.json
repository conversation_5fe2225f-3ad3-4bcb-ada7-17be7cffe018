{"files.associations": {"*.sicpackage": "json"}, "json.schemas": [{"fileMatch": ["/sitecore.json"], "url": "./.sitecore/schemas/RootConfigurationFile.schema.json"}, {"fileMatch": ["/.sitecore/user.json"], "url": "./.sitecore/schemas/UserConfiguration.schema.json"}, {"fileMatch": ["*.module.json"], "url": "./.sitecore/schemas/ModuleFile.schema.json"}], "eslint.workingDirectories": [{"pattern": "./src/head/apps/*/"}, {"pattern": "./src/head/packages/*/"}], "cSpell.words": ["tailwindcss", "uxbee"], "dotnet.preferCSharpExtension": true}
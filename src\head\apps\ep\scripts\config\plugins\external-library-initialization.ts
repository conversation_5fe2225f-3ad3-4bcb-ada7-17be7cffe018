import { ConfigPlugin } from '..';
import { JssConfig } from 'lib/config';
import { ExternalLibraryInitializationPluginCore } from '@uxbee/uxbee-sitecore-headless-sxa-external-libraries';
import { createGraphQLClientFactory } from 'lib/graphql-client-factory/create';

class ExternalLibraryInitializationPlugin implements ConfigPlugin {
  order = 78;

  async exec(config: JssConfig): Promise<JssConfig> {
    const externalLibraryInitializationPluginCore = new ExternalLibraryInitializationPluginCore({
      clientFactory: createGraphQLClientFactory(config),
      sitesString: config.sites,
      defaultSiteRootPath: config.defaultSiteRootPath,
      sitecoreSiteName: config.sitecoreSiteName,
      defaultLanguage: config.defaultLanguage,
    });

    let externalLibraryKeyData =
      await externalLibraryInitializationPluginCore.initializeExternalLibraryData();
    if (!externalLibraryKeyData) {
      console.warn('Impossible to retrieve external library data.');
      externalLibraryKeyData = [];
    }

    return Object.assign({}, config, {
      siteExternalLibraryData: JSON.stringify(externalLibraryKeyData),
    });
  }
}

export const externalLibraryInitializationPlugin = new ExternalLibraryInitializationPlugin();

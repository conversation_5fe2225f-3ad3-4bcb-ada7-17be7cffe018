import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { WithSitecoreContextDecorator } from "@uxbee/storybook-mock-sc";
import { ButtonComponent } from "ui-sitecore";

const meta: Meta<Partial<typeof ButtonComponent.Default>> = {
  title: "ui-sitecore/components/content/Button",
  component: ButtonComponent.Default,
  tags: ["autodocs", "rai", "ep"],
  parameters: {},
  argTypes: {
    variant: {
      options: [
        "btn-primary-solid",
        "btn-secondary-solid",
        "btn-tertiary-solid",
        "btn-quaternary-solid",
        "btn-primary-outline",
        "btn-secondary-outline",
        "btn-tertiary-outline",
        "btn-quaternary-outline",
        "btn-success-solid",
        "btn-danger-solid",
      ],
      control: { type: "select" },
    },
    iconPosition: {
      options: ["left", "right"],
      control: { type: "select" },
    },
  },
  decorators: [
    (Story, context) =>
      WithSitecoreContextDecorator(
        () => (
          <div className="h-[20rem] bg-[#fafafa]">
            <div className="grid grid-cols-12 gap-4">
              <div className="col-span-12 m-12">
                <Story />
              </div>
            </div>
          </div>
        ),
        context,
        ButtonComponent.Default,
        false
      ),
  ],
};

export default meta;
type Story = StoryObj<typeof ButtonComponent>;

export const Default: Story = (args) => {
  const reconstructedArgs = {
    dataSource: "Hello",
    params: {
      name: "Video",
      componentName: "Video",
      tag: "h1",
    },
    rendering: {
      uid: "{00000000-0000-0000-0000-000000000000}",
      componentName: "Video",
      dataSource: "{00000000-0000-0000-0000-000000000000}",
    },
    fields: {
      data: {
        button: {
          displayName: args.displayName,
          variant: {
            value: args.variant,
          },
          iconPosition: {
            value: args.iconPosition,
          },
          icon: {
            value: args.icon,
          },
          link: {
            value: {
              href: args.href,
              text: args.text,
            },
          },
        }
      },
    },
  };

  return <ButtonComponent.Default {...reconstructedArgs} />;
};

Default.args = {
  displayName: "Company Profile",
  variant: "btn-primary-solid",
  iconPosition: "right",
  icon: `<i class="fa-solid fa-arrow-right"></i>`,
  text: "Company Profile",
  href: "#",
} as Partial<typeof ButtonComponent.Default>;

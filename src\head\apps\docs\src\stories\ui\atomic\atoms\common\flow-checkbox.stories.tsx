import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { FlowCheckbox } from "ui";
// More on how to set up stories at: https://storybook.js.org/docs/react/writing-stories/introduction
const meta: Meta<typeof FlowCheckbox> = {
  title: "ui/Atomic/Atoms/common/FlowCheckbox",
  component: FlowCheckbox,
  tags: ["autodocs", "ui", "atoms"],
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/react/configure/story-layout
    actions: { argTypesRegex: "^on.*" },
    rootAttributesTooltip: true,
  },
};

export default meta;

type Story = StoryObj<typeof FlowCheckbox>;

export const Schema: Story = {
  render: (args) => {
    return (
      <div className="flex h-[100px] w-full items-center justify-center bg-brand-dividers-1">
        <FlowCheckbox {...args} />
      </div>
    );
  },
  args: {
    current_state: 3,
    data: [
      {
        id: 1,
        status: true,
      },
      {
        id: 2,
        status: true,
      },
      {
        id: 3,
        status: true,
      },
      {
        id: 4,
        status: false,
      },
      {
        id: 5,
        status: false,
      },
    ],
  },
};

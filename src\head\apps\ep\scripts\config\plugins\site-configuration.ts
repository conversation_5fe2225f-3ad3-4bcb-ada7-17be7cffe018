import { SiteInfo } from '@sitecore-jss/sitecore-jss-nextjs';
import { ConfigPlugin } from '..';
import { JssConfig } from 'lib/config';
import { Tenant } from '@uxbee/uxbee-sitecore-headless-sxa-multisite';
import { createGraphQLClientFactory } from 'lib/graphql-client-factory/create';
import {
  ConfigurationTemplate,
  InitializeSiteConfigurationValuesPluginCore,
} from 'site-configuration';

const CONFIGURATION_TEMPLATES: ConfigurationTemplate[] = [
  // Label event configuration
  {
    id: '5B74D2B3103F4B8BA1F29829D215A1B2',
    name: 'LabelEventConfiguration',
    fieldName: 'Event Identifier',
    siteAttributeName: 'eventId',
  },
  // Global search configuration
  {
    id: '8CC2DB7039C24C6B96A1C1773A24E60C',
    name: 'GlobalSearchConfiguration',
    fieldName: 'Source Id',
    siteAttributeName: 'globalSearchSourceId',
  },
];

class SiteConfigurationPlugin implements ConfigPlugin {
  order = 280;

  async exec(config: JssConfig): Promise<JssConfig> {
    const sites = JSON.parse(config.sites ?? '[]') as SiteInfo[];
    const tenants = JSON.parse(config.tenants ?? '[]') as Tenant[];

    if (!sites) {
      console.warn(`[SiteConfigurationPlugin] Couldn't parse multisite configuration.`);

      return Object.assign({}, config);
    }

    if (!tenants) {
      console.warn(`[SiteConfigurationPlugin] Couldn't parse tenant configuration.`);

      return Object.assign({}, config);
    }

    let mutatedSites: SiteInfo[] | null = null;
    try {
      const pluginCore = new InitializeSiteConfigurationValuesPluginCore({
        clientFactory: createGraphQLClientFactory(config),
        sites,
        tenants,
        configurationTemplates: CONFIGURATION_TEMPLATES,
        fetch,
      });

      mutatedSites = await pluginCore.getMutatedSites();
    } catch (error) {
      console.error('Site configuration properties initialization error.');
      console.error(error);
    }

    return Object.assign({}, config, {
      sites: JSON.stringify(mutatedSites ?? sites),
    });
  }
}

export const siteConfigurationPlugin = new SiteConfigurationPlugin();

  import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
  import {
    RelatedArticles,
  } from "ui-sitecore";
  
  // More on how to set up stories at: https://storybook.js.org/docs/react/writing-stories/introduction
  const meta: Meta<typeof RelatedArticles.Default> = {
    title: "ui-sitecore/components/news/RelatedArticles",
    component: RelatedArticles.Default,
    tags: ["autodocs", "ui-sitecore", "sitecore"],
    argTypes: {},
    decorators: [
    ],
  };
  export default meta;
  
  type Story = StoryObj<typeof RelatedArticles.Default>;  
  
  export const Default: Story = {
    args: {
      sectionTitle: {
        value: "Best read articles"
      },
      articles: [
        {
          title: "What is Lorem Ipsum?",
          url: "#related_1",
          image: {
            value: {
              src: "https://assets-prd.raicore.com/-/media/project/rai-amsterdam/aquatech/news/2024/hong-kong-article.png?h=628&amp;iar=0&amp;w=1200&amp;rev=094d087fe5b147a28fa51d8b07dcedf2&amp;hash=B7A7E4D65BE05965472CFC549B9D6AFE",
            }
          }
        },
        {
          title: "REGAIN consortium to test three distinct water treatment technologies in the Netherlands",
          url: "#related_2",
          image: {
            value: {
              src: "https://assets-prd.raicore.com/-/media/project/rai-amsterdam/aquatech/news/2024/hong-kong-article.png?h=628&amp;iar=0&amp;w=1200&amp;rev=094d087fe5b147a28fa51d8b07dcedf2&amp;hash=B7A7E4D65BE05965472CFC549B9D6AFE",
            }
          }
        }
      ]
    },
  };
  
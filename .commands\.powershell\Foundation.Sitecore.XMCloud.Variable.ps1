function Push-Local-Variables-For-Enviroment() {
    param(
        [string]
        $ProjectName,
        [string]
        $EnvironmentName,
        [string]
        $VariableIdentifier = 'XMV'
    )
    # To ensure a clean environment, first remove all unused custom variables
    $environmentId = Get-Environment-Id-by-Name -ProjectName $ProjectName -EnvironmentName $EnvironmentName
    if($environmentId) {
        $Variables = (dotnet sitecore cloud environment variable list --environment-id $environmentId --json) | ConvertFrom-Json
        foreach ($variable in $Variables) {
            $varName = $variable.name
            if($varName -AND $varName.StartsWith($VariableIdentifier)) {
                # Only remove varaibles that are no longer a part of the environment variables
                if (!(Test-Path "Env:\$variableName")) {
                    Remove-Variable-From-Environment -ProjectName $ProjectName -EnvironmentName $EnvironmentName -Name $varName
                }
            }
        }
    }

    # Add all the custom variables by looping through the system environment files
    $envVariables = Get-ChildItem Env:
    foreach ($envVar in $envVariables) {
        $varName = $envVar.Name
        $varValue = $envVar.Value
        if($varName.StartsWith($VariableIdentifier)) {
            $IsSecret = ($varName -match "key" -OR $varName -match "secret" -OR $varName -match "token")
            Add-Variable-To-Environment -ProjectName $ProjectName -EnvironmentName $EnvironmentName -Name $varName -Value $varValue -Target '' -IsSecret $IsSecret
        }
    }
}

function Add-Variable-To-Environment() {
    param(
        [string]
        $ProjectName,
        [string]
        $EnvironmentName,
        [string]
        $Name,
        [string]
        $Value,
        [string]
        $Target,
        [bool]
        $IsSecret
    )
    # Locate the environment id
    $environmentId = Get-Environment-Id-by-Name -ProjectName $ProjectName -EnvironmentName $EnvironmentName
    # Add the variable to the environment
    if($environmentId) {
        if($target) {
            dotnet sitecore cloud environment variable upsert --environment-id $environmentId --name $Name --value $Value --target $Target --secret $IsSecret
        } else {
            dotnet sitecore cloud environment variable upsert --environment-id $environmentId --name $Name --value $Value --secret $IsSecret
        }
        Write-Debug "Variable $Name is added to the $EnvironmentName envrionment"
    }
}

function Remove-Variable-From-Environment() {
    param(
        [string]
        $ProjectName,
        [string]
        $EnvironmentName,
        [string]
        $Name,
        [string]
        $Value
    )
    # Locate the environment id
    $environmentId = Get-Environment-Id-by-Name -ProjectName $ProjectName -EnvironmentName $EnvironmentName
    # Add the variable to the environment
    if($environmentId) {
        dotnet sitecore cloud environment variable delete --environment-id $environmentId --name $Name
        Write-Debug "Variable $Name is removed from the $EnvironmentName envrionment"
    }
}
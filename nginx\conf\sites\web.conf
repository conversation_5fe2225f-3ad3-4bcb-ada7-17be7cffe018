	server {
        listen       443 ssl;
        server_name  www.* blueprint.* aquatechtrade.*;

        ssl_certificate      ../../docker/traefik/certs/wildcard.$customer_domain.crt;
        ssl_certificate_key  ../../docker/traefik/certs/wildcard.$customer_domain.key;

        ssl_session_cache    shared:SSL:1m;
        ssl_session_timeout  5m;

        ssl_ciphers  HIGH:!aNULL:!MD5;
        ssl_prefer_server_ciphers  on;

        add_header Access-Control-Allow-Origin *;

        location / {
          proxy_pass              http://localhost:3001;
          proxy_set_header        Host              $host;
          proxy_set_header        X-Real-IP         $remote_addr;
          proxy_set_header        X-Forwarded-For   $proxy_add_x_forwarded_for;
          proxy_set_header        X-Client-Verify   SUCCESS;
          proxy_set_header        X-Client-DN       $ssl_client_s_dn;
          proxy_set_header        X-SSL-Subject     $ssl_client_s_dn;
          proxy_set_header        X-SSL-Issuer      $ssl_client_i_dn;
          proxy_set_header        X-Forwarded-Proto http;
          proxy_read_timeout      1800;
          proxy_connect_timeout   1800;
          proxy_busy_buffers_size 512k;
          proxy_buffers           4 512k;
          proxy_buffer_size       256k;
          proxy_set_header        Upgrade $http_upgrade;
          proxy_set_header        Connection "upgrade";
        }
    }
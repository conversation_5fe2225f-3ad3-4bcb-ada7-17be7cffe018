[CmdletBinding()]
param(
    [string]
    $ProjectName,
    [Parameter(ValueFromPipelineByPropertyName)]
    [ValidateSet(
        'test',
        'acceptation',
        'production'
    )]
    [string]$EnvironmentName
    
)
Push-Location $PSScriptRoot

$CommandDir = Get-Location
Push-Location -Path ..
$ContextDir = Get-Location
Pop-Location
. $CommandDir/.powershell/Foundation.Sitecore.XMCloud.ps1

$UserFile = Join-Path $ContextDir "/.sitecore/user.json"

$IsAuthenticated = Confirm-XMCloud-Login -UserFile $UserFile

if($IsAuthenticated) {
    # Add your actions here
} else {
    Write-Host "User is not authenticated, no actions can be executed" -ForegroundColor Red
}
Pop-Location
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { BreadcrumbComponent } from "ui-sitecore";
import { WithSitecoreContextDecorator } from "@uxbee/storybook-mock-sc/lib/WithSitecoreContextDecorator";

// More on how to set up stories at: https://storybook.js.org/docs/react/writing-stories/introduction
const meta: Meta<typeof BreadcrumbComponent.Default> = {
  title: "ui-sitecore/components/navigation/Breadcrumb",
  component: BreadcrumbComponent.Default,
  tags: ["autodocs", "ui-sitecore", "sitecore"],
  argTypes: {},
  decorators: [
    (Story, context) =>
      WithSitecoreContextDecorator(Story, context, BreadcrumbComponent.Default, false),
  ],
};

export default meta;

type Story = StoryObj<typeof BreadcrumbComponent.Default>;

export const Default: Story = {
  args: {
    params: {
      name: "<PERSON>readcrumb",
    },
    rendering: {
      uid: "{00000000-0000-0000-0000-000000000000}",
      componentName: "Breadcrumb",
      dataSource: "{00000000-0000-0000-0000-000000000000}",
    },
    fields: {
      "data": {
        "breadcrumbItems": {
          "ancestors": [
            {
              "id": "9B104C62861749FBB626D2CC1B957138",
              "name": "Products",
              "navigationTitle": {
                  "value": "Products"
              },
              "navigationFilter": {
                  "value": ""
              },
              "url": {
                  "path": "/company/products-and-services/,-w-,/products"
              }
            },
            {
              "id": "56055A9945F244BFB4BAFC39074D286A",
              "name": "*",
              "navigationTitle": {
                  "value": "CompanyName"
              },
              "navigationFilter": {
                  "value": ""
              },
              "url": {
                  "path": "/company/products-and-services/,-w-,"
              }
            },
            {
              "id": "145F09ABD447466DA254FA1987C88094",
              "name": "Products and services",
              "navigationTitle": {
                  "value": "Products and services Long section"
              },
              "navigationFilter": {
                  "value": ""
              },
              "url": {
                  "path": "/company/products-and-services"
              }
            },
            {
              "id": "CF551BDC9D2F4853A74B6DF7DF51A043",
              "name": "Home",
              "navigationTitle": {
                  "value": "Home"
              },
              "navigationFilter": {
                  "value": ""
              },
              "url": {
                  "path": "/company"
              }
            }
          ],
          "id": "E8F9F4C36A4D499DB1A3CC6F1DE50765",
          "name": "*",
          "navigationTitle": {
              "value": "ProductName"
          }
        }
      }
    }
  },
};

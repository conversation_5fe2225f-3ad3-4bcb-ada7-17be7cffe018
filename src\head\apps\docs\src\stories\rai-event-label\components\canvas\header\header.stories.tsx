import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { WithSitecoreContextDecorator } from "@uxbee/storybook-mock-sc";
import { RAIHeaderComponent } from "rai-event-label";
import { LabelHeaderData } from "../../../mocking/header.data";

const meta: Meta<typeof RAIHeaderComponent.Default> = {
  title: "RAI/Event Label/Canvas/Header",
  component: RAIHeaderComponent.Default,
  tags: ["autodocs", "rai", "ep"],
  args: {
    scrollForLogoShrink: 200,
  },
  argTypes: {},
  decorators: [
    (Story, context) =>
      WithSitecoreContextDecorator(
        () => (
          <div className="flex flex-col">
            <Story />
            <img
              src="https://assets-prd.raicore.com/-/media/project/rai-amsterdam/rai/header-home/newhomepagebanner.jpg"
              alt="background-image"
            />
          </div>
        ),
        context,
        RAIHeaderComponent.Default,
        false
      ),
  ],
};

export default meta;

type Story = StoryObj<typeof RAIHeaderComponent.Default>;

export const Label: Story = {
  args: {
    ...LabelHeaderData,
    params: {
      Styles: "header-default",
    },
  },
};

export const RAI: Story = {
  args: {
    ...LabelHeaderData,
    params: {
      Styles: "header-rai",
    },
  },
};

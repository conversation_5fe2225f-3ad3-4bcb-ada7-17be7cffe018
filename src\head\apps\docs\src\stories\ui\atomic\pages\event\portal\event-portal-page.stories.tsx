import type { Meta, StoryObj } from "@storybook/react";
import { EventPortalPage } from "ui";
const meta: Meta<typeof EventPortalPage> = {
  title: "ui/Atomic/Pages/Event/EventPortalPage",
  component: EventPortalPage,
  tags: ["autodocs", "ui", "molecules", "home-page"],
  parameters: {},
  argTypes: {},
};
export default meta;
type Story = StoryObj<typeof EventPortalPage>;
export const Schema: Story = {
  render: (args) => {
    return (
      <div className="h-[40rem]">
        <EventPortalPage {...args} />
      </div>
    );
  },
  args: {},
};

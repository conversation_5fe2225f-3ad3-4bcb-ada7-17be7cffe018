function Reset-Turbo-Build() {
	param(
        [ValidateNotNullOrEmpty()]
        [string]
        $TurboPath,
        [ValidateNotNullOrEmpty()]
        [string]
        $LockFileName = "package-lock.json"
	)
	Write-Host "Resetting Turbo" -ForegroundColor DarkYellow
	$PackageLockFile = (Join-Path $TurboPath $LockFileName)
	if(Test-Path -Path $PackageLockFile){
		Remove-Item -Force $PackageLockFile
	}
	Push-Location $TurboPath
	npm install
	Pop-Location
	Write-Host "Turbo reset is completed" -ForegroundColor DarkGreen
}

function Remove-All-Node-Modules-From-Repository() {
    param (
        [ValidateNotNullOrEmpty()]
        [string]
        $RepositoryPath,
        [ValidateNotNullOrEmpty()]
        [string]
        $LockFileName = "package-lock.json",
        [switch]
        $Reinstall = $false
    )
    # Remove all node_modules to ensure a quick copy
    Push-Location $RepositoryPath
    npm cache clean -f
    npm run clear:all
    # Also remove the package-lock.json file
    $PackageLockFile = (Join-Path $RepositoryPath $LockFileName)
    if(Test-Path -Path $PackageLockFile){
        Remove-Item $PackageLockFile
    }
    npm run clear:all
    npm cache clean -f
    if($Reinstall) {
        Write-Host "Reinstalling all repository packages" -ForegroundColor DarkYellow
        npm install
    }
    Pop-Location
}

function Update-Workspace-With-Turbo-Template() {
    param(
        [ValidateNotNullOrEmpty()]
        [string]
        $TurboPath,
        [string]
        $WorkspaceName,
        [string]
        $TemplateName = "turbo-workspace-template",
        [array]
        $FilesToRemove = @(".eslintrc")
    )
    # Files to add from the template
    $TemplateRootPath =  Join-Path $TurboPath "templates"
    $TemplatePath = Join-Path $TemplateRootPath $TemplateName
    $AppsPath = Join-Path $TurboPath "apps"
    $WorkspacePath = Join-Path $AppsPath $WorkspaceName

    # Copy the actal template files
    Write-Host "Installing template" $TemplateName "on" $WorkspaceName -ForegroundColor DarkYellow
    Copy-Item -Path $TemplatePath\* -Destination $WorkspacePath -Recurse -Force

    # Sync the file package.*.json with the package.json on target workspace
    Get-ChildItem $WorkspacePath -Directory -Include "package.*.json" | ForEach-Object {
        #TODO: sync the template package json file with the workspace package.json
    }

    # Files to remove from the configuration
    $FilesToRemove | ForEach-Object {
        $FileToRemove = Join-Path $WorkspacePath $_
        if(Test-Path -Path $FileToRemove){
            Remove-Item -Force $FileToRemove
        }
    }
    # Remove the template package.json files
    Get-ChildItem $WorkspacePath -Directory -Include "package.*.json" | ForEach-Object {
        Remove-Item -FolderPath $_.FullName
    }
}

function Sync-All-Workspaces-With-Package-Version() {
    param(
        [ValidateNotNullOrEmpty()]
        [string]
        $TurboPath,
        [ValidateNotNullOrEmpty()]
        [string]
        $PackageName,
        [ValidateNotNullOrEmpty()]
        [string]
        $PackageVersion,
        [ValidateSet("","save", "dev", "save-dev")]
        [string]
        $PackageScope
    )
    Push-Location $TurboPath
    $WorkspacesRoot = Join-Path $TurboPath "/apps"
    Get-ChildItem $WorkspacesRoot |
    Foreach-Object {
        Write-Host "Installing package" $PackageName"@"$PackageVersion "on" $_.Name -ForegroundColor DarkYellow
        if($PackageScope -ceq "dev" -or $PackageScope -ceq "save-dev") {
            npm -y install $PackageName@$PackageVersion --workspace $_.Name --save-dev
        } else {
        npm -y install $PackageName@$PackageVersion --workspace $_.Name
        }
    }
    Pop-Location
}

function Add-Turbo-Pipeline() {
    param(
        [ValidateNotNullOrEmpty()]
        [string]
        $TurboPath,
        [ValidateNotNullOrEmpty()]
        [string]
        $PipelineName
    )
    $TurboConfigFile = Join-Path $TurboPath "turbo.json"
    # Add a new pipeline to turbo.json for the given name
    $TurboConfig = Read-JSON-From-File -JSONFile $TurboConfigFile
    if (-not $TurboConfig.pipeline.$PipelineName) {
        $TurboConfig.pipeline | Add-Member -MemberType NoteProperty  -Name $PipelineName -Value ([PSCustomObject]@{})
        Write-JSON-To-File -JSON $TurboConfig -JSONFile $TurboConfigFile
    }
}
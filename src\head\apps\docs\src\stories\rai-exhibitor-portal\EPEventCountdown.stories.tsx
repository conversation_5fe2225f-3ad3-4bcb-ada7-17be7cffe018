import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { WithSitecoreContextDecorator } from "@uxbee/storybook-mock-sc";
import { EPEventCountdownComponent } from "rai-exhibitor-portal";
import { EventCountdownData } from "./data/ep-event-countdown.data";

const meta: Meta<typeof EPEventCountdownComponent.Default> = {
    title: "RAI/Exhibitor Portal/Canvas/EPEventCountdownComponent",
    component: EPEventCountdownComponent.Default,
    tags: ["autodocs", "rai", "ep"],
    argTypes: {},
    decorators: [
        (Story, context) =>
            WithSitecoreContextDecorator(
                () => (
                    <div className="h-[30rem] bg-[#fafafa]">
                        <div className="grid grid-cols-12 gap-4">
                            <Story />
                        </div>
                    </div>
                ),
                context,
                EPEventCountdownComponent.Default,
                false
            ),
    ],
};

export default meta;

type Story = StoryObj<typeof EPEventCountdownComponent.Default>;

export const Default: Story = {
    // args: LabelFooterData,
    args: EventCountdownData
};

import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { Modal, Pagination, PaginationContent, PaginationEllipsis, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "ui";
import { useToggle } from "ui";
import { useI18n } from 'next-localization';
import parse from 'html-react-parser';

const meta: Meta<typeof Modal> = {
  title: "ui/Atomic/Molecules/Pagination/Pagination",
  component: Modal,
  tags: ["autodocs", "ui", "atoms", "modal"],
  parameters: {},
  argTypes: {
    
  },
};
export default meta;

type Story = StoryObj<typeof Modal>;

export const Default: Story = {
  render: (args) => {

    return (
      <div className="h-[40rem]">
        <Pagination>
        <PaginationContent>
          <PaginationItem>
            <PaginationPrevious href="#" />
          </PaginationItem>
          <PaginationItem>
            <PaginationLink href="#">1</PaginationLink>
          </PaginationItem>
          <PaginationItem>
            <PaginationLink href="#" isActive>
              2
            </PaginationLink>
          </PaginationItem>
          <PaginationItem>
            <PaginationLink href="#">3</PaginationLink>
          </PaginationItem>
          <PaginationItem>
            <PaginationEllipsis />
          </PaginationItem>
          <PaginationItem>
            <PaginationNext href="#" />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
      </div>
    );
  },
  args: {
  },
};

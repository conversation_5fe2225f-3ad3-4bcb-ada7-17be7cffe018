[CmdletBinding()]
param(
    [string]
    $ProjectName,
    [Parameter(ValueFromPipelineByPropertyName)]
    [ValidateSet(
        '*',
        'test',
        'acceptation',
        'production'
    )]
    [string]
    $EnvironmentName
)
Push-Location $PSScriptRoot

$CommandDir = Get-Location
Push-Location -Path ..
$ContextDir = Get-Location
Pop-Location
. $CommandDir/.powershell/Foundation.Sitecore.XMCloud.ps1

$UserFile = Join-Path $ContextDir "/.sitecore/user.json"

$IsAuthenticated = Confirm-XMCloud-Login -UserFile $UserFile

if($IsAuthenticated) {
    if($EnvironmentName -eq '*') {
        $projectId = Get-Project-Id-by-Name -ProjectName $ProjectName
        if($projectId) {
            dotnet sitecore cloud environment list --project-id $projectId
        } else {
            Write-Host "Project $ProjectName could not be found" -ForegroundColor Red
        }
    } else {
        $environmentId = Get-Environment-Id-by-Name -ProjectName $ProjectName -EnvironmentName $EnvironmentName
        if($environmentId) {
            dotnet sitecore cloud environment info --environment-id $environmentId
        } else {
            Write-Host "Environment $EnvironmentName clould not be found in project $ProjectName" -ForegroundColor Red
        }
    }
} else {
    Write-Host "User is not authenticated, no actions can be executed" -ForegroundColor Red
}
Pop-Location
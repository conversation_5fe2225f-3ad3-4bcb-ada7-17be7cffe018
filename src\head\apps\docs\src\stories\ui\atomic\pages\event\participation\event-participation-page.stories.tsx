import type { Meta, StoryObj } from "@storybook/react";
import { EventParticipationPage } from "ui";
const meta: Meta<typeof EventParticipationPage> = {
  title: "ui/Atomic/Pages/Event/EventParticipationPage",
  component: EventParticipationPage,
  tags: ["autodocs", "ui", "molecules", "home-page"],
  parameters: {},
  argTypes: {},
};
export default meta;
type Story = StoryObj<typeof EventParticipationPage>;
export const Schema: Story = {
  render: (args) => {
    return (
      <div className="h-[40rem]">
        <EventParticipationPage {...args} />
      </div>
    );
  },
  args: {},
};

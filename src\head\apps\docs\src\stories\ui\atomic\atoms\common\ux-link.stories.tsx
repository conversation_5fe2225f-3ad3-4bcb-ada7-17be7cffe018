import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { UxLink } from "ui";

// More on how to set up stories at: https://storybook.js.org/docs/react/writing-stories/introduction
const meta: Meta<typeof UxLink> = {
  title: "ui/Atomic/Atoms/Common/Link",
  component: UxLink,
  tags: ["autodocs", "ui", "atoms","common"],
  parameters: {
  },
  argTypes: {
  },
};

export default meta;

type Story = StoryObj<typeof UxLink>;

export const DefaultNoIcon: Story = {
  args: {    
    id: '1',
    children: 'Service',    
    disabled: false,
    selected: false,
    href: 'https://www.uxbee.nl'
  },
};


export const Default: Story = {
  args: {   
    id: '1',
    children: 'Service',
    iconName: 'Service',
    disabled: false,
    selected: false,
    href: 'https://www.uxbee.nl'
  },
};


export const DefaultSelected: Story = {
  args: {    
    id: '1',
    children: 'Service',
    iconName: 'Service',
    disabled: false,
    selected: true,
    href: 'https://www.uxbee.nl'
  },
};


export const DefaultDisabled: Story = {
  args: {    
    id: '1',
    children: 'Service',
    iconName: 'Service',
    disabled: true,
    selected: false,
    href: 'https://www.uxbee.nl'
  },
};



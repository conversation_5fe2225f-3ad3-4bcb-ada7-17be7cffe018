import { createGraphQLClientFactory } from 'lib/graphql-client-factory/create';
import { JssConfig } from 'lib/config';
import { InitializeWildcardsPluginCore } from 'wildcards';
import { ConfigPlugin } from '..';

const LOCALES = ['en'];

/**
 * This plugin will set the "wildcardData" config prop.
 * By default this will attempt to fetch wildcard information directly from Sitecore (using the GraphQLWildcardInfoService).
 * You could easily modify this to fetch from another source such as a static JSON file instead.
 */
class WildcardsPlugin implements ConfigPlugin {
  order = 1000;

  async exec(config: JssConfig) {
    const pluginCore = new InitializeWildcardsPluginCore({
      clientFactory: createGraphQLClientFactory(config),
      locales: LOCALES,
    });

    const wildcardData = await pluginCore.getWildcards();

    return Object.assign({}, config, {
      wildcardData: JSON.stringify(wildcardData),
    });
  }
}

export const wildcardsPlugin = new WildcardsPlugin();

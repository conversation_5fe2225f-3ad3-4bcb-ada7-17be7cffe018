import type { <PERSON>a, StoryObj } from "@storybook/react";
import { WithSitecoreContextDecorator } from "@uxbee/storybook-mock-sc/lib/WithSitecoreContextDecorator";
import { useI18n } from "next-localization";
import { useToggle } from "ui";
import { DeleteAccountComponent } from "visitor-portal";

// More on how to set up stories at: https://storybook.js.org/docs/react/writing-stories/introduction
const meta: Meta<typeof DeleteAccountComponent.Default> = {
  title: "RAI/Visitor Portal/Canvas/DeleteAccount",
  component: DeleteAccountComponent.Default,
  argTypes: {},
  decorators: [
    (Story, context) => {
      const [open, handleOpen] = useToggle();
      const { t } = useI18n();

      return WithSitecoreContextDecorator(
        () => (
          <div className="h-[40rem]">
            <button
              onClick={() => handleOpen()}
              className="px-4 py-2 text-red-500 rounded-md"
            >
              Delete Account
            </button>
            {open && (
              <Story args={{ handleOpenDeleteAccountModal: handleOpen }} />
            )}
          </div>
        ),
        context,
        DeleteAccountComponent,
        false
      );
    },
  ],
};

export default meta;

type Story = StoryObj<typeof DeleteAccountComponent.Default>;

export const Default: Story = {
  args: {
    
  },
};

using namespace System.Text.RegularExpressions
function Read-JSON-From-File() {
    param(
        [string]
        [ValidateNotNullOrEmpty()]
        $JSONFile
    )
    if (Test-Path -Path $JSONFile -PathType Leaf) {
        return Get-Content $JSONFile -Encoding UTF8 | Out-String | ConvertFrom-Json
    } else{
        Write-Host "JSON file ($JSONFile) could not be found." -ForegroundColor Red
        EXIT
    }
}

function Write-JSON-To-File() {
    param(
        [string]
        [ValidateNotNullOrEmpty()]
        $JSONFile,
        [object]
        $JSON
    )

    # recreate the object as array, and use the -Depth parameter (your json needs 3 minimum)
    ConvertTo-Json $JSON -Depth 100 | Format-JSON | ForEach-Object { [Regex]::Replace($_, "\\u(?<Value>[a-zA-Z0-9]{4})", { param($m) ([char]([int]::Parse($m.Groups['Value'].Value, [System.Globalization.NumberStyles]::HexNumber))).ToString() } )}  | Set-Content $JSONFile -Encoding UTF8
    Remove-Bom-From-File -Path $JSONFile -Destination $JSONFile
}

function Remove-Bom-From-File {
    <#
    .SYNOPSIS
        To remove BOM (byte order marking) from a file.
    .DESCRIPTION
        To remove BOM (byte order marking) from a file.
    .PARAMETER Path
        The path to the source file.
    .PARAMETER Destination
        The path to the destination file.
    .EXAMPLE
        Remove-BomFromFiles -Path .\UTF8-BOM.txt -Destination .\UTF.txt
     
        Takes the contents of .\UTF8-BOM.txt, removes the byte order marking
        and writes to .\UTF8.txt
    #>
    
        [CmdletBinding()]
        param
        (
            [Alias('OldPath')]
            [string] $Path,
    
            [Alias('NewPath')]
            [string] $Destination
        )
    
        $Content = Get-Content -Path $Path -Raw
        $Utf8NoBomEncoding = New-Object -TypeName System.Text.UTF8Encoding -ArgumentList $False
        [System.IO.File]::WriteAllLines($Destination, $Content, $Utf8NoBomEncoding)
    }

function Format-JSON {
    <#
    .SYNOPSIS
        Prettifies JSON output.
    .DESCRIPTION
        Reformats a JSON string so the output looks better than what ConvertTo-Json outputs.
    .PARAMETER Json
        Required: [string] The JSON text to prettify.
    .PARAMETER Minify
        Optional: Returns the json string compressed.
    .PARAMETER Indentation
        Optional: The number of spaces (1..1024) to use for indentation. Defaults to 4.
    .PARAMETER AsArray
        Optional: If set, the output will be in the form of a string array, otherwise a single string is output.
    .EXAMPLE
        $json | ConvertTo-Json  | Format-Json -Indentation 2
    #>
    [CmdletBinding(DefaultParameterSetName = 'Prettify')]
    Param(
        [Parameter(Mandatory = $true, Position = 0, ValueFromPipeline = $true)]
        [string]$Json,

        [Parameter(ParameterSetName = 'Minify')]
        [switch]$Minify,

        [Parameter(ParameterSetName = 'Prettify')]
        [ValidateRange(1, 1024)]
        [int]$Indentation = 2,

        [Parameter(ParameterSetName = 'Prettify')]
        [switch]$AsArray
    )

    if ($PSCmdlet.ParameterSetName -eq 'Minify') {
        return ($Json | ConvertFrom-Json) | ConvertTo-Json -Depth 100 -Compress
    }

    # If the input JSON text has been created with ConvertTo-Json -Compress
    # then we first need to reconvert it without compression
    if ($Json -notmatch '\r?\n') {
        $Json = ($Json | ConvertFrom-Json) | ConvertTo-Json -Depth 100
    }

    $indent = 0
    $regexUnlessQuoted = '(?=([^"]*"[^"]*")*[^"]*$)'

    $result = $Json -split '\r?\n' |
        ForEach-Object {
            # If the line contains a ] or } character, 
            # we need to decrement the indentation level unless it is inside quotes.
            if ($_ -match "[}\]]$regexUnlessQuoted") {
                $indent = [Math]::Max($indent - $Indentation, 0)
            }

            # Replace all colon-space combinations by ": " unless it is inside quotes.
            $line = (' ' * $indent) + ($_.TrimStart() -replace ":\s+$regexUnlessQuoted", ': ')

            # If the line contains a [ or { character, 
            # we need to increment the indentation level unless it is inside quotes.
            if ($_ -match "[\{\[]$regexUnlessQuoted") {
                $indent += $Indentation
            }

            $line
        }
    return $result
    if ($AsArray) { return $result }
    return $result -Join [Environment]::NewLine
}

#region manipulation of json files
function Update-JSON-Scalar-Value() {
    param(
        [ValidateNotNullOrEmpty()]
        [string]
        $JSONFile,
        [ValidateNotNullOrEmpty()]
        [string]
        $Property,
        [object]
        $Value
    )
    # Read the json file
    $JSON = Read-JSON-From-File -JSONFile $JSONFile
    # Set the value
    Set-Property -Json $JSON -Path ($Property -split "\.") -Value $Value
    # Store the new JSON file
    Write-JSON-To-File -JSONFile $JSONFile -JSON $JSON
}

function Update-JSON-Array-Value() {
    param(
        [ValidateNotNullOrEmpty()]
        [string]
        $JSONFile,
        [ValidateNotNullOrEmpty()]
        [string]
        $Property,
        [bool]
        $MultiProperty = $true,
        [string]
        $Value
    )
    # Read the json file
    $JSON = Read-JSON-From-File -JSONFile $JSONFile
    # get the current value
    if($MultiProperty) {
        $NewValue = Get-Property -Json $JSON -Path ($Property -split "\.").Split(',')
    } else {
        $NewValue = Get-Property -Json $JSON -Path @($Property)
    }
    # Handle absent property
    if(($null -eq $NewValue)) {
        $NewValue = @()
    }
    #Handle single value property
    if($NewValue.GetType().ToString() -eq "System.String") {
        $NewValue = @($NewValue)
    }
    # Check if the value already exists
    if(!($Value -in $NewValue)) {
        $NewValue += $Value
    }
    # Set the value
    if($MultiProperty) {
        Set-Property -Json $JSON -Path ($Property -split "\.") -Value @($NewValue)
    } else {
        Set-Property -Json $JSON -Path @($Property) -Value @($NewValue)
    }
    # Store the new JSON file
    Write-JSON-To-File -JSONFile $JSONFile -JSON $JSON
}

function Remove-JSON-Entry() {
    param(
        [ValidateNotNullOrEmpty()]
        [string]
        $JSONFile,
        [ValidateNotNullOrEmpty()]
        [string]
        $Property
    )
    # Read the json file
    $JSON = Read-JSON-From-File -JSONFile $JSONFile
    # Check if the entry exits
    Remove-Property -Json $JSON -Path ($Property -split "\.")
    # Store the new JSON file
    Write-JSON-To-File -JSONFile $JSONFile -JSON $JSON
}

function Set-Property {
    param(
        [ValidateNotNullOrEmpty()]
        [object]
        $Json,
        [ValidateNotNull()]
        [array]
        $Path,
        [ValidateNotNull()]
        [object]
        $Value = "",
        [switch]
        $Force,
        [char]
        $escapeChar = '#'
    )
    process {
        $obj = $Json
        # loop all but the very last property
        for ($x = 0; $x -lt $Path.count -1; $x ++) {
            $propName = $Path[$x] -replace $escapeChar, '.'
            if (!($obj | Get-Member -MemberType NoteProperty -Name $propName)) {
                $obj | Add-Member NoteProperty -Name $propName -Value (New-Object PSCustomObject) -Force:$Force.IsPresent
            }
            $obj = $obj.$propName
        }
        $propName = ($Path | Select-Object -Last 1) -replace $escapeChar, '.'
        if (!($obj | Get-Member -MemberType NoteProperty -Name $propName)) {
            $obj | Add-Member NoteProperty -Name $propName -Value $Value -Force:$Force.IsPresent
        } else {
            $obj.$PropName = $Value
        }
    }
}

function Get-Property() {
    param(
        [ValidateNotNullOrEmpty()]
        [object]
        $Json,
        [ValidateNotNull()]
        [array]
        $Path
    )
    $first, $rest = $Path
    if ($rest) {
        Get-Property -Json $Json.$first -Path $rest -Value $Value
    } else {
        return $Json.$first
    }
}

function Remove-Property() {
    param(
        [ValidateNotNullOrEmpty()]
        [object]
        $Json,
        [ValidateNotNull()]
        [array]
        $Path,
        [char]
        $escapeChar = '#'
    )
    $obj = $Json
    for ($x = 0; $x -lt $Path.count -1; $x ++) {
        $propName = $Path[$x] -replace $escapeChar, '.'
        $obj = $obj.$propName
    }
    $propName = ($Path | Select-Object -Last 1) -replace $escapeChar, '.'
    if ($obj | Get-Member -MemberType NoteProperty -Name $propName) {
        $obj.PSObject.Properties.Remove($propName)
    }
}
#endregion
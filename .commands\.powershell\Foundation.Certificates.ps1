function Install-Root-Certificate {
	param(
        [string]$ProjectPath = $pwd,
        [string]$CertFolder = (Join-Path $ProjectPath "\docker\traefik\certs"),
        [string]$RootCAName = "RootCA"
	)
	$rootCAFileCRT = (Join-Path $CertFolder "$RootCAName.crt")
    $rootCAFileKey = (Join-Path $CertFolder "$RootCAName.key")
	if (!(Test-Path -Path $rootCAFileCRT -PathType Leaf)) {
		# Create Root Certificate file
		$rootKey = Create-RSAKey -KeyLength 4096
		Create-KeyFile -Key $rootKey -OutKeyPath $rootCAFileKey
		$rootCertificate = Create-SelfSignedCertificate -Key $rootKey
		Create-CertificateFile -Certificate $rootCertificate -OutCertPath $rootCAFileCRT
		#importing the root certificate
		Import-Certificate -FilePath $rootCAFileCRT -CertStoreLocation "Cert:\LocalMachine\Root"

        Write-Host "Root certificate was created and installed into Cert:\LocalMachine\Root" -ForegroundColor DarkYellow
	} else {
        Write-Host "Root cretificate was found skipping the creation and installation" -ForegroundColor DarkYellow
    }
}

function Uninstall-Root-Certificate {
    param(
        [string]$ProjectPath = $pwd,
        [string]$CertFolder = (Join-Path $ProjectPath "\docker\traefik\certs"),
        [string]$RootCAName = "RootCA"
	)
	$rootCAFileCRT = (Join-Path $CertFolder "$RootCAName.crt")
	if (Test-Path -Path $rootCAFileCRT -PathType Leaf) {
		$cert = New-Object System.Security.Cryptography.X509Certificates.X509Certificate2
		$cert.Import($rootCAFileCRT)
		$thumbprint = $cert.Thumbprint
		if($thumbprint) {
			Write-Host "Uninstalling root certificate from certificate store" -ForegroundColor DarkYellow
            try {
			    Get-ChildItem Cert:\LocalMachine\Root\$thumbprint | Remove-Item
            } catch {
                Write-Host "Certificate could not be removed from the certificate store" -ForegroundColor Red
            }
		} else {
			Write-Host "Root certificate not found, it was not removed!" -ForegroundColor DarkYellow
		}
	} else {
		Write-Host "Root certificate not found, it was not removed!" -ForegroundColor DarkYellow
	}
}

function Add-Certificate {
	param(
        [string]$HostName,
        [string]$ProjectPath = $pwd,
        [string]$CertFolder = (Join-Path $ProjectPath "\docker\traefik\certs"),
        [string]$RootCAName = "RootCA"
	)
    # Ensure that the Cert folder exits
    if(!(Test-Path -PathType container -Path $CertFolder)) {
        New-Item -ItemType "directory" -Path $CertFolder
    }    

	$PublicKey = (Join-Path $CertFolder "$RootCAName.crt")
    $PrivateKey = (Join-Path $CertFolder "$RootCAName.key")
	
	if (!(Test-Path -Path $PublicKey -PathType Leaf)) {
		Write-Host "Root certificate is missing, creating a new root certificate" -ForegroundColor DarkYellow
        Install-Root-Certificate -CertFolder $CertFolder -RootCAName $RootCAName
	}
	$fileName = $HostName.replace('*.','wildcard.')

    $HostBlocks = $HostName.Split('.')
    $HostBlock = $HostBlocks[0]
    if($HostBlock -eq "*") {
        $HostBlock = "\*"
    }
    $WildcardName = $HostName -replace "$HostBlock.","wildcard."
    if($HostBlocks.Length -ge 3) {
        # Ensure that only wildcards are created
        $fileName = $WildcardName
        $HostName = $HostName -replace "$HostBlock.","*."
    }
    $WildcardCertificate = Join-Path $CertFolder "$WildcardName.crt"
    $HostCertificate = Join-Path $CertFolder "$HostName.crt"
    if (!(Test-Path -Path $WildcardCertificate -PathType Leaf) -AND !(Test-Path -Path $HostCertificate -PathType Leaf) ) {
        # If the certificate is missing, create a specic certificate
        Write-Host "Loading the root certificate" -ForegroundColor DarkYellow
		$rootCertificate = Convert-PemToPfx -InputPath $PublicKey -KeyPath $PrivateKey
		$selfSignedKey = Create-RSAKey
		$certificate = Create-SelfSignedCertificateWithSignature -Key $selfSignedKey -CommonName $HostName -DnsName $HostName -RootCertificate $rootCertificate
		Create-KeyFile -Key $selfSignedKey -OutKeyPath "$CertFolder\$fileName.key"
		Create-CertificateFile -Certificate $certificate -OutCertPath "$CertFolder\$fileName.crt"
		Write-Host "Certificate for $HostName is created" -ForegroundColor DarkYellow
        Update-Traefik-CertsConfigFile -ProjectPath $ProjectPath -CertFolder $CertFolder -RootCAName $RootCAName
	} else {
        Write-Host "Certificate for $HostName already exits, creation is skipped" -ForegroundColor DarkYellow
    }
}

function Join-Certificate-With-Root {
    param(
        [string]$CertName,
        [string]$RootCAName = "RootCA",
        [string]$ProjectPath = $pwd,
        [string]$CertFolder = (Join-Path $ProjectPath "\docker\traefik\certs")
	)
    $CertFileName = -join($CertName, '.crt');
    $CertFileLocation = (Join-Path $CertFolder $CertFileName);
    $RootFileName = -join($RootCAName, '.crt');
    $RootFileLocation = (Join-Path $CertFolder $RootFileName);
    $CombinedFileName = -join($CertName, '-combined.crt');
    $CombinedFileLocation = (Join-Path $CertFolder $CombinedFileName);
    if (!(Test-Path -Path $CombinedFileLocation -PathType Leaf)) {
        Write-Host "Combined certificate for $CertName is missing, creating a new one." -ForegroundColor DarkYellow
        Get-Content $CertFileLocation, $RootFileLocation | Set-Content $CombinedFileLocation
    } else {
        Write-Host "Combined certificate for $CertName is found, skipping creation." -ForegroundColor DarkYellow
    }
}

function Update-Traefik-CertsConfigFile {
	param(
        [string]$ProjectPath = $pwd,
		[string]$CertFolder = (Join-Path $ProjectPath "\docker\traefik\certs"),
        [string]$RootCAName = "RootCA",
        [string]$CertsConfigFile =  (Join-Path $ProjectPath "\docker\traefik\config\dynamic\certs_config.yaml")
    )
	$certificatePath = "C:\etc\traefik\certs\"
    If(!(Test-Path -PathType Leaf -Path $CertsConfigFile)) {
        New-Item $CertsConfigFile -Force
    }
    

	$existingCertificateFiles = Get-ChildItem "$CertFolder\*" -Exclude "$RootCAName.*", "readme", "*.key", ".gitignore", ".gitkeep" -Name
	if ($existingCertificateFiles){

		$newFileContent = @("tls:", "  certificates:")

		foreach ($certificateFilename in $existingCertificateFiles) {
				$certificateName = $certificateFilename -replace '.crt', ''
				$newFileContent +=  "    - certFile: " + $certificatePath + $certificateName + ".crt"
				$newFileContent +=  "      keyFile: " + $certificatePath + $certificateName + ".key"
		}

		# Clear certs_config.yaml file
		Clear-Content -Path $CertsConfigFile

		# Setting new content to the certs_config.yaml file
		$newFileContent | Set-Content $CertsConfigFile

		Write-Host "certs_config.yaml file was successfully updated." -ForegroundColor DarkYellow
	}
}

Add-Type @"
namespace System.Security.Cryptography.X509Certificates {
    public enum X509KeySpecFlags {
        None = 0,
        AT_KEYEXCHANGE = 1,
        AT_SIGNATURE = 2
    }
}
"@
Add-Type -Path "$PSScriptRoot/SysadminsLV.Asn1Parser.dll"
function Convert-PemToPfx {
[OutputType('[System.Security.Cryptography.X509Certificates.X509Certificate2]')]
[CmdletBinding()]
    param(
        [Parameter(Mandatory = $true, Position = 0)]
        [string]$InputPath,
        [string]$KeyPath,
        [string]$OutputPath,
        [Security.Cryptography.X509Certificates.X509KeySpecFlags]$KeySpec = "AT_KEYEXCHANGE",
        [Security.SecureString]$Password,
        [string]$ProviderName = "Microsoft Enhanced RSA and AES Cryptographic Provider",
        [Security.Cryptography.X509Certificates.StoreLocation]$StoreLocation = "CurrentUser",
        [switch]$Install
    )
    if ($PSBoundParameters.Verbose) {$VerbosePreference = "continue"}
    if ($PSBoundParameters.Debug) {
        $Host.PrivateData.DebugForegroundColor = "Cyan"
        $DebugPreference = "continue"
    }

    #region helper functions
    function __normalizeAsnInteger ($array) {
        $padding = $array.Length % 8
        if ($padding) {
            $array = $array[$padding..($array.Length - 1)]
        }
        [array]::Reverse($array)
        [Byte[]]$array
    }
    function __extractCert([string]$Text) {
        if ($Text -match "(?msx).*-{5}BEGIN\sCERTIFICATE-{5}(.+)-{5}END\sCERTIFICATE-{5}") {
        $keyFlags = [Security.Cryptography.X509Certificates.X509KeyStorageFlags]::Exportable
        if ($Install) {
            if ($StoreLocation -eq "CurrentUser") {
               $keyFlags = $keyFlags -bor [Security.Cryptography.X509Certificates.X509KeyStorageFlags]::UserKeySet
            } else {
               $keyFlags = $keyFlags -bor [Security.Cryptography.X509Certificates.X509KeyStorageFlags]::MachineKeySet
            }
        }
        $RawData = [Convert]::FromBase64String($matches[1])
            try {
                New-Object Security.Cryptography.X509Certificates.X509Certificate2 -ArgumentList $RawData, "", $keyFlags
            } catch {throw "The data is not valid security certificate."}
            Write-Debug "X.509 certificate is correct."
        } else {throw "Missing certificate file."}
    }
    # returns [byte[]]
    function __composePRIVATEKEYBLOB($modulus, $PublicExponent, $PrivateExponent, $Prime1, $Prime2, $Exponent1, $Exponent2, $Coefficient) {
        Write-Debug "Calculating key length."
        $bitLen = "{0:X4}" -f $($modulus.Length * 8)
        Write-Debug "Key length is $($modulus.Length * 8) bits."
        [byte[]]$bitLen1 = Invoke-Expression 0x$($bitLen.Substring(0,2))
        [byte[]]$bitLen2 = Invoke-Expression 0x$($bitLen.Substring(2,2))
        [Byte[]]$PrivateKey = 0x07,0x02,0x00,0x00,0x00,0x24,0x00,0x00,0x52,0x53,0x41,0x32,0x00
        [Byte[]]$PrivateKey = $PrivateKey + $bitLen1 + $bitLen2 + $PublicExponent + ,0x00 + `
            $modulus + $Prime1 + $Prime2 + $Exponent1 + $Exponent2 + $Coefficient + $PrivateExponent
        $PrivateKey
    }
    # returns RSACryptoServiceProvider for dispose purposes
    function __attachPrivateKey($Cert, [Byte[]]$PrivateKey) {
        $cspParams = New-Object Security.Cryptography.CspParameters -Property @{
            ProviderName = $ProviderName
            KeyContainerName = "pspki-" + [Guid]::NewGuid().ToString()
            KeyNumber = [int]$KeySpec
        }
        if ($Install -and $StoreLocation -eq "LocalMachine") {
            $cspParams.Flags = $cspParams.Flags -bor [Security.Cryptography.CspProviderFlags]::UseMachineKeyStore
        }
        $rsa = New-Object Security.Cryptography.RSACryptoServiceProvider $cspParams
        $rsa.ImportCspBlob($PrivateKey)
        $Cert.PrivateKey = $rsa
        $rsa
    }
    # returns Asn1Reader
    function __decodePkcs1($base64) {
        Write-Debug "Processing PKCS#1 RSA KEY module."
        $asn = New-Object SysadminsLV.Asn1Parser.Asn1Reader @(,[Convert]::FromBase64String($base64))
        if ($asn.Tag -ne 48) {throw "The data is invalid."}
        $asn
    }
    # returns Asn1Reader
    function __decodePkcs8($base64) {
        Write-Debug "Processing PKCS#8 Private Key module."
        $asn = New-Object SysadminsLV.Asn1Parser.Asn1Reader @(,[Convert]::FromBase64String($base64))
        if ($asn.Tag -ne 48) {throw "The data is invalid."}
        # version
        if (!$asn.MoveNext()) {throw "The data is invalid."}
        # algorithm identifier
        if (!$asn.MoveNext()) {throw "The data is invalid."}
        # octet string
        if (!$asn.MoveNextCurrentLevel()) {throw "The data is invalid."}
        if ($asn.Tag -ne 4) {throw "The data is invalid."}
        if (!$asn.MoveNext()) {throw "The data is invalid."}
        $asn
    }
    #endregion
    $ErrorActionPreference = "Stop"
    
    #$File = Get-Item $InputPath -Force -ErrorAction Stop
    if ($KeyPath) {$Key = Get-Item $KeyPath -Force -ErrorAction Stop}
    
    # parse content
    $Text = Get-Content -Path $InputPath -Raw -ErrorAction Stop
    Write-Debug "Extracting certificate information..."
    $Cert = __extractCert $Text
    if ($Key) {$Text = Get-Content -Path $KeyPath -Raw -ErrorAction Stop}
    $asn = if ($Text -match "(?msx).*-{5}BEGIN\sPRIVATE\sKEY-{5}(.+)-{5}END\sPRIVATE\sKEY-{5}") {
        __decodePkcs8 $matches[1]
    } elseif ($Text -match "(?msx).*-{5}BEGIN\sRSA\sPRIVATE\sKEY-{5}(.+)-{5}END\sRSA\sPRIVATE\sKEY-{5}") {
        __decodePkcs1 $matches[1]
    }  else {throw "The data is invalid."}
    # private key version
    if (!$asn.MoveNext()) {throw "The data is invalid."}
    # modulus n
    if (!$asn.MoveNext()) {throw "The data is invalid."}
    $modulus = __normalizeAsnInteger $asn.GetPayload()
    Write-Debug "Modulus length: $($modulus.Length)"
    # public exponent e
    if (!$asn.MoveNext()) {throw "The data is invalid."}
    # public exponent must be 4 bytes exactly.
    $PublicExponent = if ($asn.GetPayload().Length -eq 3) {
        ,0 + $asn.GetPayload()
    } else {
        $asn.GetPayload()
    }
    Write-Debug "PublicExponent length: $($PublicExponent.Length)"
    # private exponent d
    if (!$asn.MoveNext()) {throw "The data is invalid."}
    $PrivateExponent = __normalizeAsnInteger $asn.GetPayload()
    Write-Debug "PrivateExponent length: $($PrivateExponent.Length)"
    # prime1 p
    if (!$asn.MoveNext()) {throw "The data is invalid."}
    $Prime1 = __normalizeAsnInteger $asn.GetPayload()
    Write-Debug "Prime1 length: $($Prime1.Length)"
    # prime2 q
    if (!$asn.MoveNext()) {throw "The data is invalid."}
    $Prime2 = __normalizeAsnInteger $asn.GetPayload()
    Write-Debug "Prime2 length: $($Prime2.Length)"
    # exponent1 d mod (p-1)
    if (!$asn.MoveNext()) {throw "The data is invalid."}
    $Exponent1 = __normalizeAsnInteger $asn.GetPayload()
    Write-Debug "Exponent1 length: $($Exponent1.Length)"
    # exponent2 d mod (q-1)
    if (!$asn.MoveNext()) {throw "The data is invalid."}
    $Exponent2 = __normalizeAsnInteger $asn.GetPayload()
    Write-Debug "Exponent2 length: $($Exponent2.Length)"
    # coefficient (inverse of q) mod p
    if (!$asn.MoveNext()) {throw "The data is invalid."}
    $Coefficient = __normalizeAsnInteger $asn.GetPayload()
    Write-Debug "Coefficient length: $($Coefficient.Length)"
    # creating Private Key BLOB structure
    $PrivateKey = __composePRIVATEKEYBLOB $modulus $PublicExponent $PrivateExponent $Prime1 $Prime2 $Exponent1 $Exponent2 $Coefficient
    #region key attach and export routine
    $rsaKey = __attachPrivateKey $Cert $PrivateKey
    if (![string]::IsNullOrEmpty($OutputPath)) {
        if (!$Password) {
            $Password = Read-Host -Prompt "Enter PFX password" -AsSecureString
        }
        $pfxBytes = $Cert.Export("pfx", $Password)
        Set-Content -Path $OutputPath -Value $pfxBytes -Encoding Byte
    }
    #endregion
    if ($Install) {
        $store = New-Object Security.Cryptography.X509Certificates.X509Store "my", $StoreLocation
        $store.Open("ReadWrite")
        $store.Add($Cert)
        $store.Close()
    }
    $rsaKey.Dispose()
    $Cert
}
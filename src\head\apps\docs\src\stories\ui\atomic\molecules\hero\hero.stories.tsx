import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { Hero } from "ui";

const meta: Meta<typeof Hero> = {
  title: "ui/Atomic/Molecules/Hero/Hero",
  component: Hero,
  tags: ["autodocs", "ui", "molecules", "navigation"],
  parameters: {
  },
  argTypes: {},
};

export default meta;

type Story = StoryObj<typeof Hero>;
export const Schema: Story = {
  render: (args) => {
    return (
        <div className="h-min-[20rem] px-4 py-10 bg-neutral-50">
          <Hero {...args} />
        </div>
    );
  },

  args: {
    hero: {
      title: "Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
      image: "hero-image-02.jpg",
    },
  },
  
};

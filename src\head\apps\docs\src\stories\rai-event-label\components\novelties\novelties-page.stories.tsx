import type { Meta, StoryObj } from "@storybook/react";
import { WithSitecoreContextDecorator } from "@uxbee/storybook-mock-sc";
import { Default as NoveltiesPage } from "rai-event-label";

const meta: Meta<typeof NoveltiesPage> = {
    title: "RAI/Event Label/Novelties/Novelties Page",
    component: NoveltiesPage,
    tags: ["autodocs", "rai", "ep"],
    args: {
        loadNext: async (hostname: string, query: string,filter: string,category: string, page: number) => {
            console.log("LOAD ==>", hostname, query, filter, category, page);
            return Array.from({ length: 20 }, (_, index) => ({
                image: "https://ep.rai.nl/noviteiten/popup/img?inn=47a06dc0-92b8-4d1d-bc99-f37e30e71629&vraag=17&hires=1",
                title: `${filter && `${filter} - `}Limited Edition Collection ${index}`,
                description: "<PERSON><PERSON><PERSON><PERSON><PERSON>, a cutting-edge and patented technology, offering a complete range of intelligent data throughout the anchoring procedure immediately recognizing anchor drag, thus minimizing the risk of groundings, collisions and environmental damage. We move todays anchoring from reactive to proactive.",
                category: "security & safety aboard",
                introductionDate: "15/11/2023",
                productNewArea: "the World",
                url: "https://company.metstrade.com/?a=PKiHoC7XqL6TjWrtOWV0t37VBQ4bVDT1lGkYd6%2BlVjGBeOFF8fQU%2BEvk0guxwcYwmFcX5cDaAhzlFmK3nKC1Vmebn6ey4gouL1VmqY3gGVIEvpsc4ZFaPUAFu0KkR5PO",
                status: ["winner", "nominated"],
                company: {
                  url: "https://rai.nl",
                  name: "Swiss Ocean Tech Ltd.",
                  logo: "https://ep.rai.nl/mypages/en/Home/ShowLogo?docid=106215&width=190&height=120",
                },
              }))
        },
        firstLetterFilter: ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"],
        categoryFilters: [
            "Sample Cateory for Filter",
            "Marine Life",
            "Defense and Protection Indsutry",
            "Education for Kids",
        ],
    },
    argTypes: {},
    decorators: [
        (Story, context) =>
            WithSitecoreContextDecorator(
                () => (
                    <div className="min-h-screen bg-white">
                        <Story />
                    </div>
                ),
                context,
                NoveltiesPage,
                false
            ),
    ],
};

export default meta;

type Story = StoryObj<typeof NoveltiesPage>;

export const Default: Story = {
    args: {
        config: {
            isShowSearchField: true,
            isShowFilterLetter: true,
            variant: 'default',
            searchFieldWidth: {
                value: 700,
                unit: "px"
            }
        }
    }
}

export const FilterWithCategory: Story = {
    args: {
        config: {
            isShowSearchField: true,
            isShowFilterLetter: true,
            variant: 'filter-with-category',
            searchFieldWidth: {
                value: 700,
                unit: "px"
            }
        }
    }
}
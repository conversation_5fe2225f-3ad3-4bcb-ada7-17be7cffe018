import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { NewsDetailTableOfContent } from "ui-sitecore";
import articleContent from "./articles/news-article.md";
import ReactMarkdown from "react-markdown";

const meta: Meta<typeof NewsDetailTableOfContent.Default> = {
  title: "ui-sitecore/components/structure/TableOfContents",
  component: NewsDetailTableOfContent.Default,
  tags: ["autodocs"],
  args: {
  },
  parameters: {
    actions: { argTypesRegex: "^on.*" },
    rootAttributesTooltip: true,
  },
};

export default meta;

type Story = StoryObj<typeof NewsDetailTableOfContent.Default>;

export const Default: Story = {
  render: (args) => {
    return (
      <div className="flex flex-row space-x-2 items-start">
        <NewsDetailTableOfContent.Default />
        <div id="news-detail" className="ml-8 space-y-6 max-w-2xl prose">
          <ReactMarkdown>{articleContent}</ReactMarkdown>
        </div>
      </div>
    );
  },
};

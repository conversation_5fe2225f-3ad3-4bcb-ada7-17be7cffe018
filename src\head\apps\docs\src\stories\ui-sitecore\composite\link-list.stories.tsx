import { JSX } from "react";
import type { Meta, StoryObj } from "@storybook/react";
import { LinkListComponent } from "ui-sitecore";
import { WithSitecoreContextDecorator } from "@uxbee/storybook-mock-sc/lib/WithSitecoreContextDecorator";

// const componentsList: Map<string, any> = new Map();
// componentsList.set('ContentBlock', scContentBlock);

type StoryType = () => JSX.Element;

type ContextInterface = Record<string, unknown>;
// More on how to set up stories at: https://storybook.js.org/docs/react/writing-stories/introduction
const meta: Meta<typeof LinkListComponent.Default> = {
  title: "ui-sitecore/components/composite/LinkList",
  component: LinkListComponent.Default,
  tags: ["autodocs", "ui-sitecore", "sitecore"],
  argTypes: {},
  decorators: [
    (Story: StoryType, context: ContextInterface) =>
      WithSitecoreContextDecorator(
        Story,
        context,
        LinkListComponent.Default,
        false
      ),
  ],
};
export default meta;

type Story = StoryObj<typeof LinkListComponent.Default>;

// More on writing stories with args: https://storybook.js.org/docs/react/writing-stories/args
export const Default: Story = {
  args: {
    params: {
      name: "LinkList",
      componentName: "LinkList",
      decorator: "sitecore",
    },
    fields: {
      data: {
        datasource: {
          children: {
            results: [
              {
                id: "E067F5D92BCE4DE6959A2F97B4308098",
                template: {
                  name: "XBE_Link",
                },
                Icon: {
                  value: "fa-light fa-chevron-right",
                },
                Link: {
                  jsonValue: {
                    value: {
                      href: "/en/",
                      text: "Home",
                      anchor: "",
                      linktype: "internal",
                      class: "",
                      title: "",
                      target: "",
                      querystring: "",
                      id: "{110D559F-DEA5-42EA-9C1C-8A5DF7E70EF9}",
                    },
                  },
                },
                Title: {
                  jsonValue: {
                    value: "Link 1",
                    editable: "false",
                  },
                },
                Url: {
                  Path: "/en/xbe-configuration/data/link-lists/example-link-list/link-1",
                },
              },
              {
                id: "B009BD35B11040DD8FE239D8E911A12F",
                template: {
                  name: "XBE_Link",
                },
                Icon: {
                  value: "fa-light fa-chevron-right",
                },
                Link: {
                  jsonValue: {
                    value: {
                      href: "/en/",
                      text: "Home 2",
                      anchor: "",
                      linktype: "internal",
                      class: "",
                      title: "",
                      target: "",
                      querystring: "",
                      id: "{110D559F-DEA5-42EA-9C1C-8A5DF7E70EF9}",
                    },
                  },
                },
                Title: {
                  jsonValue: {
                    value: "Link 1",
                    editable: "false",
                  },
                },                Url: {
                  Path: "/en/xbe-configuration/data/link-lists/example-link-list/link-2",
                },
              },
            ],
          },
          Title: {
            jsonValue: {
              value: "Example Link List",
            },
          },
          Icon: {
            value: "fa-sharp fa-light fa-chevron-right",
          },
        },
      },
    },
  },
};

import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { NewsOverviewComponent } from "ui-sitecore";
import { ExhibitorListModelMock, FirstLettersMock } from 'company-profile-data';

// More on how to set up stories at: https://storybook.js.org/docs/react/writing-stories/introduction
const meta: Meta = {
    title: "ui-sitecore/components/news/NewsOverview",
    component: NewsOverviewComponent.Default,
    tags: ["autodocs"],
    args: {
        tags: [
            { title: "<PERSON>wang<PERSON>" },

            { title: "Babynamen" },

            { title: "Verzorging" },

            { title: "Voeding" },

            { title: "Event" },

            { title: "Slap<PERSON>" },

            { title: "Musthave<PERSON>" },

            { title: "Ontwikkeling" },

            { title: "Advertorial" },

            { title: "Bevalling" },

            { title: "Mamablogger" }
        ],
        onLoadNews: async (page: number, selectedTags: NewsOverviewComponent.TagModel[]) => {
            return Array.from({ length: 20 }, (_, index) => ({
                title: index % 4 == 0 ? "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book." : `Title ${index + 1} Page ${page}`,
                date: "20 Feb 2019", 
                slug: `slug-${index + 1}`,
                tags: Array.from({ length: 10 }, (_, index) => `Tag ${index + 1}`),
                image: "https://edge.sitecorecloud.io/raiamsterda2c2f-raidigitalp9735-test6cf6-434c/media/project/rai-amsterdam-xmc/blueprints/blueprint_label/banners/news-banner.png"
            }));
        },
        gridColClassMobile: "two-column-mobile",
    },
    parameters: {
        actions: { argTypesRegex: "^on.*" },
        rootAttributesTooltip: true,
    },
};

export default meta;

type Story = StoryObj<typeof NewsOverviewComponent.Default>;

export const Schema: Story = {
    render: (args) => {
        return (
            <>
                <div>
                    <div className="flex flex-row  space-x-2 items-center">
                        <NewsOverviewComponent.Default {...args} />
                    </div>
                </div>
            </>
        );
    },
};
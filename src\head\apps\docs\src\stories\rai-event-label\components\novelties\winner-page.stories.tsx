import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { WithSitecoreContextDecorator } from "@uxbee/storybook-mock-sc";
import { WinnerPageComponent } from "rai-event-label";

const meta: Meta<typeof WinnerPageComponent.Default> = {
    title: "RAI/Event Label/Novelties/Winner Page",
    component: WinnerPageComponent.Default,
    tags: ["autodocs", "rai", "ep"],
    args: {
        loadNext: async (query: string, filter: string, page: number) => {
            console.log("LOAD ==>", query, filter, page);
            return Array.from({ length: 20 }, (_, index) => ({
                image: "https://ep.rai.nl/noviteiten/popup/img?inn=ddb0534c-c540-491f-8634-87b5f8e6ad5e&vraag=17",
                title: `Linea XT platform`,
                description: "This range of digital switches proves that you can blend aesthetic styling without penalising user-centric features. The Linea XT drew praise from the jury for its detailed design which is minimalist in look and yet with configurable backlit icons and status feedback options to make the job of identifying the function of its switches much easier. Clever features include the ability to incorporate proximity sensors and programme the backlighting for power saving. XT keypads can be customised in field using semi-transparent mylar labels, while the power supply and actuators sit in the flush mounting box.",
                category: "electronic & electrical systems",
                introductionDate: "15/11/2023",
                productNewArea: "the World",
                juryCommment: "This excellently conceived and engineered 25kW electric saildrive tangibly moves the goalposts for 45-70ft sailboats in terms of providing a reliable and extremely quiet option for electrification. Oceanvolt’s use of its controllable pitch propeller technology ensures maximum efficiency for propulsion and hydrogeneration, with the system able to return up 5kW of power when the yacht is sailing at 10 knots. Thought has been given to minimising installation and maintenance time and the ServoProp 25 operates from a 48V battery pack, keeping it within the safe voltage threshold. An exemplar product that pushes the electrification agenda toward more far-reaching horizons.",
                url: "https://company.metstrade.com/?a=PKiHoC7XqL6TjWrtOWV0t2iU8CGwO1KIx0dwtMBy9gjAdqT5UMA2STW9AdD75OIIa4cNLYgB0txuc6RNN2b1cNUlYdhQrt8%2BHbP7le9EWA1y1nLrUovArPHK4zriJxe7",
                company: {
                  url: "https://rai.nl",
                  name: "Vimar SpA",
                  logo: "https://ep.rai.nl/mypages/en/Home/ShowLogo?docid=67746&width=190&height=120",
                },
              }))
        },
        firstLetterFilter: ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"]
    },
    argTypes: {},
    decorators: [
        (Story, context) =>
            WithSitecoreContextDecorator(
                () => (
                    <div className="min-h-screen bg-white">
                        <Story />
                    </div>
                ),
                context,
                WinnerPageComponent.Default,
                false
            ),
    ],
};

export default meta;

type Story = StoryObj<typeof WinnerPageComponent.Default>;

export const Default: Story = {
    args: {
        config: {
            isShowFilterLetter: true,
            isShowSearchField: true,
            searchFieldWidth: {
                value: 700,
                unit: "px"
            }
        }
    }
}
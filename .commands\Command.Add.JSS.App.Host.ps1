[CmdletBinding()]
param(
    [string]
    [ValidateNotNullOrEmpty()]
    $HostName = "www.xmcloud.local",
    [string]
    [ValidateNotNullOrEmpty()]
    $AppName = "web"
)
# Define the script context
$CommandDir = Get-Location
Push-Location -Path ..
$ContextDir = Get-Location
Pop-Location
# Setting some variables
$HostNameLower = $HostName.ToLower()
$RenderingAppFile = Join-Path $ContextDir "docker-compose.override.rendering.$AppNameLower.yml"

# Import the used powershell functions
. $CommandDir/.powershell/Foundation.Docker.ps1
. $CommandDir/.powershell/Foundation.Certificates.ps1

Add-Traefik-Routers-Host -DockerComposeFilePath $RenderingAppFile -HostName $HostName
Add-Certificate -HostName $HostNameLower
Add-HostsEntry $HostNameLower

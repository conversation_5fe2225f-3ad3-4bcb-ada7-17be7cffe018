import { ExternalPackageComponentInjectionPluginBase } from '../externalPackageComponentInjectionPluginBase';
import * as externalPackageComponents from 'ui-sitecore';

class UiSitecorePackagesPlugin extends ExternalPackageComponentInjectionPluginBase {
  order = 100;

  packageName = 'ui-sitecore';

  protected getExternalPackage(): object {
    return externalPackageComponents;
  }
}

export const uiSitecorePackagesPlugin = new UiSitecorePackagesPlugin();

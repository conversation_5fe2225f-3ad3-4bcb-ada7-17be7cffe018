parameters:
- name: vmImage
  displayName: Pool Image
  type: string
  default: windows-latest
  values:
  - windows-latest
  - ubuntu-latest
  - macOS-latest

pr:
  branches:
    include: ['*']
  paths:
    include:
    - /src/head/*  

resources:
  repositories:
  - repository: repository
    type: git
    name: 'Foundation XM Cloud'
    ref: $(Build.SourceBranch)

stages:
- stage: turbolint_solutions
  displayName: Turbo lint
  jobs: 
  - template: template/job-turbo-lint.yaml
    parameters:
      JobName: turbolint
      JobDisplayName: Turbo lint
      TargetEnvironment: 'Test'
      SourceCode_Repository: repository
      XmcVariableGroup: RAI EP Portal - Test Environment
      NpmPrivateEndpoint: Uxbee Azure Artifacts (Private NPM)
      TurboProjectPath: '$(Build.SourcesDirectory)/src/head'

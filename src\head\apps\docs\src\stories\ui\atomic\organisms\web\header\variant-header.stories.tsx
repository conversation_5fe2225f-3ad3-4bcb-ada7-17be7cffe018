import React from 'react';
import type { Meta, StoryObj } from '@storybook/react';
import { VariantHeaderCountdownDesktop } from 'ui';
import { variantsHeaderData } from './variants-header-data';

const meta: Meta<typeof VariantHeaderCountdownDesktop> = {
    title: 'Components/Header/VariantHeaderCountdownDesktop',
    component: VariantHeaderCountdownDesktop,
    tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof VariantHeaderCountdownDesktop>;

export const Default: Story = {
    args: {
        variantHeaderData: variantsHeaderData[0]
    },
};

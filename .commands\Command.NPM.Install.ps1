[CmdletBinding()]
param()

Push-Location $PSScriptRoot
$CommandDir = Get-Location
Push-Location -Path ..
$ContextDir = Get-Location
$TurboPath = Join-Path $ContextDir "/src/head"
Pop-Location

. $CommandDir/.powershell/Foundation.Env.ps1
$GlobalEnvFile = Join-Path $ContextDir ".env"
Read-Local-Environment-Variables-From-File -EnvFilePath $GlobalEnvFile

Push-Location $TurboPath
npm install
Pop-Location

Pop-Location
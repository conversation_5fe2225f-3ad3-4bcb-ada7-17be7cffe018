import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { Logo } from "ui";

// More on how to set up stories at: https://storybook.js.org/docs/react/writing-stories/introduction
const meta: Meta<typeof Logo> = {
  title: "ui/Atomic/Atoms/Common/Logo",
  component: Logo,
  tags: ["autodocs", "ui", "atoms"],
  parameters: {
  },
  argTypes: {
    logoSrc: {
      options:[
        "logo.svg", "navbar-logo-1.png"
      ],
      control: { type: "select" },
      defaultValue: "navbar-logo-1.png",
    },
  },
};

export default meta;

type Story = StoryObj<typeof Logo>;

export const Default: Story = {
  render: (args) => {
    return (
      <>
        <Logo {...args} />
      </>
    );
  }, args: {
    className: "w-40 h-40 m-auto",
    logoSrc: "navbar-logo-1.png"
  },
};

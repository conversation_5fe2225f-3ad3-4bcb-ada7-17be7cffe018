function Install-Package-Globally() {
    param(
        [string]
        [ValidateNotNullOrEmpty()]
        $PackageName,
        [boolean]
        $AlwaysInstall = $FALSE
    )
    $PackageNameToUpper = "$PackageName".ToUpper()
    $install_package = $TRUE
    $GetCommand = "Get-Command $PackageName"
    if (Invoke-Expression $GetCommand -errorAction SilentlyContinue) {
        $VersionCommand = "$PackageName --version"
        $package_prev_v = (Invoke-Expression $VersionCommand)
    }
    
    if ($package_prev_v -and !$AlwaysInstall) {
        write-host "[$PackageNameToUpper] $PackageName $package_prev_v is already installed." -ForegroundColor DarkYellow
        $install_package = $FALSE
    }
    
    if ($install_package -or $AlwaysInstall) {
        write-host "Installing $PackageName globally"
        npm -y install $PackageName --global
    }	
}
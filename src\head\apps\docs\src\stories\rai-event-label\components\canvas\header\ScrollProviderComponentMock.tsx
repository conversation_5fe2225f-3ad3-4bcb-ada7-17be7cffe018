import React, { useState, useEffect, useRef } from 'react';

const ScrollProviderComponentMock = ({ children }: { children: (scrollTop: number) => React.ReactNode }) => {
  const [scrollTop, setScrollTop] = useState(0);
  const divRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleScroll = () => {
      if (divRef.current) {
        setScrollTop(divRef.current.scrollTop);
      }
    };

    const currentRef = divRef.current;
    if (currentRef) {
      currentRef.addEventListener("scroll", handleScroll);
    }

    // Cleanup event listener on unmount
    return () => {
      if (currentRef) {
        currentRef.removeEventListener("scroll", handleScroll);
      }
    };
  }, []);

  return (
    <div ref={divRef} className="relative h-screen bg-red-500 overflow-auto">
      <div className="flex flex-col">
        {children(scrollTop)}
        <div className="bg-green-500 h-[20px] w-full border-b-8"></div>
        <div className="bg-blue-500 h-[2200px] w-full border-b-8"></div>
        <div className="bg-yellow-500 h-[20px] w-full border-b-8"></div>
      </div>
      <div className="fixed bottom-0 left-0 bg-white p-2">
        <div>Scroll Top: {scrollTop}</div>
      </div>
    </div>
  );
};

export default ScrollProviderComponentMock;

// import { withActions } from "@storybook/addon-actions/decorator";
import type { Meta, StoryObj } from "@storybook/react";
import { Spinner } from "ui";

// More on how to set up stories at: https://storybook.js.org/docs/react/writing-stories/introduction
const meta: Meta<typeof Spinner> = {
  title: "ui/Atomic/Atoms/Common/Spinner",
  component: Spinner,
  tags: ["ui", "molecules", "common"],
  parameters: {},
  argTypes: {},
};

export default meta;

type Story = StoryObj<typeof Spinner>;

export const Default: Story = {
  render: (args) => {
    return (
      <div className="w-screen h-screen flex justify-center items-center flex-col gap-2">
        <Spinner color={args.color} />
        <Spinner color="info" aria-label="Info spinner example" />
        <Spinner color="success" aria-label="Success spinner example" />
        <Spinner color="failure" aria-label="Failure spinner example" />
        <Spinner color="warning" aria-label="Warning spinner example" />
        <Spinner color="pink" aria-label="Pink spinner example" />
        <Spinner color="purple" aria-label="Purple spinner example" />
      </div>
    );
  },
  args: {
    color: "success",
  },
};

function Remove-Folder() {
	param(
		[ValidateNotNullOrEmpty()]	
		[string]
		$FolderPath
	)
	if(Test-Path -Path $FolderPath){
		Remove-Item -Recurse -Force $FolderPath
        Write-Host "The Folder $FolderPath was removed." -ForegroundColor DarkYellow
	} else {
        Write-Host "The Folder $FolderPath was not found and could therefor not be removed." -ForegroundColor DarkYellow
    }
}

function Copy-Files() {
    param(
        [ValidateNotNullOrEmpty()]
		[string]
		$Source,
		[ValidateNotNullOrEmpty()]
        [string]
		$Target
    )
    if((Get-Item -force $Source) -is [System.IO.DirectoryInfo]) {
        # Add wildcard to source folder to ensure consistent behavior
        Copy-Item -Path $Source\* -Destination $Target -Recurse -Force
    } else {
        Copy-Item $Source -Destination $Target
    }
}
function Copy-Files-With-Excludes() {
	param(
		[ValidateNotNullOrEmpty()]	
        [string]
		$Source,
        [ValidateNotNullOrEmpty()]	
		[string]
		$Target,
		[array]
		$ExcludedFiles,
		[array]
		$ExcludedFolders
    )
	Get-ChildItem -Path $Source -Recurse -Exclude $ExcludedFiles | 
	Where-Object { $_.FullName.Replace($Source, "") -notmatch $ExcludedFolders } |
	Copy-Item -Destination {
		if ($_.PSIsContainer) {
			Join-Path $Target $_.Parent.FullName.Substring($Source.length -1)
		}
		else {
			Join-Path $Target $_.FullName.Substring($Source.length -1)
		}
	} -Force
}
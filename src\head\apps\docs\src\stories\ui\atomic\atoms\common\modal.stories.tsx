import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { Modal } from "ui";
import { useToggle } from "ui";
import { useI18n } from 'next-localization';
import parse from 'html-react-parser';

const meta: Meta<typeof Modal> = {
  title: "ui/Atomic/Atoms/Common/Modal",
  component: Modal,
  tags: ["autodocs", "ui", "atoms", "modal"],
  parameters: {},
  argTypes: {
    size: {
      options: ['xs', 'sm', 'md', 'lg', 'xl', '2xl', '3xl', '4xl', '5xl', '6xl', '7xl', 'full'],
      control: { type: 'select' },
    },
  },
};
export default meta;

type Story = StoryObj<typeof Modal>;

export const Default: Story = {
  render: (args) => {
    const [open, handleOpen] = useToggle();
    const { t } = useI18n();

    return (
      <div className="h-[40rem]">
        <button onClick={() => handleOpen()} className="px-4 py-2 text-white bg-brand-primary-1  rounded-md">{parse(t("ep-companyprofile-editbutton"))}</button>
        {open && (
          <Modal size={args.size} handleClick={handleOpen} >
            <h3 className="text-base font-semibold leading-6 text-gray-900" id="modal-title">Modal Title</h3>
            <div className="w-full mt-2 h-[36rem]">
              <iframe className="w-full h-full" src="https://www.youtube.com/embed/tgbNymZ7vqY?autoplay=1&mute=1" title="YouTube video player" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"></iframe>
            </div>
          </Modal>
        )}
      </div>
    );
  },
  args: {
    size: "xs",
    handleClick: () => { },
    children: "Element",
  },
};


export const DefaultSm: Story = {
  render: (args) => {
    const [open, handleOpen] = useToggle();
    const { t } = useI18n();

    return (
      <div className="h-[40rem]">
        <button onClick={() => handleOpen()} className="px-4 py-2 text-white bg-brand-primary-1  rounded-md">{parse(t("ep-companyprofile-editbutton"))}</button>
        {open && (
          <Modal size={args.size} handleClick={handleOpen} >
            <h3 className="text-base font-semibold leading-6 text-gray-900" id="modal-title">Modal Title</h3>
            <div className="w-full mt-2 h-[36rem]">
              <iframe className="w-full h-full" src="https://www.youtube.com/embed/tgbNymZ7vqY?autoplay=1&mute=1" title="YouTube video player" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"></iframe>
            </div>
          </Modal>
        )}
      </div>
    );
  },
  args: {
    size: "sm",
    handleClick: () => { },
    children: "Element",
  },
};

export const DefaultMd: Story = {
  render: (args) => {
    const [open, handleOpen] = useToggle();
    const { t } = useI18n();

    return (
      <div className="h-[40rem]">
        <button onClick={() => handleOpen()} className="px-4 py-2 text-white bg-brand-primary-1  rounded-md">{parse(t("ep-companyprofile-editbutton"))}</button>
        {open && (
          <Modal size={args.size} handleClick={handleOpen} >
            <h3 className="text-base font-semibold leading-6 text-gray-900" id="modal-title">Modal Title</h3>
            <div className="w-full mt-2 h-[36rem]">
              <iframe className="w-full h-full" src="https://www.youtube.com/embed/tgbNymZ7vqY?autoplay=1&mute=1" title="YouTube video player" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"></iframe>
            </div>
          </Modal>
        )}
      </div>
    );
  },
  args: {
    size: "md",
    handleClick: () => { },
    children: "Element",
  },
};

export const DefaultLg: Story = {
  render: (args) => {
    const [open, handleOpen] = useToggle();
    const { t } = useI18n();

    return (
      <div className="h-[40rem]">
        <button onClick={() => handleOpen()} className="px-4 py-2 text-white bg-brand-primary-1  rounded-md">{parse(t("ep-companyprofile-editbutton"))}</button>
        {open && (
          <Modal size={args.size} handleClick={handleOpen} >
            <h3 className="text-base font-semibold leading-6 text-gray-900" id="modal-title">Modal Title</h3>
            <div className="w-full mt-2 h-[36rem]">
              <iframe className="w-full h-full" src="https://www.youtube.com/embed/tgbNymZ7vqY?autoplay=1&mute=1" title="YouTube video player" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"></iframe>
            </div>
          </Modal>
        )}
      </div>
    );
  },
  args: {
    size: "lg",
    handleClick: () => { },
    children: "Element",
  },
};

export const DefaultXl: Story = {
  render: (args) => {
    const [open, handleOpen] = useToggle();
    const { t } = useI18n();

    return (
      <div className="h-[40rem]">
        <button onClick={() => handleOpen()} className="px-4 py-2 text-white bg-brand-primary-1  rounded-md">{parse(t("ep-companyprofile-editbutton"))}</button>
        {open && (
          <Modal size={args.size} handleClick={handleOpen} >
            <h3 className="text-base font-semibold leading-6 text-gray-900" id="modal-title">Modal Title</h3>
            <div className="w-full mt-2 h-[36rem]">
              <iframe className="w-full h-full" src="https://www.youtube.com/embed/tgbNymZ7vqY?autoplay=1&mute=1" title="YouTube video player" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"></iframe>
            </div>
          </Modal>
        )}
      </div>
    );
  },
  args: {
    size: "xl",
    handleClick: () => { },
    children: "Element",
  },
};
export const Default2Xl: Story = {
  render: (args) => {
    const [open, handleOpen] = useToggle();
    const { t } = useI18n();

    return (
      <div className="h-[40rem]">
        <button onClick={() => handleOpen()} className="px-4 py-2 text-white bg-brand-primary-1  rounded-md">{parse(t("ep-companyprofile-editbutton"))}</button>
        {open && (
          <Modal size={args.size} handleClick={handleOpen} >
            <h3 className="text-base font-semibold leading-6 text-gray-900" id="modal-title">Modal Title</h3>
            <div className="w-full mt-2 h-[36rem]">
              <iframe className="w-full h-full" src="https://www.youtube.com/embed/tgbNymZ7vqY?autoplay=1&mute=1" title="YouTube video player" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"></iframe>
            </div>
          </Modal>
        )}
      </div>
    );
  },
  args: {
    size: "2xl",
    handleClick: () => { },
    children: "Element",
  },
};

export const Default3Xl: Story = {
  render: (args) => {
    const [open, handleOpen] = useToggle();
    const { t } = useI18n();

    return (
      <div className="h-[40rem]">
        <button onClick={() => handleOpen()} className="px-4 py-2 text-white bg-brand-primary-1  rounded-md">{parse(t("ep-companyprofile-editbutton"))}</button>
        {open && (
          <Modal size={args.size} handleClick={handleOpen} >
            <h3 className="text-base font-semibold leading-6 text-gray-900" id="modal-title">Modal Title</h3>
            <div className="w-full mt-2 h-[36rem]">
              <iframe className="w-full h-full" src="https://www.youtube.com/embed/tgbNymZ7vqY?autoplay=1&mute=1" title="YouTube video player" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"></iframe>
            </div>
          </Modal>
        )}
      </div>
    );
  },
  args: {
    size: "3xl",
    handleClick: () => { },
    children: "Element",
  },
};
export const Default4Xl: Story = {
  render: (args) => {
    const [open, handleOpen] = useToggle();
    const { t } = useI18n();

    return (
      <div className="h-[40rem]">
        <button onClick={() => handleOpen()} className="px-4 py-2 text-white bg-brand-primary-1  rounded-md">{parse(t("ep-companyprofile-editbutton"))}</button>
        {open && (
          <Modal size={args.size} handleClick={handleOpen} >
            <h3 className="text-base font-semibold leading-6 text-gray-900" id="modal-title">Modal Title</h3>
            <div className="w-full mt-2 h-[36rem]">
              <iframe className="w-full h-full" src="https://www.youtube.com/embed/tgbNymZ7vqY?autoplay=1&mute=1" title="YouTube video player" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"></iframe>
            </div>
          </Modal>
        )}
      </div>
    );
  },
  args: {
    size: "4xl",
    handleClick: () => { },
    children: "Element",
  },
};

export const Default5Xl: Story = {
  render: (args) => {
    const [open, handleOpen] = useToggle();
    const { t } = useI18n();

    return (
      <div className="h-[40rem]">
        <button onClick={() => handleOpen()} className="px-4 py-2 text-white bg-brand-primary-1  rounded-md">{parse(t("ep-companyprofile-editbutton"))}</button>
        {open && (
          <Modal size={args.size} handleClick={handleOpen} >
            <h3 className="text-base font-semibold leading-6 text-gray-900" id="modal-title">Modal Title</h3>
            <div className="w-full mt-2 h-[36rem]">
              <iframe className="w-full h-full" src="https://www.youtube.com/embed/tgbNymZ7vqY?autoplay=1&mute=1" title="YouTube video player" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"></iframe>
            </div>
          </Modal>
        )}
      </div>
    );
  },
  args: {
    size: "5xl",
    handleClick: () => { },
    children: "Element",
  },
};
export const Default6Xl: Story = {
  render: (args) => {
    const [open, handleOpen] = useToggle();
    const { t } = useI18n();

    return (
      <div className="h-[40rem]">
        <button onClick={() => handleOpen()} className="px-4 py-2 text-white bg-brand-primary-1  rounded-md">{parse(t("ep-companyprofile-editbutton"))}</button>
        {open && (
          <Modal size={args.size} handleClick={handleOpen} >
            <h3 className="text-base font-semibold leading-6 text-gray-900" id="modal-title">Modal Title</h3>
            <div className="w-full mt-2 h-[36rem]">
              <iframe className="w-full h-full" src="https://www.youtube.com/embed/tgbNymZ7vqY?autoplay=1&mute=1" title="YouTube video player" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"></iframe>
            </div>
          </Modal>
        )}
      </div>
    );
  },
  args: {
    size: "6xl",
    handleClick: () => { },
    children: "Element",
  },
};

export const Default7Xl: Story = {
  render: (args) => {
    const [open, handleOpen] = useToggle();
    const { t } = useI18n();

    return (
      <div className="h-[40rem]">
        <button onClick={() => handleOpen()} className="px-4 py-2 text-white bg-brand-primary-1  rounded-md">{parse(t("ep-companyprofile-editbutton"))}</button>
        {open && (
          <Modal size={args.size} handleClick={handleOpen} >
            <h3 className="text-base font-semibold leading-6 text-gray-900" id="modal-title">Modal Title</h3>
            <div className="w-full mt-2 h-[36rem]">
              <iframe className="w-full h-full" src="https://www.youtube.com/embed/tgbNymZ7vqY?autoplay=1&mute=1" title="YouTube video player" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"></iframe>
            </div>
          </Modal>
        )}
      </div>
    );
  },
  args: {
    size: "7xl",
    handleClick: () => { },
    children: "Element",
  },
};
export const DefaultFull: Story = {
  render: (args) => {
    const [open, handleOpen] = useToggle();
    const { t } = useI18n();

    return (
      <div className="h-[40rem]">
        <button onClick={() => handleOpen()} className="px-4 py-2 text-white bg-brand-primary-1  rounded-md">{parse(t("ep-companyprofile-editbutton"))}</button>
        {open && (
          <Modal size={args.size} handleClick={handleOpen} >
            <h3 className="text-base font-semibold leading-6 text-gray-900" id="modal-title">Modal Title</h3>
            <div className="w-full mt-2 h-[36rem]">
              <iframe className="w-full h-full" src="https://www.youtube.com/embed/tgbNymZ7vqY?autoplay=1&mute=1" title="YouTube video player" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"></iframe>
            </div>
          </Modal>
        )}
      </div>
    );
  },
  args: {
    size: "full",
    handleClick: () => { },
    children: "Element",
  },
};

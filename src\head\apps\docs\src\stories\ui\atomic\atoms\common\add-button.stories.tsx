// import { withActions } from "@storybook/addon-actions/decorator";
import type { Meta, StoryObj } from "@storybook/react";
import { AddButton } from "ui";

// More on how to set up stories at: https://storybook.js.org/docs/react/writing-stories/introduction
const meta: Meta<typeof AddButton> = {
  title: "ui/Atomic/Atoms/Common/AddButton",
  component: AddButton,
  tags: ["ui", "molecules", "common"],
  parameters: {},
  argTypes: {
  },
};

export default meta;

type Story = StoryObj<typeof AddButton>;

export const Default: Story = {
  render: (args) => {
    return (
      <div className="w-screen h-screen p-5 gap-y-[10px]">
        <AddButton {...args}
        />
      </div>
    );
  },
  args: {
    title:"Add Product",
    description:"Lorem ipsum dolor, sit amet consectetur"
  },
};
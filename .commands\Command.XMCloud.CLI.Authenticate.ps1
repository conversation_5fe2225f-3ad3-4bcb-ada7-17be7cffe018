[CmdletBinding()]
param(
    [string]
    [ValidateNotNullOrEmpty()]
    $CMURL = "https://xmcloudcm.localhost",
    [string]
    [ValidateNotNullOrEmpty()]
    $AuthorityURL = "https://auth.sitecorecloud.io",
    [string]
    [ValidateNotNullOrEmpty()]
    $AudienceURL = "https://api.sitecorecloud.io",
    [string]
    [ValidateNotNullOrEmpty()]
    $ClientId = "Chi8EwfFnEejksk3Sed9hlalGiM9B2v7",
    [string]
    [ValidateNotNullOrEmpty()]
    $SitecoreUserJson = "\.sitecore\user.json",
    [string]
    $EnvironmentName = "Default"
)
Push-Location $PSScriptRoot
# Based on this documentation
# https://doc.sitecore.com/xmc/en/developers/xm-cloud/log-in-to-a-sitecore-instance-with-sitecore-command-line-interface.html#use-an-interactive-user-login-device-code-flow
# Define the script context
#$CommandDir = Get-Location
Push-Location -Path ..
$ContextDir = Get-Location
Pop-Location
Push-Location $ContextDir
# Check if the user is already authenticated
$sitecoreUserLogin = (Join-Path $ContextDir $SitecoreUserJson)
if (Test-Path -Path $sitecoreUserLogin -PathType Leaf) {
    # If the user is already authenticated, give the option to logout by deleting the file
    # TODO: Deleting the file is not ideal, the file should be read as json and based on the environment name, that is passed in we need to check the validity of the authentication.
    $message = -join("Are you sure you want to delete the file ", $sitecoreUserLogin)
    $confirmation = Read-Host $message
    if ($confirmation -eq 'y') {
        Write-Host "--- Removing user.json ---"
        Remove-Item $sitecoreUserLogin
    }
}

# Check if the user is not already authenticated, start the reauthentication process
#if (!(Test-Path -Path $sitecoreUserLogin -PathType Leaf)) {
    dotnet tool restore
    dotnet sitecore login --authority $AuthorityURL --cm $CMURL --audience $AudienceURL --client-id $ClientId --environment-name $EnvironmentName --allow-write true
#}
Pop-Location
Pop-Location
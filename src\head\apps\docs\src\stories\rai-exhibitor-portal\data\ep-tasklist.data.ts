export const TaskListData = {
    params: {
        name: "EPTaskList",
        componentName: "EPTaskList",
        tag: "h1",
        GridParameters: "col-span-12 xl:col-span-6 lg:col-span-6"
    },
    rendering: {
        uid: "{00000000-0000-0000-0000-000000000000}",
        componentName: "EPTaskList",
        dataSource: "{00000000-0000-0000-0000-000000000000}",
    },
    fields: {
        data: {
            taskList: {
                categories: {
                    results: [
                        {
                            title: {
                                value: "Category 1"
                            },
                            tasks: {
                                results: [
                                    {
                                        id: "1",
                                        date: {
                                            value: "2024-02-12"
                                        },
                                        title: {
                                            value: "Task 1"
                                        },
                                        description: {
                                            value: "Description of Task 1"
                                        },
                                        optional: {
                                            value: false
                                        },
                                        link: {
                                            url: "https://example.com/task1",
                                            text: "More Info",
                                            target: "_blank"
                                        }
                                    },
                                    {
                                        id: "2",
                                        date: {
                                            value: "2024-02-13"
                                        },
                                        title: {
                                            value: "Task 2"
                                        },
                                        description: {
                                            value: "Description of Task 2"
                                        },
                                        optional: {
                                            value: true
                                        },
                                        link: {
                                            url: "https://example.com/task2",
                                            text: "Details",
                                            target: "_blank"
                                        }
                                    }
                                ]
                            }
                        },
                        {
                            title: {
                                value: "Category 2"
                            },
                            tasks: {
                                results: [
                                    {
                                        id: "3",
                                        date: {
                                            value: "2024-02-14"
                                        },
                                        title: {
                                            value: "Task 3"
                                        },
                                        description: {
                                            value: "Description of Task 3"
                                        },
                                        optional: {
                                            value: false
                                        },
                                        link: {
                                            url: "https://example.com/task3",
                                            text: "Learn More",
                                            target: "_blank"
                                        }
                                    }
                                ]
                            }
                        }
                    ]
                }
            }
        }
    }
}
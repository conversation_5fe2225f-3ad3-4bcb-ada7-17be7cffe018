// import { withActions } from "@storybook/addon-actions/decorator";
import type { Meta, StoryObj } from "@storybook/react";
import { SlideImage, Slider } from "ui";

// More on how to set up stories at: https://storybook.js.org/docs/react/writing-stories/introduction
const meta: Meta<typeof Slider> = {
  title: "ui/Atomic/Atoms/Common/Slider",
  component: Slider,
  tags: ["ui", "molecules", "common"],
};

export default meta;

type Story = StoryObj<typeof Slider>;

export const Default: Story = {
  render: (args) => {
    return (
      <div className="w-screen h-screen p-5 gap-y-[10px]">
        <Slider {...args} />
      </div>
    );
  },
  args: {
    items: [
      {
        image:
          "https://assets-prd.raicore.com/-/media/project/rai-amsterdam/negenmaandenbeurs/nmb/afbeeldingen/2024/selectie/fotograaf-damon-rigter---rigtercreatives---negenmaandenbeurs-dag-1---14_02_2024-078-min.jpg",
        title: "Test With Title",
        link: "https://rai.nl",
      },
      {
        image:
          "https://assets-prd.raicore.com/-/media/project/rai-amsterdam/negenmaandenbeurs/nmb/afbeeldingen/2024/selectie/fotograaf-damon-rigter---rigtercreatives---negenmaandenbeurs-dag-2---15-02-2024-071-min.jpg",
        title: "",
        link: "",
      },
      {
        image:
          "https://assets-prd.raicore.com/-/media/project/rai-amsterdam/negenmaandenbeurs/nmb/afbeeldingen/2024/fotograaf-damon-rigter---rigtercreatives---negenmaandenbeurs-dag-2---15-02-2024-168-min.jpg",
        title: "",
        link: "",
      },
      {
        image:
          "https://assets-prd.raicore.com/-/media/project/rai-amsterdam/negenmaandenbeurs/nmb/afbeeldingen/2024/fotograaf-damon-rigter---rigtercreatives---negenmaandenbeurs-dag-1---14_02_2024-102-min.jpg",
        title: "Another with Title",
        link: "",
      },
      {
        image:
          "https://assets-prd.raicore.com/-/media/project/rai-amsterdam/negenmaandenbeurs/nmb/afbeeldingen/2024/selectie/fotograaf-damon-rigter---rigtercreatives---negenmaandenbeurs-dag-1---14_02_2024-137-min.jpg",
        title: "My Title",
        link: "",
      },
    ],
    builder: (arg: { image: string; title?: string; link?: string }) => {
      return <SlideImage {...arg} settings={{ showCTAButton: !!arg.link }} />;
    },
    title: "Custom Slider Title",
    settings: {
      isAdaptiveHeight: false,
      isAutoplay: true,
      autoplaySpeed: 2000,
      showArrows: true,
      showDots: true,
      isFade: false,
      isInfiniteScroll: true,
      pauseOnHover: true,
      slidesToShow: 4,
      spaceBetweenSlides: {
        value: 10,
        unit: "px",
      },
      showCTAButton: false,
    },
  },
};

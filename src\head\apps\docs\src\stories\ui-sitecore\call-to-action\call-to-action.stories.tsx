import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { WithSitecoreContextDecorator } from "@uxbee/storybook-mock-sc/lib/WithSitecoreContextDecorator";
import { CallToActionComponent } from "ui-sitecore";
import {
  CallToActionDefaultData1,
  CallToActionDefaultDataNoDatasource
} from "./../data/call-to-action.data";

// More on how to set up stories at: https://storybook.js.org/docs/react/writing-stories/introduction
const meta: Meta<typeof CallToActionComponent.Default> = {
  title: "ui-sitecore/components/call-to-action/default",
  component: CallToActionComponent.Default,
  tags: ["autodocs", "ui-sitecore", "sitecore"],
  argTypes: {},
  decorators: [
    (Story, context) =>
      WithSitecoreContextDecorator(Story, context, CallToActionComponent.Default, false),
  ],
};
export default meta;

type Story = StoryObj<typeof CallToActionComponent.Default>;

// More on writing stories with args: https://storybook.js.org/docs/react/writing-stories/args
export const Default: Story = {
  args: CallToActionDefaultData1,
};

export const DefaultDataNoDatasource: Story = {
  args: CallToActionDefaultDataNoDatasource,
};

import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { WithSitecoreContextDecorator } from "@uxbee/storybook-mock-sc";
import { EPMessagesComponent } from "rai-exhibitor-portal";
import { MessageData } from "./data/ep-message.data";

const meta: Meta<typeof EPMessagesComponent.Default> = {
    title: "RAI/Exhibitor Portal/Canvas/EPMessagesComponent",
    component: EPMessagesComponent.Default,
    tags: ["autodocs", "rai", "ep"],
    argTypes: {},
    decorators: [
        (Story, context) =>
            WithSitecoreContextDecorator(
                () => (
                    <div className="h-[30rem] bg-[#fafafa]">
                        <div className="grid grid-cols-12 gap-4">
                            <Story />
                        </div>
                    </div>
                ),
                context,
                EPMessagesComponent.Default,
                false
            ),
    ],
};

export default meta;

type Story = StoryObj<typeof EPMessagesComponent.Default>;

export const Default: Story = {
    // args: LabelFooterData,
    args: MessageData
};

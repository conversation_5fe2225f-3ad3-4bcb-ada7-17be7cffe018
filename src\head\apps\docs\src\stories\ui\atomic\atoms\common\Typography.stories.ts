import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { Typography } from "ui";

const meta: Meta<typeof Typography> = {
  title: "ui/Atomic/Atoms/Common/Typography",
  component: Typography,
  tags: ["autodocs", "ui", "atoms"],
  argTypes: {
    alignment: {
      options: ["left", "center", "right"],
      control: { type: "select" },
      defaultValue: "h1",
    },
    tag: {
      options: ["h1", "h2", "h3", "h4", "h5", "h6", "p", "a", "div","span"],
      control: { type: "select" },
      defaultValue: "h1",
    },
  },
};

export default meta;
type Story = StoryObj<typeof Typography>;

export const DefaultH1LeftDesktop: Story = {
  args: {
    alignment: "left",
    tag: "h1",
    children: "The quick brown fox jumps over the lazy dog",
    // className: "text-black",
  },
};

export const DefaultH1LeftMobileSmall: Story = {
  args: {
    alignment: "left",
    tag: "h1",
    children: "The quick brown fox jumps over the lazy dog",
    className: "text-black",
  },
  parameters:{
    viewport:{
      // defaultViewport: 'mobile1',
    }
  }
};

export const DefaulH1LeftMobileLarge: Story = {
  args: {
    alignment: "left",
    tag: "h1",
    children: "The quick brown fox jumps over the lazy dog",
  },
  parameters:{
    viewport:{
      defaultViewport: 'mobile2',
    }
  }
};

export const DefaultH1LeftMobileTablet: Story = {
  args: {
    alignment: "left",
    tag: "h1",
    children: "The quick brown fox jumps over the lazy dog",
  },
  parameters:{
    viewport:{
      defaultViewport: 'tablet',
    }
  }
};


export const DefaultH1Center: Story = {
  args: {
    alignment: "center",
    tag: "h1",
    children: "The quick brown fox jumps over the lazy dog",
  },
};

export const DefaultH1Right: Story = {
  args: {
    alignment: "right",
    tag: "h1",
    children: "The quick brown fox jumps over the lazy dog",
  },
};
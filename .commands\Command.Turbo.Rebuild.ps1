[CmdletBinding(DefaultParameterSetName = "no-arguments")]
param(
)

Push-Location $PSScriptRoot
# Define the script context
Push-Location -Path ..
$ContextDir = Get-Location
$TurboPath = Join-Path $ContextDir "/src/head"
Pop-Location

Push-Location $ContextDir

Write-Host "=============== Kill Turbo Processes =====================" -ForegroundColor Green
.\execute.ps1 kill.nodejs
.\execute.ps1 kill.turbo

Write-Host "=============== Rebuilding Turbo =====================" -ForegroundColor Green
Push-Location $TurboPath
npm run clear:all
Pop-Location

.\execute.ps1 npm.install

Push-Location $TurboPath
turbo build --filter security --force
turbo build --force
Pop-Location

Pop-Location
Pop-Location


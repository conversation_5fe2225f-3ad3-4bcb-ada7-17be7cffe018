export const tasksInfoData = [
   {
    id: "0",
    psId: "0",
    date: "20231220T235900Z",
    title: "Company Profile",
    priority: "default",
    linkTitle: "Company Profile",
    linkHref: "#",
    linkTarget: "",
    completed: true, 
    content:
      "Lorem ipsum dolor sit amet consectetur adipisicing elit. Nesciunt saepe sit ipsam similique voluptatem vel unde sint numquam non debitis, vitae cum id placeat architecto dolore, nam soluta recusandae amet!",
    required: true,
    category: "1month",
    parsedDate: new Date(2025, 5, 11),
    hasPriority: false,
  },
  {
    id: "1",
    psId: "1",
    date: "20231220T235900Z",
    title: "Company Profile",
    priority: "urgent",
    linkTitle: "Company Profile",
    linkHref: "#",
    linkTarget: "",
    completed: false, 
    content:
      "Lorem ipsum dolor sit amet consectetur adipisicing elit. Nesciunt saepe sit ipsam similique voluptatem vel unde sint numquam non debitis, vitae cum id placeat architecto dolore, nam soluta recusandae amet!",
    required: true,
    category: "1month",
    parsedDate: new Date(2025, 5, 12),
    hasPriority: false,
  },
  {
    id: "2",
    psId: "2",
    date: "20231220T235900Z",
    title: "Company Profile",
    priority: "default",
    linkTitle: "Company Profile",
    linkHref: "#",
    linkTarget: "",
    completed: false, 
    content:
      "Lorem ipsum dolor sit amet consectetur adipisicing elit. Nesciunt saepe sit ipsam similique voluptatem vel unde sint numquam non debitis, vitae cum id placeat architecto dolore, nam soluta recusandae amet!",
    required: true,
    category: "1month",
    parsedDate: new Date(2025, 5, 13),
    hasPriority: false,
  },
  {
    id: "3",
    psId: "3",
    date: "20231220T235900Z",
    title: "Company Profile",
    priority: "overdue",
    linkTitle: "Company Profile",
    linkHref: "#",
    linkTarget: "",
    completed: false, 
    content:
      "Lorem ipsum dolor sit amet consectetur adipisicing elit. Nesciunt saepe sit ipsam similique voluptatem vel unde sint numquam non debitis, vitae cum id placeat architecto dolore, nam soluta recusandae amet!",
    required: true,
    category: "1month",
    parsedDate: new Date(2025, 5, 14),
    hasPriority: false,
  },
  {
    id: "4",
    psId: "4",
    date: "20240120T235900Z",
    title: "Company Profile",
    priority: "optional",
    linkTitle: "Company Profile",
    linkHref: "#",
    linkTarget: "",
    completed: false, 
    content:
      "Lorem ipsum dolor sit amet consectetur adipisicing elit. Nesciunt saepe sit ipsam similique voluptatem vel unde sint numquam non debitis, vitae cum id placeat architecto dolore, nam soluta recusandae amet!",
    required: false,
    category: "1month",
    parsedDate: new Date(2025, 5, 15),
    hasPriority: false,
  },
  {
    id: "5",
    psId: "5",
    date: "20240120T235900Z",
    priority: "optional",
    title: "Company Profile",
    linkTitle: "Company Profile",
    linkHref: "#",
    linkTarget: "",
    completed: false, 
    content:
      "Lorem ipsum dolor sit amet consectetur adipisicing elit. Nesciunt saepe sit ipsam similique voluptatem vel unde sint numquam non debitis, vitae cum id placeat architecto dolore, nam soluta recusandae amet!",
    required: false,
    category: "1month",
    parsedDate: new Date(2025, 5, 16),
    hasPriority: false,
  },
  {
    id: "6",
    psId: "6",
    date: "20240120T235900Z",
    title: "Company Profile",
    linkTitle: "Company Profile",
    linkHref: "#",
    linkTarget: "",
    priority: "optional",
    completed: true, 
    content:
      "Lorem ipsum dolor sit amet consectetur adipisicing elit. Nesciunt saepe sit ipsam similique voluptatem vel unde sint numquam non debitis, vitae cum id placeat architecto dolore, nam soluta recusandae amet!",
    required: false,
    category: "1month",
    parsedDate: new Date(2025, 5, 17),
    hasPriority: false,
  },
  {
    id: "7",
    psId: "7",
    date: "20240220T235900Z",
    title: "Company Profile",
    linkTitle: "Company Profile",
    linkHref: "#",
    linkTarget: "",
    priority: "optional",
    completed: false, 
    content:
      "Lorem ipsum dolor sit amet consectetur adipisicing elit. Nesciunt saepe sit ipsam similique voluptatem vel unde sint numquam non debitis, vitae cum id placeat architecto dolore, nam soluta recusandae amet!",
    required: false,
    category: "1month",
    parsedDate: new Date(2025, 5, 18),
    hasPriority: false,
  },
  {
    id: "8",
    psId: "8",
    date: "20240220T235900Z",
    title: "Company Profile",
    linkTitle: "Company Profile",
    linkHref: "#",
    linkTarget: "",
    priority: "optional",
    completed: false, 
    content:
      "Lorem ipsum dolor sit amet consectetur adipisicing elit. Nesciunt saepe sit ipsam similique voluptatem vel unde sint numquam non debitis, vitae cum id placeat architecto dolore, nam soluta recusandae amet!",
    required: false,
    category: "1month",
    parsedDate: new Date(2025, 5, 19),
    hasPriority: false,
  },
  {
    id: "9",
    psId: "9",
    date: "20240220T235900Z",
    title: "Company Profile",
    linkTitle: "Company Profile",
    linkHref: "#",
    linkTarget: "",
    priority: "optional",
    completed: true, 
    content:
      "Lorem ipsum dolor sit amet consectetur adipisicing elit. Nesciunt saepe sit ipsam similique voluptatem vel unde sint numquam non debitis, vitae cum id placeat architecto dolore, nam soluta recusandae amet!",
    required: false,
    category: "1month",
    parsedDate: new Date(2025, 5, 20),
    hasPriority: false,
  },
];
// import { withActions } from "@storybook/addon-actions/decorator";
import type { Meta, StoryObj } from "@storybook/react";
import { Checkbox } from "ui";

// More on how to set up stories at: https://storybook.js.org/docs/react/writing-stories/introduction
const meta: Meta<typeof Checkbox> = {
  title: "ui/Atomic/Atoms/Common/Checkbox",
  component: Checkbox,
  tags: ["ui", "molecules", "common"],
  parameters: {},
  argTypes: {
  },
};

export default meta;

type Story = StoryObj<typeof Checkbox>;

export const Default: Story = {
  render: (args) => {
    const onUpdateValue=()=>{
        console.log("checkbox")
    }
    return (
        <div className="w-screen h-screen flex justify-center items-center">
            <Checkbox value={args.value} onUpdateValue={onUpdateValue} selected={false}/>
        </div>
    );
  },
  args: {
    value: "E-mailadres",
  },
};

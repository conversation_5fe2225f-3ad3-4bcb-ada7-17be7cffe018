Push-Location $PSScriptRoot
# Define the script context
Push-Location -Path ..
$ContextDir = Get-Location
Pop-Location

Push-Location $ContextDir
#check if docker is running
$hash = docker compose ps --services --filter "status=running"
#Even when a service is not running there is still one empty row that needs to be taken into consideration. That is why 2 is used here.
if($hash.count -ge 2) {
	Write-Host "--- Your environment is running, I am taking it down! ---"
	docker compose down
}

Write-Host "--- Stopping Docker ---"
Stop-service com.docker.service
Stop-service docker
Stop-service hns
#remove panic log
$panicFile = 'C:\ProgramData\Docker\panic.log'
if (Test-Path -Path $panicFile -PathType Leaf) {
    Write-Host "--- Removing panic.log ---"
    Remove-Item $panicFile -Force
}
Write-Host "--- Starting Docker ---"
Start-service hns
Start-service docker
Start-service com.docker.service

docker network prune --force

# Naam van het Docker-netwerk om te controleren
$networkName = "xmcloud-foundation_default"
# Controleer of het Docker-netwerk bestaat
$networkExists = docker network ls --filter name=$networkName --quiet

if ($networkExists) {
    # Verwijder het Docker-netwerk
    docker network rm $networkName
    Write-Host "Docker-netwerk '$networkName' was dangling, therefor I removed it."
} else {
    Write-Host "Docker-netwerk '$networkName' was removed."
}
Pop-Location
Pop-Location
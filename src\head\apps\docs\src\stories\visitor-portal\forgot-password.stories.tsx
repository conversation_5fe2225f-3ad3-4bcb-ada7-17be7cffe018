import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { WithSitecoreContextDecorator } from "@uxbee/storybook-mock-sc/lib/WithSitecoreContextDecorator";
import { ForgotPasswordComponent } from "visitor-portal";

// More on how to set up stories at: https://storybook.js.org/docs/react/writing-stories/introduction
const meta: Meta<typeof ForgotPasswordComponent.Default> = {
  title: "RAI/Visitor Portal/Canvas/ForgotPassword",
  component: ForgotPasswordComponent.Default,
  tags: ["autodocs", "rai", "ep"],
  argTypes: {},
  args: {
    backgroundUrl: "https://assets-prd.raicore.com/-/media/project/rai-amsterdam/rai/header-home/newhomepagebanner.jpg?rev=1c27181b571448ac905e0bca800fcf57",
    loginUrl: "#",
    registerUrl: "#",
    resendUrl: "#"
  },
  decorators: [
    (Story, context) =>
      WithSitecoreContextDecorator(
        () => (
          <div className="h-[40rem]">
            <Story />
          </div>
        ),
        context,
        ForgotPasswordComponent.Default,
        false
      ),
  ],
};

export default meta;

type Story = StoryObj<typeof ForgotPasswordComponent.Default>;

export const Default: Story = {
  args: {
    onForgotPassword: async (email: string) => {
      console.log("Start Forgot Password");
      await new Promise<void>((resolve) => {
        setTimeout(() => {
          resolve();
        }, 3000);
      });
      console.log("Success Forgot Password");
      return 'success';
    },
  },
};

export const Fail: Story = {
  args: {
    onForgotPassword: async (email: string) => {
      console.log("Start Forgot Password");
      await new Promise<void>((resolve) => {
        setTimeout(() => {
          resolve();
        }, 3000);
      });
      throw "Email already requested for password reset";
    },
    backgroundUrl: "https://assets-prd.raicore.com/-/media/project/rai-amsterdam/rai/header-home/newhomepagebanner.jpg?rev=1c27181b571448ac905e0bca800fcf57",
  },
};

export const EmailAlreadySent: Story = {
    args: {
      onForgotPassword: async (email: string) => {
        console.log("Start Forgot Password");
        await new Promise<void>((resolve) => {
            setTimeout(() => {
              resolve();
            }, 3000);
          });
        return 'email-already-sent'
      },
      backgroundUrl: "https://assets-prd.raicore.com/-/media/project/rai-amsterdam/rai/header-home/newhomepagebanner.jpg?rev=1c27181b571448ac905e0bca800fcf57",
    },
  };

  export const EmailNotExist: Story = {
    args: {
      onForgotPassword: async (email: string) => {
        console.log("Start Forgot Password");
        await new Promise<void>((resolve) => {
            setTimeout(() => {
              resolve();
            }, 3000);
          });
        return "email-not-exist"
      },
      backgroundUrl: "https://assets-prd.raicore.com/-/media/project/rai-amsterdam/rai/header-home/newhomepagebanner.jpg?rev=1c27181b571448ac905e0bca800fcf57",
    },
  };

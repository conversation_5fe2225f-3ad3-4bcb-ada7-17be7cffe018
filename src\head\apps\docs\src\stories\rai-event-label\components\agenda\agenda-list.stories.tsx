import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { AgendaListComponent } from 'rai-event-label';  // Adjust the import path according to your project structure
import { agendaListMock } from "./agenda.mock";

const meta: Meta<typeof AgendaListComponent.Default> = {
    title: "RAI/Event Label/Agenda/List",
  component: AgendaListComponent.Default,
  tags: ["autodocs", "component"],
  argTypes: {},
  decorators: [
    (Story) => (
      <div className="min-h-screen bg-[#fafafa] p-4">
        <Story />
      </div>
    ),
  ],
};

export default meta;

type Story = StoryObj<typeof AgendaListComponent.Default>;

export const Default: Story = {
  args: {
    agenda: 'Calendar',
    title: 'These events will be at <br> RAI Amsterdam soon',
    loadNext: async (page: number) => {
      console.log("Load agenda list :", page)
      return agendaListMock.map(event => ({
        ...event,
        title: `${event.title} - Page ${page}`
      }));
    }
  },
};

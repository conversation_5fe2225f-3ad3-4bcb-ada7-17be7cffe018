import {
  ComponentRendering,
  Placeholder,
} from "@sitecore-jss/sitecore-jss-nextjs";
import type { Meta, StoryObj } from "@storybook/react";
import { WithSitecoreContextDecorator } from "@uxbee/storybook-mock-sc/lib/WithSitecoreContextDecorator";
import { GridContainer } from "ui";
import {
  BreadcrumbComponent,
  NewsDetails,
  ShareComponent,
} from "ui-sitecore";
import { newsDetailContent } from "./news-detail.html";
import { NewsArticle } from "ui-sitecore/src/components/news/NewsDetail";
// import { CompanyProfileFullDefaultData1 } from "../../company-profile/data/company-profile-full.data";

// More on how to set up stories at: https://storybook.js.org/docs/react/writing-stories/introduction
const meta: Meta<typeof NewsDetails.Default> = {
  title: "ui-sitecore/components/news/NewsDetail",
  component: NewsDetails.Default,
  tags: ["autodocs", "ui-sitecore", "sitecore"],
  argTypes: {},
  decorators: [
    (Story, context) =>
      WithSitecoreContextDecorator(
        () => (
          <div className="h-[20rem] bg-white">
            <div className="container">
              <GridContainer columnSpacing="04" rowSpacing="04">
                <Story />
              </GridContainer>
            </div>
          </div>
        ),
        context,
        NewsDetails.Default,
        false
      ),
  ],
};
export default meta;

type Story = StoryObj<typeof NewsDetails.Default>;

const newsMock: NewsArticle = {
  id: "id-mock",
  language: "en",
  hero: {
    value: {
      src: "https://assets-prd.raicore.com/-/media/project/rai-amsterdam/aquatech/news/2024/hong-kong-article.png?h=628&amp;iar=0&amp;w=1200&amp;rev=094d087fe5b147a28fa51d8b07dcedf2&amp;hash=B7A7E4D65BE05965472CFC549B9D6AFE",
    },
  },
  tags: ["Desalination", "Membranes", "Asia"],
  title: {
    value:
      "HONG KONG’S SOLAR POWERED DESALINATION PLANT PROVIDES “CLIMATE-PROOF” WATER",
  },
  author: "Kim Hollamby",
  date: {
    field: {
      value: "Monday, 19 January 2024"
    },
    timeStamp: 1700496000
  },
  contentHtml: {
    value: newsDetailContent,
  }
};


export const SitecoreJSSPlaceholder: Story = {
  args: {
    rendering: {} as ComponentRendering,
    news: newsMock,
    likeComponent: {
      config: {
        showComponent: true,
        showDislike: true,
        showLikeNumber: true,
      },
      visitorStatus: {
        isDislike: false,
        isLike: false,
      },
      numberOfLikes: 100,
      onToggleLike: async () => { },
      onToggleDislike: async () => { },
    },
    loginPopup: {
      loginUrl: "#"
    }
  },
};

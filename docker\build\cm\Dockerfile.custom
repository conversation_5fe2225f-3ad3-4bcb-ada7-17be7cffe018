# escape=`

ARG PARENT_IMAGE
ARG SOLUTION_IMAGE
ARG TOOLS_IMAGE

FROM ${TOOLS_IMAGE} as tools
FROM ${PARENT_IMAGE}

SHELL ["powershell", "-Command", "$ErrorActionPreference = 'Stop'; $ProgressPreference = 'SilentlyContinue';"]

WORKDIR C:\inetpub\wwwroot

# Copy developer tools and entrypoint
COPY --from=tools C:\tools C:\tools

# Add entry point execution file
COPY .\tools\entrypoints\docker\Development.ps1 C:\tools\entrypoints\docker\Development.ps1

# Add development config files
COPY .\App_Config\Include\zzz C:\inetpub\wwwroot\App_Config\Include\zzz
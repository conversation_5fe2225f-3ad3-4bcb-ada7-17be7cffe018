import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { But<PERSON> } from "ui";

// More on how to set up stories at: https://storybook.js.org/docs/react/writing-stories/introduction
const button: Meta<typeof Button> = {
  title: "ui/Atomic/Atoms/Common/Button",
  component: Button,
  tags: ["autodocs", "ui", "atoms"],
  parameters: {
    actions: { argTypesRegex: "^on.*" },
    rootAttributesTooltip: true,
  },

  argTypes: {
    variant: {
      options: [
        'btn-primary-solid',
        'btn-secondary-solid',
        'btn-tertiary-solid',
        'btn-quaternary-solid',
        'btn-white-solid',
        'btn-primary-outline',
        'btn-secondary-outline',
        'btn-tertiary-outline',
        'btn-quaternary-outline',
        'btn-success-solid',
        'btn-danger-solid',
        'btn-quinary-solid',
        'btn-senary-solid',
        'btn-septenary-solid',
        'btn-quinary-outline',
        'btn-senary-outline',
        'btn-septenary-outline'
      ],
      control: { type: 'select' },
    },
    iconPosition: {
      options: ['left', 'right'],
      control: { type: 'select' },
    },
  },
};

export default button;

type Story = StoryObj<typeof Button>;

// More on writing stories with args: https://storybook.js.org/docs/react/writing-stories/args
export const Default: Story = {
  render: (args) => {
    return (
      <div className="h-[20rem] bg-gray-400">
        <div className="grid grid-cols-12 gap-4">
          <div className="col-span-12 m-12">
            <Button {...args} />
          </div>
        </div>
      </div>
    );
  },
  args: {
    children: "Company Profile",
    variant: "btn-primary-solid",
    iconPosition: "right",
    icon: `<i class="fa-solid fa-arrow-right"></i>`,
    href: "#",
  },
};

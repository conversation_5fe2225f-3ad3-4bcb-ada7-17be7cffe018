import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { ShareComponent } from "ui-sitecore";
import { WithSitecoreContextDecorator } from "@uxbee/storybook-mock-sc/lib/WithSitecoreContextDecorator";

// More on how to set up stories at: https://storybook.js.org/docs/react/writing-stories/introduction
const meta: Meta<typeof ShareComponent.Variant> = {
  title: "ui-sitecore/components/social/VariantSharePlugin",
  component: ShareComponent.Variant,
  tags: ["autodocs", "ui-sitecore", "sitecore"],
  argTypes: {},
  decorators: [
    (Story, context) =>
      WithSitecoreContextDecorator(Story, context, ShareComponent.Variant, false),
  ],
};

export default meta;

type Story = StoryObj<typeof ShareComponent.Variant>;

export const Default: Story = {
  args: {
    params: {
      name: "Share",
    },
    rendering: {
      uid: "{00000000-0000-0000-0000-000000000000}",
      componentName: "Share",
      dataSource: "{00000000-0000-0000-0000-000000000000}",
    },
    fields: {
      "data": {
          "socials": {
              "children": {
                  "results": [
                      {
                          "name": "Facebook",
                          "icon": {
                              "value": "Facebook"
                          },
                          "share": {
                              "jsonValue": {
                                  "value": {
                                      "href": "https://www.facebook.com/sharer.php?u=",
                                      "linktype": "external",
                                      "url": "https://www.facebook.com/sharer.php?u=",
                                      "anchor": "",
                                      "target": ""
                                  }
                              }
                          }
                      },
                      {
                          "name": "Instagram",
                          "icon": {
                              "value": "Instagram"
                          },
                          "share": {
                              "jsonValue": {
                                  "value": {
                                      "href": ""
                                  }
                              }
                          }
                      },
                      {
                          "name": "LinkedIn",
                          "icon": {
                              "value": "Linkedin"
                          },
                          "share": {
                              "jsonValue": {
                                  "value": {
                                      "href": "https://www.linkedin.com/shareArticle?url=",
                                      "linktype": "external",
                                      "url": "https://www.linkedin.com/shareArticle?url=",
                                      "anchor": "",
                                      "target": ""
                                  }
                              }
                          }
                      },
                      {
                          "name": "Twitter",
                          "icon": {
                              "value": "Twitter"
                          },
                          "share": {
                              "jsonValue": {
                                  "value": {
                                      "href": "https://twitter.com/intent/tweet?&url=",
                                      "linktype": "external",
                                      "url": "https://twitter.com/intent/tweet?&url=",
                                      "anchor": "",
                                      "target": ""
                                  }
                              }
                          }
                      }
                  ]
              }
          }
      }
    }
  },
};
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { Navbar, SessionProvider } from "ui";
import { EPSidebarData1 } from "../../../../data/ep-sidebar.data";
import { EPHeaderData } from "../../../../data/ep-header.data";

const meta: Meta<typeof Navbar> = {
  title: "ui/Atomic/Organisms/Navbar/Navbar",
  component: Navbar,
  tags: ["autodocs", "ui", "molecules", "navbar-secondary"],
  parameters: {},
  argTypes: {},
};
export default meta;
type Story = StoryObj<typeof Navbar>;
export const Schema: Story = {
  render: (args) => {
    return (
      <div className="h-40 bg-[#fafafa] min-h-screen">
        <SessionProvider>
          <Navbar siteNavigation={args.siteNavigation} />
        </SessionProvider>
      </div>
    );
  },
  args: {
    siteNavigation: EPHeaderData.fields.data.siteNavigation
  },
};

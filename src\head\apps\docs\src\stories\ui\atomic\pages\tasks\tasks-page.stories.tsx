import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { TasksPage } from "ui";
const meta: Meta<typeof TasksPage> = {
  title: "ui/Atomic/Pages/Tasks/TasksPage",
  component: TasksPage,
  tags: ["autodocs", "ui", "molecules", "tasks-page"],
  parameters: {},
  argTypes: {},
};
export default meta;
type Story = StoryObj<typeof TasksPage>;
export const Schema: Story = {
  render: (args) => {
    return (
      <div className="h-[40rem]">
        <TasksPage {...args} />
      </div>
    );
  },
  args: {},
};

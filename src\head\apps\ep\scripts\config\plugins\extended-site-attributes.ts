import { ConfigPlugin } from '..';
import { JssConfig } from 'lib/config';
import { SiteAttributePluginCore } from '@uxbee/uxbee-sitecore-headless-sxa-multisite';
import { createGraphQLClientFactory } from 'lib/graphql-client-factory/create';

class ExtendedSiteAttributesPlugin implements ConfigPlugin {
  order = 70;

  async exec(config: JssConfig): Promise<JssConfig> {
    const siteAttributesPluginCore = new SiteAttributePluginCore({
      clientFactory: createGraphQLClientFactory(config),
      sitesString: config.sites,
    });

    let sites = await siteAttributesPluginCore.initializeSiteAttributes();
    if (!sites) {
      console.warn('Impossible to retrieve root paths for sites.');
      sites = [];
    }

    return Object.assign({}, config, {
      sites: JSON.stringify(sites),
    });
  }
}

export const extendedSiteAttributesPlugin = new ExtendedSiteAttributesPlugin();

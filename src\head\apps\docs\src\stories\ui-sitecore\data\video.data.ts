export const VideoData = {
  params: {
    name: "Video",
    componentName: "Video",
    tag: "h1",
    GridParameters: "",
    Styles: "",
  },
  rendering: {
    uid: "{00000000-0000-0000-0000-000000000000}",
    componentName: "Video",
    dataSource: "{00000000-0000-0000-0000-000000000000}",
  },
  fields: {
    data: {
      video: {
        template: {
          name: "Youtube",
        },
        title: {
          jsonValue: {
            value: "Video Title",
          }
        },
        baseUrl: {
          value: "https://www.youtube.com/embed/",
        },
        videoId: {
          value: "tgbNymZ7vqY",
        },
        mP4Movie: {
          value: {
            href:"https://www.youtube.com/embed/tgbNymZ7vqY?autoplay=1&mute=1"
          }
        },
        posterImage: {
          jsonValue: {
            value: {
              src: "https://images.unsplash.com/photo-1550745165-9bc0b252726f?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
            }
          }
        },
      }
    }
  },
};

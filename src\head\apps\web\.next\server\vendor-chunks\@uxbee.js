"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@uxbee";
exports.ids = ["vendor-chunks/@uxbee"];
exports.modules = {

/***/ "(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/config-plugins/default-site-root-path-plugin-core.js":
/*!*************************************************************************************************************************************************************!*\
  !*** ../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/config-plugins/default-site-root-path-plugin-core.js ***!
  \*************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DefaultSiteRootPathPluginCore: () => (/* binding */ DefaultSiteRootPathPluginCore)\n/* harmony export */ });\n/* harmony import */ var _lib_util__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/util */ \"(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/util.js\");\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n\nclass DefaultSiteRootPathPluginCore {\n    constructor(config) {\n        this.config = config;\n    }\n    initializeDefaultSiteRootPath() {\n        return __awaiter(this, void 0, void 0, function* () {\n            if (!this.config.sitecoreSiteName || this.config.sitecoreSiteName === '') {\n                console.warn('\"sitecoreSiteName\" property is not defined. Skipping default site root path initialization.');\n                return null;\n            }\n            const defaultSiteName = this.config.sitecoreSiteName;\n            const siteInfoCollection = JSON.parse(this.config.sitesString || '[]');\n            let defaultSiteRootPath = null;\n            if (siteInfoCollection && siteInfoCollection.length > 0) {\n                const defaultSiteInfoMatch = siteInfoCollection.find((siteInfo) => siteInfo.name.toLocaleLowerCase() === (defaultSiteName === null || defaultSiteName === void 0 ? void 0 : defaultSiteName.toLocaleLowerCase()));\n                if (defaultSiteInfoMatch) {\n                    defaultSiteRootPath = (0,_lib_util__WEBPACK_IMPORTED_MODULE_0__.getSiteRootPath)(defaultSiteInfoMatch);\n                }\n            }\n            return defaultSiteRootPath;\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/config-plugins/default-site-root-path-plugin-core.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/config-plugins/site-attribute-plugin-core.js":
/*!*****************************************************************************************************************************************************!*\
  !*** ../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/config-plugins/site-attribute-plugin-core.js ***!
  \*****************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SiteAttributePluginCore: () => (/* binding */ SiteAttributePluginCore)\n/* harmony export */ });\n/* harmony import */ var _sitecore_jss_sitecore_jss_site__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @sitecore-jss/sitecore-jss/site */ \"@sitecore-jss/sitecore-jss/site\");\n/* harmony import */ var _sitecore_jss_sitecore_jss_site__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_sitecore_jss_sitecore_jss_site__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_graphql_site_attribute_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/graphql-site-attribute-service */ \"(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/graphql-site-attribute-service.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../constants */ \"(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/constants.js\");\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n\n\n\nclass SiteAttributePluginCore {\n    constructor(config) {\n        this.config = config;\n    }\n    initializeSiteAttributes() {\n        return __awaiter(this, void 0, void 0, function* () {\n            const result = [];\n            const clientFactory = this.config.clientFactory;\n            try {\n                let configSites = this.parseSitesFromSitesString();\n                if (!configSites) {\n                    // No Sitecore multisite addon installed. Fetch site info by our own.\n                    configSites = yield this.fetchSiteInfo(clientFactory);\n                }\n                console.log('Fetching site attributes');\n                const siteAttributeService = new _lib_graphql_site_attribute_service__WEBPACK_IMPORTED_MODULE_1__.GraphQLSiteAttributeService({\n                    clientFactory,\n                    fetch: fetch,\n                });\n                const siteAttributes = yield siteAttributeService.fetchSiteAttributes();\n                configSites.forEach((siteInfo) => {\n                    const site = siteAttributes.find((x) => x.name.toLocaleLowerCase() === siteInfo.name.toLocaleLowerCase());\n                    const extendedSiteProperties = {};\n                    if (site) {\n                        _constants__WEBPACK_IMPORTED_MODULE_2__.ExtendedSiteAttributes.forEach((attributeName) => {\n                            const siteAttribute = site.attributes.find((attr) => attr.key === attributeName);\n                            if (siteAttribute) {\n                                extendedSiteProperties[attributeName] = siteAttribute.value;\n                            }\n                        });\n                        // Sitecore Experience Edge doesn't return 'rootPath' attribute in the site attributes object.\n                        // Set it explicitly.\n                        extendedSiteProperties[_constants__WEBPACK_IMPORTED_MODULE_2__.SITE_ROOT_PATH_PARAMETER_NAME] = site.rootPath;\n                    }\n                    result.push(Object.assign({}, siteInfo, extendedSiteProperties));\n                });\n            }\n            catch (error) {\n                console.error('Error at fetching site root paths');\n                console.error(error);\n                return null;\n            }\n            return result;\n        });\n    }\n    parseSitesFromSitesString() {\n        var _a;\n        if (!this.config.sitesString || this.config.sitesString === '') {\n            return null;\n        }\n        return (_a = JSON.parse(this.config.sitesString)) !== null && _a !== void 0 ? _a : [];\n    }\n    fetchSiteInfo(clientFactory) {\n        return __awaiter(this, void 0, void 0, function* () {\n            const siteInfoService = new _sitecore_jss_sitecore_jss_site__WEBPACK_IMPORTED_MODULE_0__.GraphQLSiteInfoService({ clientFactory });\n            return yield siteInfoService.fetchSiteInfo();\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9wYWNrYWdlcy9jb250ZW50LXRva2Vucy9ub2RlX21vZHVsZXMvQHV4YmVlL3V4YmVlLXNpdGVjb3JlLWhlYWRsZXNzLXN4YS1tdWx0aXNpdGUvZGlzdC9lc20vY29uZmlnLXBsdWdpbnMvc2l0ZS1hdHRyaWJ1dGUtcGx1Z2luLWNvcmUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBQSxpQkFBaUIsU0FBSSxJQUFJLFNBQUk7QUFDN0IsNEJBQTRCLCtEQUErRCxpQkFBaUI7QUFDNUc7QUFDQSxvQ0FBb0MsTUFBTSwrQkFBK0IsWUFBWTtBQUNyRixtQ0FBbUMsTUFBTSxtQ0FBbUMsWUFBWTtBQUN4RixnQ0FBZ0M7QUFDaEM7QUFDQSxLQUFLO0FBQ0w7QUFDeUU7QUFDVztBQUNDO0FBQzlFO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlEQUFpRCw0RkFBMkI7QUFDNUU7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLDhEQUFzQjtBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QjtBQUN6QjtBQUNBO0FBQ0EsK0NBQStDLHFFQUE2QjtBQUM1RTtBQUNBLGdEQUFnRDtBQUNoRCxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3Q0FBd0MsbUZBQXNCLEdBQUcsZUFBZTtBQUNoRjtBQUNBLFNBQVM7QUFDVDtBQUNBIiwic291cmNlcyI6WyJEOlxcV29ya1xcVVhCRUVcXEZvdW5kYXRpb25YTUNsb3VkMlxcc3JjXFxoZWFkXFxwYWNrYWdlc1xcY29udGVudC10b2tlbnNcXG5vZGVfbW9kdWxlc1xcQHV4YmVlXFx1eGJlZS1zaXRlY29yZS1oZWFkbGVzcy1zeGEtbXVsdGlzaXRlXFxkaXN0XFxlc21cXGNvbmZpZy1wbHVnaW5zXFxzaXRlLWF0dHJpYnV0ZS1wbHVnaW4tY29yZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgX19hd2FpdGVyID0gKHRoaXMgJiYgdGhpcy5fX2F3YWl0ZXIpIHx8IGZ1bmN0aW9uICh0aGlzQXJnLCBfYXJndW1lbnRzLCBQLCBnZW5lcmF0b3IpIHtcbiAgICBmdW5jdGlvbiBhZG9wdCh2YWx1ZSkgeyByZXR1cm4gdmFsdWUgaW5zdGFuY2VvZiBQID8gdmFsdWUgOiBuZXcgUChmdW5jdGlvbiAocmVzb2x2ZSkgeyByZXNvbHZlKHZhbHVlKTsgfSk7IH1cbiAgICByZXR1cm4gbmV3IChQIHx8IChQID0gUHJvbWlzZSkpKGZ1bmN0aW9uIChyZXNvbHZlLCByZWplY3QpIHtcbiAgICAgICAgZnVuY3Rpb24gZnVsZmlsbGVkKHZhbHVlKSB7IHRyeSB7IHN0ZXAoZ2VuZXJhdG9yLm5leHQodmFsdWUpKTsgfSBjYXRjaCAoZSkgeyByZWplY3QoZSk7IH0gfVxuICAgICAgICBmdW5jdGlvbiByZWplY3RlZCh2YWx1ZSkgeyB0cnkgeyBzdGVwKGdlbmVyYXRvcltcInRocm93XCJdKHZhbHVlKSk7IH0gY2F0Y2ggKGUpIHsgcmVqZWN0KGUpOyB9IH1cbiAgICAgICAgZnVuY3Rpb24gc3RlcChyZXN1bHQpIHsgcmVzdWx0LmRvbmUgPyByZXNvbHZlKHJlc3VsdC52YWx1ZSkgOiBhZG9wdChyZXN1bHQudmFsdWUpLnRoZW4oZnVsZmlsbGVkLCByZWplY3RlZCk7IH1cbiAgICAgICAgc3RlcCgoZ2VuZXJhdG9yID0gZ2VuZXJhdG9yLmFwcGx5KHRoaXNBcmcsIF9hcmd1bWVudHMgfHwgW10pKS5uZXh0KCkpO1xuICAgIH0pO1xufTtcbmltcG9ydCB7IEdyYXBoUUxTaXRlSW5mb1NlcnZpY2UgfSBmcm9tICdAc2l0ZWNvcmUtanNzL3NpdGVjb3JlLWpzcy9zaXRlJztcbmltcG9ydCB7IEdyYXBoUUxTaXRlQXR0cmlidXRlU2VydmljZSB9IGZyb20gJy4uL2xpYi9ncmFwaHFsLXNpdGUtYXR0cmlidXRlLXNlcnZpY2UnO1xuaW1wb3J0IHsgRXh0ZW5kZWRTaXRlQXR0cmlidXRlcywgU0lURV9ST09UX1BBVEhfUEFSQU1FVEVSX05BTUUgfSBmcm9tICcuLi9jb25zdGFudHMnO1xuZXhwb3J0IGNsYXNzIFNpdGVBdHRyaWJ1dGVQbHVnaW5Db3JlIHtcbiAgICBjb25zdHJ1Y3Rvcihjb25maWcpIHtcbiAgICAgICAgdGhpcy5jb25maWcgPSBjb25maWc7XG4gICAgfVxuICAgIGluaXRpYWxpemVTaXRlQXR0cmlidXRlcygpIHtcbiAgICAgICAgcmV0dXJuIF9fYXdhaXRlcih0aGlzLCB2b2lkIDAsIHZvaWQgMCwgZnVuY3Rpb24qICgpIHtcbiAgICAgICAgICAgIGNvbnN0IHJlc3VsdCA9IFtdO1xuICAgICAgICAgICAgY29uc3QgY2xpZW50RmFjdG9yeSA9IHRoaXMuY29uZmlnLmNsaWVudEZhY3Rvcnk7XG4gICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgIGxldCBjb25maWdTaXRlcyA9IHRoaXMucGFyc2VTaXRlc0Zyb21TaXRlc1N0cmluZygpO1xuICAgICAgICAgICAgICAgIGlmICghY29uZmlnU2l0ZXMpIHtcbiAgICAgICAgICAgICAgICAgICAgLy8gTm8gU2l0ZWNvcmUgbXVsdGlzaXRlIGFkZG9uIGluc3RhbGxlZC4gRmV0Y2ggc2l0ZSBpbmZvIGJ5IG91ciBvd24uXG4gICAgICAgICAgICAgICAgICAgIGNvbmZpZ1NpdGVzID0geWllbGQgdGhpcy5mZXRjaFNpdGVJbmZvKGNsaWVudEZhY3RvcnkpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygnRmV0Y2hpbmcgc2l0ZSBhdHRyaWJ1dGVzJyk7XG4gICAgICAgICAgICAgICAgY29uc3Qgc2l0ZUF0dHJpYnV0ZVNlcnZpY2UgPSBuZXcgR3JhcGhRTFNpdGVBdHRyaWJ1dGVTZXJ2aWNlKHtcbiAgICAgICAgICAgICAgICAgICAgY2xpZW50RmFjdG9yeSxcbiAgICAgICAgICAgICAgICAgICAgZmV0Y2g6IGZldGNoLFxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIGNvbnN0IHNpdGVBdHRyaWJ1dGVzID0geWllbGQgc2l0ZUF0dHJpYnV0ZVNlcnZpY2UuZmV0Y2hTaXRlQXR0cmlidXRlcygpO1xuICAgICAgICAgICAgICAgIGNvbmZpZ1NpdGVzLmZvckVhY2goKHNpdGVJbmZvKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHNpdGUgPSBzaXRlQXR0cmlidXRlcy5maW5kKCh4KSA9PiB4Lm5hbWUudG9Mb2NhbGVMb3dlckNhc2UoKSA9PT0gc2l0ZUluZm8ubmFtZS50b0xvY2FsZUxvd2VyQ2FzZSgpKTtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZXh0ZW5kZWRTaXRlUHJvcGVydGllcyA9IHt9O1xuICAgICAgICAgICAgICAgICAgICBpZiAoc2l0ZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgRXh0ZW5kZWRTaXRlQXR0cmlidXRlcy5mb3JFYWNoKChhdHRyaWJ1dGVOYW1lKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3Qgc2l0ZUF0dHJpYnV0ZSA9IHNpdGUuYXR0cmlidXRlcy5maW5kKChhdHRyKSA9PiBhdHRyLmtleSA9PT0gYXR0cmlidXRlTmFtZSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHNpdGVBdHRyaWJ1dGUpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZXh0ZW5kZWRTaXRlUHJvcGVydGllc1thdHRyaWJ1dGVOYW1lXSA9IHNpdGVBdHRyaWJ1dGUudmFsdWU7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBTaXRlY29yZSBFeHBlcmllbmNlIEVkZ2UgZG9lc24ndCByZXR1cm4gJ3Jvb3RQYXRoJyBhdHRyaWJ1dGUgaW4gdGhlIHNpdGUgYXR0cmlidXRlcyBvYmplY3QuXG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBTZXQgaXQgZXhwbGljaXRseS5cbiAgICAgICAgICAgICAgICAgICAgICAgIGV4dGVuZGVkU2l0ZVByb3BlcnRpZXNbU0lURV9ST09UX1BBVEhfUEFSQU1FVEVSX05BTUVdID0gc2l0ZS5yb290UGF0aDtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICByZXN1bHQucHVzaChPYmplY3QuYXNzaWduKHt9LCBzaXRlSW5mbywgZXh0ZW5kZWRTaXRlUHJvcGVydGllcykpO1xuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgYXQgZmV0Y2hpbmcgc2l0ZSByb290IHBhdGhzJyk7XG4gICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcihlcnJvcik7XG4gICAgICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gcmVzdWx0O1xuICAgICAgICB9KTtcbiAgICB9XG4gICAgcGFyc2VTaXRlc0Zyb21TaXRlc1N0cmluZygpIHtcbiAgICAgICAgdmFyIF9hO1xuICAgICAgICBpZiAoIXRoaXMuY29uZmlnLnNpdGVzU3RyaW5nIHx8IHRoaXMuY29uZmlnLnNpdGVzU3RyaW5nID09PSAnJykge1xuICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIChfYSA9IEpTT04ucGFyc2UodGhpcy5jb25maWcuc2l0ZXNTdHJpbmcpKSAhPT0gbnVsbCAmJiBfYSAhPT0gdm9pZCAwID8gX2EgOiBbXTtcbiAgICB9XG4gICAgZmV0Y2hTaXRlSW5mbyhjbGllbnRGYWN0b3J5KSB7XG4gICAgICAgIHJldHVybiBfX2F3YWl0ZXIodGhpcywgdm9pZCAwLCB2b2lkIDAsIGZ1bmN0aW9uKiAoKSB7XG4gICAgICAgICAgICBjb25zdCBzaXRlSW5mb1NlcnZpY2UgPSBuZXcgR3JhcGhRTFNpdGVJbmZvU2VydmljZSh7IGNsaWVudEZhY3RvcnkgfSk7XG4gICAgICAgICAgICByZXR1cm4geWllbGQgc2l0ZUluZm9TZXJ2aWNlLmZldGNoU2l0ZUluZm8oKTtcbiAgICAgICAgfSk7XG4gICAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/config-plugins/site-attribute-plugin-core.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/config-plugins/tenant-plugin-core.js":
/*!*********************************************************************************************************************************************!*\
  !*** ../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/config-plugins/tenant-plugin-core.js ***!
  \*********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TenantPluginCore: () => (/* binding */ TenantPluginCore)\n/* harmony export */ });\n/* harmony import */ var _lib_graphql_tenant_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/graphql-tenant-service */ \"(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/graphql-tenant-service.js\");\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n\nclass TenantPluginCore {\n    constructor(config) {\n        this.config = config;\n    }\n    getTenants() {\n        return __awaiter(this, void 0, void 0, function* () {\n            const clientFactory = this.config.clientFactory;\n            const fetch = this.config.fetch;\n            try {\n                const tenantService = new _lib_graphql_tenant_service__WEBPACK_IMPORTED_MODULE_0__.GraphQLTenantService({\n                    clientFactory,\n                    fetch,\n                });\n                const tenantResult = yield tenantService.fetchTenantData();\n                return tenantResult !== null && tenantResult !== void 0 ? tenantResult : [];\n            }\n            catch (error) {\n                console.error('Error at fetching tenants');\n                console.error(error);\n                return null;\n            }\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9wYWNrYWdlcy9jb250ZW50LXRva2Vucy9ub2RlX21vZHVsZXMvQHV4YmVlL3V4YmVlLXNpdGVjb3JlLWhlYWRsZXNzLXN4YS1tdWx0aXNpdGUvZGlzdC9lc20vY29uZmlnLXBsdWdpbnMvdGVuYW50LXBsdWdpbi1jb3JlLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUEsaUJBQWlCLFNBQUksSUFBSSxTQUFJO0FBQzdCLDRCQUE0QiwrREFBK0QsaUJBQWlCO0FBQzVHO0FBQ0Esb0NBQW9DLE1BQU0sK0JBQStCLFlBQVk7QUFDckYsbUNBQW1DLE1BQU0sbUNBQW1DLFlBQVk7QUFDeEYsZ0NBQWdDO0FBQ2hDO0FBQ0EsS0FBSztBQUNMO0FBQ3FFO0FBQzlEO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBDQUEwQyw2RUFBb0I7QUFDOUQ7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxXb3JrXFxVWEJFRVxcRm91bmRhdGlvblhNQ2xvdWQyXFxzcmNcXGhlYWRcXHBhY2thZ2VzXFxjb250ZW50LXRva2Vuc1xcbm9kZV9tb2R1bGVzXFxAdXhiZWVcXHV4YmVlLXNpdGVjb3JlLWhlYWRsZXNzLXN4YS1tdWx0aXNpdGVcXGRpc3RcXGVzbVxcY29uZmlnLXBsdWdpbnNcXHRlbmFudC1wbHVnaW4tY29yZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgX19hd2FpdGVyID0gKHRoaXMgJiYgdGhpcy5fX2F3YWl0ZXIpIHx8IGZ1bmN0aW9uICh0aGlzQXJnLCBfYXJndW1lbnRzLCBQLCBnZW5lcmF0b3IpIHtcbiAgICBmdW5jdGlvbiBhZG9wdCh2YWx1ZSkgeyByZXR1cm4gdmFsdWUgaW5zdGFuY2VvZiBQID8gdmFsdWUgOiBuZXcgUChmdW5jdGlvbiAocmVzb2x2ZSkgeyByZXNvbHZlKHZhbHVlKTsgfSk7IH1cbiAgICByZXR1cm4gbmV3IChQIHx8IChQID0gUHJvbWlzZSkpKGZ1bmN0aW9uIChyZXNvbHZlLCByZWplY3QpIHtcbiAgICAgICAgZnVuY3Rpb24gZnVsZmlsbGVkKHZhbHVlKSB7IHRyeSB7IHN0ZXAoZ2VuZXJhdG9yLm5leHQodmFsdWUpKTsgfSBjYXRjaCAoZSkgeyByZWplY3QoZSk7IH0gfVxuICAgICAgICBmdW5jdGlvbiByZWplY3RlZCh2YWx1ZSkgeyB0cnkgeyBzdGVwKGdlbmVyYXRvcltcInRocm93XCJdKHZhbHVlKSk7IH0gY2F0Y2ggKGUpIHsgcmVqZWN0KGUpOyB9IH1cbiAgICAgICAgZnVuY3Rpb24gc3RlcChyZXN1bHQpIHsgcmVzdWx0LmRvbmUgPyByZXNvbHZlKHJlc3VsdC52YWx1ZSkgOiBhZG9wdChyZXN1bHQudmFsdWUpLnRoZW4oZnVsZmlsbGVkLCByZWplY3RlZCk7IH1cbiAgICAgICAgc3RlcCgoZ2VuZXJhdG9yID0gZ2VuZXJhdG9yLmFwcGx5KHRoaXNBcmcsIF9hcmd1bWVudHMgfHwgW10pKS5uZXh0KCkpO1xuICAgIH0pO1xufTtcbmltcG9ydCB7IEdyYXBoUUxUZW5hbnRTZXJ2aWNlIH0gZnJvbSAnLi4vbGliL2dyYXBocWwtdGVuYW50LXNlcnZpY2UnO1xuZXhwb3J0IGNsYXNzIFRlbmFudFBsdWdpbkNvcmUge1xuICAgIGNvbnN0cnVjdG9yKGNvbmZpZykge1xuICAgICAgICB0aGlzLmNvbmZpZyA9IGNvbmZpZztcbiAgICB9XG4gICAgZ2V0VGVuYW50cygpIHtcbiAgICAgICAgcmV0dXJuIF9fYXdhaXRlcih0aGlzLCB2b2lkIDAsIHZvaWQgMCwgZnVuY3Rpb24qICgpIHtcbiAgICAgICAgICAgIGNvbnN0IGNsaWVudEZhY3RvcnkgPSB0aGlzLmNvbmZpZy5jbGllbnRGYWN0b3J5O1xuICAgICAgICAgICAgY29uc3QgZmV0Y2ggPSB0aGlzLmNvbmZpZy5mZXRjaDtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgY29uc3QgdGVuYW50U2VydmljZSA9IG5ldyBHcmFwaFFMVGVuYW50U2VydmljZSh7XG4gICAgICAgICAgICAgICAgICAgIGNsaWVudEZhY3RvcnksXG4gICAgICAgICAgICAgICAgICAgIGZldGNoLFxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIGNvbnN0IHRlbmFudFJlc3VsdCA9IHlpZWxkIHRlbmFudFNlcnZpY2UuZmV0Y2hUZW5hbnREYXRhKCk7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHRlbmFudFJlc3VsdCAhPT0gbnVsbCAmJiB0ZW5hbnRSZXN1bHQgIT09IHZvaWQgMCA/IHRlbmFudFJlc3VsdCA6IFtdO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgYXQgZmV0Y2hpbmcgdGVuYW50cycpO1xuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoZXJyb3IpO1xuICAgICAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICAgICAgfVxuICAgICAgICB9KTtcbiAgICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/config-plugins/tenant-plugin-core.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/constants.js":
/*!*********************************************************************************************************************!*\
  !*** ../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/constants.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ExtendedSiteAttributes: () => (/* binding */ ExtendedSiteAttributes),\n/* harmony export */   SITE_ROOT_PATH_PARAMETER_NAME: () => (/* binding */ SITE_ROOT_PATH_PARAMETER_NAME),\n/* harmony export */   SITE_VIRTUAL_FOLDER_PARAMETER_NAME: () => (/* binding */ SITE_VIRTUAL_FOLDER_PARAMETER_NAME)\n/* harmony export */ });\nconst ExtendedSiteAttributes = ['virtualFolder'];\n// Parameters\nconst SITE_ROOT_PATH_PARAMETER_NAME = 'rootPath';\nconst SITE_VIRTUAL_FOLDER_PARAMETER_NAME = 'virtualFolder';\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9wYWNrYWdlcy9jb250ZW50LXRva2Vucy9ub2RlX21vZHVsZXMvQHV4YmVlL3V4YmVlLXNpdGVjb3JlLWhlYWRsZXNzLXN4YS1tdWx0aXNpdGUvZGlzdC9lc20vY29uc3RhbnRzLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFPO0FBQ1A7QUFDTztBQUNBIiwic291cmNlcyI6WyJEOlxcV29ya1xcVVhCRUVcXEZvdW5kYXRpb25YTUNsb3VkMlxcc3JjXFxoZWFkXFxwYWNrYWdlc1xcY29udGVudC10b2tlbnNcXG5vZGVfbW9kdWxlc1xcQHV4YmVlXFx1eGJlZS1zaXRlY29yZS1oZWFkbGVzcy1zeGEtbXVsdGlzaXRlXFxkaXN0XFxlc21cXGNvbnN0YW50cy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgRXh0ZW5kZWRTaXRlQXR0cmlidXRlcyA9IFsndmlydHVhbEZvbGRlciddO1xuLy8gUGFyYW1ldGVyc1xuZXhwb3J0IGNvbnN0IFNJVEVfUk9PVF9QQVRIX1BBUkFNRVRFUl9OQU1FID0gJ3Jvb3RQYXRoJztcbmV4cG9ydCBjb25zdCBTSVRFX1ZJUlRVQUxfRk9MREVSX1BBUkFNRVRFUl9OQU1FID0gJ3ZpcnR1YWxGb2xkZXInO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/constants.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/index.js":
/*!*****************************************************************************************************************!*\
  !*** ../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/index.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _lib_graphql_site_attribute_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/graphql-site-attribute-service */ \"(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/graphql-site-attribute-service.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _lib_graphql_site_attribute_service__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _lib_graphql_site_attribute_service__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _lib_graphql_tenant_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/graphql-tenant-service */ \"(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/graphql-tenant-service.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _lib_graphql_tenant_service__WEBPACK_IMPORTED_MODULE_1__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _lib_graphql_tenant_service__WEBPACK_IMPORTED_MODULE_1__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _config_plugins_site_attribute_plugin_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./config-plugins/site-attribute-plugin-core */ \"(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/config-plugins/site-attribute-plugin-core.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _config_plugins_site_attribute_plugin_core__WEBPACK_IMPORTED_MODULE_2__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _config_plugins_site_attribute_plugin_core__WEBPACK_IMPORTED_MODULE_2__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _config_plugins_default_site_root_path_plugin_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./config-plugins/default-site-root-path-plugin-core */ \"(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/config-plugins/default-site-root-path-plugin-core.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _config_plugins_default_site_root_path_plugin_core__WEBPACK_IMPORTED_MODULE_3__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _config_plugins_default_site_root_path_plugin_core__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _config_plugins_tenant_plugin_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./config-plugins/tenant-plugin-core */ \"(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/config-plugins/tenant-plugin-core.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _config_plugins_tenant_plugin_core__WEBPACK_IMPORTED_MODULE_4__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _config_plugins_tenant_plugin_core__WEBPACK_IMPORTED_MODULE_4__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _site_resolving_plugins_remove_default_site_duplicate_plugin_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./site-resolving-plugins/remove-default-site-duplicate-plugin-core */ \"(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/site-resolving-plugins/remove-default-site-duplicate-plugin-core.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _site_resolving_plugins_remove_default_site_duplicate_plugin_core__WEBPACK_IMPORTED_MODULE_5__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _site_resolving_plugins_remove_default_site_duplicate_plugin_core__WEBPACK_IMPORTED_MODULE_5__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _lib_virtual_folder_aware_site_resolver__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./lib/virtual-folder-aware-site-resolver */ \"(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/virtual-folder-aware-site-resolver.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _lib_virtual_folder_aware_site_resolver__WEBPACK_IMPORTED_MODULE_6__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _lib_virtual_folder_aware_site_resolver__WEBPACK_IMPORTED_MODULE_6__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _lib_tenant_resolver__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./lib/tenant-resolver */ \"(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/tenant-resolver.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _lib_tenant_resolver__WEBPACK_IMPORTED_MODULE_7__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _lib_tenant_resolver__WEBPACK_IMPORTED_MODULE_7__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _middleware_site_virtual_folder_middleware__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./middleware/site-virtual-folder-middleware */ \"(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/site-virtual-folder-middleware.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _middleware_site_virtual_folder_middleware__WEBPACK_IMPORTED_MODULE_8__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _middleware_site_virtual_folder_middleware__WEBPACK_IMPORTED_MODULE_8__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _middleware_handle_default_locale_middleware__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./middleware/handle-default-locale-middleware */ \"(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/handle-default-locale-middleware.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _middleware_handle_default_locale_middleware__WEBPACK_IMPORTED_MODULE_9__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _middleware_handle_default_locale_middleware__WEBPACK_IMPORTED_MODULE_9__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _middleware_default_locale_url_rewrite_middleware__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./middleware/default-locale-url-rewrite-middleware */ \"(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/default-locale-url-rewrite-middleware.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _middleware_default_locale_url_rewrite_middleware__WEBPACK_IMPORTED_MODULE_10__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _middleware_default_locale_url_rewrite_middleware__WEBPACK_IMPORTED_MODULE_10__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./constants */ \"(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/constants.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _constants__WEBPACK_IMPORTED_MODULE_11__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _constants__WEBPACK_IMPORTED_MODULE_11__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _lib_util__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./lib/util */ \"(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/util.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _lib_util__WEBPACK_IMPORTED_MODULE_12__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _lib_util__WEBPACK_IMPORTED_MODULE_12__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n// services\n\n\n// configuration plugins\n\n\n\n\n// site resolver\n\n// tenant resolver\n\n// middleware\n\n\n\n// constants\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9wYWNrYWdlcy9jb250ZW50LXRva2Vucy9ub2RlX21vZHVsZXMvQHV4YmVlL3V4YmVlLXNpdGVjb3JlLWhlYWRsZXNzLXN4YS1tdWx0aXNpdGUvZGlzdC9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQTtBQUNxRDtBQUNSO0FBQzdDO0FBQzREO0FBQ1E7QUFDaEI7QUFDK0I7QUFDbkY7QUFDeUQ7QUFDekQ7QUFDc0M7QUFDdEM7QUFDNEQ7QUFDRTtBQUNLO0FBQ25FO0FBQzRCO0FBQ0QiLCJzb3VyY2VzIjpbIkQ6XFxXb3JrXFxVWEJFRVxcRm91bmRhdGlvblhNQ2xvdWQyXFxzcmNcXGhlYWRcXHBhY2thZ2VzXFxjb250ZW50LXRva2Vuc1xcbm9kZV9tb2R1bGVzXFxAdXhiZWVcXHV4YmVlLXNpdGVjb3JlLWhlYWRsZXNzLXN4YS1tdWx0aXNpdGVcXGRpc3RcXGVzbVxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc2VydmljZXNcbmV4cG9ydCAqIGZyb20gJy4vbGliL2dyYXBocWwtc2l0ZS1hdHRyaWJ1dGUtc2VydmljZSc7XG5leHBvcnQgKiBmcm9tICcuL2xpYi9ncmFwaHFsLXRlbmFudC1zZXJ2aWNlJztcbi8vIGNvbmZpZ3VyYXRpb24gcGx1Z2luc1xuZXhwb3J0ICogZnJvbSAnLi9jb25maWctcGx1Z2lucy9zaXRlLWF0dHJpYnV0ZS1wbHVnaW4tY29yZSc7XG5leHBvcnQgKiBmcm9tICcuL2NvbmZpZy1wbHVnaW5zL2RlZmF1bHQtc2l0ZS1yb290LXBhdGgtcGx1Z2luLWNvcmUnO1xuZXhwb3J0ICogZnJvbSAnLi9jb25maWctcGx1Z2lucy90ZW5hbnQtcGx1Z2luLWNvcmUnO1xuZXhwb3J0ICogZnJvbSAnLi9zaXRlLXJlc29sdmluZy1wbHVnaW5zL3JlbW92ZS1kZWZhdWx0LXNpdGUtZHVwbGljYXRlLXBsdWdpbi1jb3JlJztcbi8vIHNpdGUgcmVzb2x2ZXJcbmV4cG9ydCAqIGZyb20gJy4vbGliL3ZpcnR1YWwtZm9sZGVyLWF3YXJlLXNpdGUtcmVzb2x2ZXInO1xuLy8gdGVuYW50IHJlc29sdmVyXG5leHBvcnQgKiBmcm9tICcuL2xpYi90ZW5hbnQtcmVzb2x2ZXInO1xuLy8gbWlkZGxld2FyZVxuZXhwb3J0ICogZnJvbSAnLi9taWRkbGV3YXJlL3NpdGUtdmlydHVhbC1mb2xkZXItbWlkZGxld2FyZSc7XG5leHBvcnQgKiBmcm9tICcuL21pZGRsZXdhcmUvaGFuZGxlLWRlZmF1bHQtbG9jYWxlLW1pZGRsZXdhcmUnO1xuZXhwb3J0ICogZnJvbSAnLi9taWRkbGV3YXJlL2RlZmF1bHQtbG9jYWxlLXVybC1yZXdyaXRlLW1pZGRsZXdhcmUnO1xuLy8gY29uc3RhbnRzXG5leHBvcnQgKiBmcm9tICcuL2NvbnN0YW50cyc7XG5leHBvcnQgKiBmcm9tICcuL2xpYi91dGlsJztcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/index.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/graphql-site-attribute-service.js":
/*!**********************************************************************************************************************************************!*\
  !*** ../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/graphql-site-attribute-service.js ***!
  \**********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GraphQLSiteAttributeService: () => (/* binding */ GraphQLSiteAttributeService)\n/* harmony export */ });\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nconst query = `\r\n  {\r\n    site {\r\n      siteInfoCollection {\r\n        name,\r\n        rootPath,\r\n        attributes {\r\n          key,\r\n          value\r\n        }\r\n      }\r\n    }\r\n  }\r\n`;\nclass GraphQLSiteAttributeService {\n    get query() {\n        return query;\n    }\n    constructor(options) {\n        this.graphQlClient = options.clientFactory({\n            fetch: options.fetch,\n        });\n    }\n    fetchSiteAttributes() {\n        return __awaiter(this, void 0, void 0, function* () {\n            var _a;\n            const queryResult = yield this.graphQlClient.request(this.query);\n            const siteData = (_a = queryResult === null || queryResult === void 0 ? void 0 : queryResult.site) === null || _a === void 0 ? void 0 : _a.siteInfoCollection;\n            if (!siteData) {\n                throw new Error('No site data from site collection.');\n            }\n            const result = [];\n            siteData.forEach((site) => result.push(site));\n            return result;\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/graphql-site-attribute-service.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/graphql-tenant-service.js":
/*!**************************************************************************************************************************************!*\
  !*** ../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/graphql-tenant-service.js ***!
  \**************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GraphQLTenantService: () => (/* binding */ GraphQLTenantService)\n/* harmony export */ });\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nconst TENANT_TEMPLATE_ID = '78180355F0A24161A34C3069A9E17539';\nconst CONTENT_ROOT_PATH_ID = '0DE95AE441AB4D019EB067441B7C2450';\nconst TENANT_SHARED_SITES_FIELD_NAME = 'SharedSites';\nconst SITE_ROOT_TEMPLATE_ID = 'A2B9FDC3F641496694A5B63944DC39DE';\nconst TENANTS_PAGESIZE = '50';\nconst SITES_PAGESIZE = '200';\nconst query = `\r\n  query TenantQuery {\r\n    tenants: search(where: {\r\n      AND: [\r\n        {\r\n          name: \"_templates\",\r\n          value: \"${TENANT_TEMPLATE_ID}\",\r\n          operator: CONTAINS\r\n        },\r\n        {\r\n          name: \"_path\",\r\n          value: \"${CONTENT_ROOT_PATH_ID}\",\r\n          operator: CONTAINS\r\n        },\r\n        { name: \"_language\", value: \"en\" }\r\n      ]\r\n    }, first: ${TENANTS_PAGESIZE}) {\r\n      results {\r\n        id,\r\n        path,\r\n        sharedSites: field(name: \"${TENANT_SHARED_SITES_FIELD_NAME}\") {\r\n          ... on MultilistField {\r\n            targetItems {\r\n              id,\r\n              path\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    sites: search(where: {\r\n      AND: [\r\n        {\r\n          name: \"_templates\",\r\n          value: \"${SITE_ROOT_TEMPLATE_ID}\",\r\n          operator: CONTAINS\r\n        },\r\n        {\r\n          name: \"_path\",\r\n          value: \"${CONTENT_ROOT_PATH_ID}\",\r\n          operator: CONTAINS\r\n        }\r\n      ]\r\n    }, first: ${SITES_PAGESIZE}){\r\n      results {\r\n        id,\r\n        path\r\n      }\r\n    },\r\n    siteNames: site {\r\n      siteInfoCollection {\r\n        name,\r\n        rootPath\r\n      }\r\n    }\r\n  }\r\n`;\nclass GraphQLTenantService {\n    constructor(options) {\n        this.graphQlClient = options.clientFactory({\n            fetch: options.fetch,\n        });\n    }\n    fetchTenantData() {\n        return __awaiter(this, void 0, void 0, function* () {\n            const queryResult = yield this.graphQlClient.request(query);\n            if (!queryResult) {\n                console.warn('No data from TenantQuery.');\n                return undefined;\n            }\n            const tenantQueryResponse = queryResult.tenants.results;\n            const sitesQueryResults = queryResult.sites.results;\n            const result = [];\n            //Remove duplicates from site list\n            const allSites = sitesQueryResults.filter((site, index, self) => {\n                return self.findIndex((p) => p.id === site.id) === index;\n            });\n            //All tenant paths sorted by length, so we can check if a site is part of a tenant, most specific first\n            const allTenantPaths = tenantQueryResponse\n                .map((tenant) => tenant.path)\n                .sort((a, b) => b.length - a.length);\n            tenantQueryResponse.forEach((tenant) => {\n                result.push({\n                    id: tenant.id,\n                    path: tenant.path,\n                    sharedSites: tenant.sharedSites.targetItems\n                        .map((x) => {\n                        return {\n                            id: x.id,\n                            name: this.getSiteNameByRootPath(x.path, queryResult.siteNames.siteInfoCollection),\n                            path: x.path,\n                        };\n                    })\n                        .filter((x) => x.name !== ''),\n                    sites: this.getAllSitesForTenant(allSites, allTenantPaths, tenant.path, queryResult.siteNames.siteInfoCollection),\n                });\n            });\n            return result;\n        });\n    }\n    getAllSitesForTenant(sites, allTenantPaths, tenantPath, siteInfoCollection) {\n        const result = [];\n        sites.forEach((site) => {\n            if (site.path.toLocaleLowerCase().startsWith(tenantPath.toLocaleLowerCase())) {\n                //Site is part of this tenant, but also check if this is the most specific tenant\n                const matchedTenant = allTenantPaths.find((x) => site.path.toLocaleLowerCase().startsWith(x.toLocaleLowerCase()));\n                if (matchedTenant && matchedTenant.toLocaleLowerCase() === tenantPath.toLocaleLowerCase()) {\n                    result.push({\n                        id: site.id,\n                        name: this.getSiteNameByRootPath(site.path, siteInfoCollection),\n                        path: site.path,\n                    });\n                }\n            }\n        });\n        return result.filter((x) => x.name !== '');\n    }\n    getSiteNameByRootPath(rootPath, siteInfoQueryResult) {\n        const rootPathNormalized = rootPath.toLowerCase();\n        const siteInfoMatch = siteInfoQueryResult.find((site) => site.rootPath.toLowerCase() === rootPathNormalized);\n        if (!siteInfoMatch) {\n            console.warn(`[GraphQLTenantService] Couldn't find tenant site info match by root path (${rootPathNormalized}).`);\n            return '';\n        }\n        return siteInfoMatch.name;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/graphql-tenant-service.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/tenant-resolver.js":
/*!*******************************************************************************************************************************!*\
  !*** ../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/tenant-resolver.js ***!
  \*******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TenantResolver: () => (/* binding */ TenantResolver)\n/* harmony export */ });\n/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util */ \"(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/util.js\");\n\nclass TenantResolver {\n    constructor(tenants) {\n        this.tenants = tenants;\n    }\n    resolveTenant(site) {\n        const siteRootPath = (0,_util__WEBPACK_IMPORTED_MODULE_0__.getSiteRootPath)(site);\n        if (!siteRootPath || siteRootPath === '') {\n            return null;\n        }\n        for (const tenant of this.tenants) {\n            if (tenant.sites.some((x) => x.path.toLocaleLowerCase() === siteRootPath.toLocaleLowerCase())) {\n                return tenant;\n            }\n        }\n        return null;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9wYWNrYWdlcy9jb250ZW50LXRva2Vucy9ub2RlX21vZHVsZXMvQHV4YmVlL3V4YmVlLXNpdGVjb3JlLWhlYWRsZXNzLXN4YS1tdWx0aXNpdGUvZGlzdC9lc20vbGliL3RlbmFudC1yZXNvbHZlci5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF5QztBQUNsQztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCLHNEQUFlO0FBQzVDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxXb3JrXFxVWEJFRVxcRm91bmRhdGlvblhNQ2xvdWQyXFxzcmNcXGhlYWRcXHBhY2thZ2VzXFxjb250ZW50LXRva2Vuc1xcbm9kZV9tb2R1bGVzXFxAdXhiZWVcXHV4YmVlLXNpdGVjb3JlLWhlYWRsZXNzLXN4YS1tdWx0aXNpdGVcXGRpc3RcXGVzbVxcbGliXFx0ZW5hbnQtcmVzb2x2ZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZ2V0U2l0ZVJvb3RQYXRoIH0gZnJvbSAnLi91dGlsJztcbmV4cG9ydCBjbGFzcyBUZW5hbnRSZXNvbHZlciB7XG4gICAgY29uc3RydWN0b3IodGVuYW50cykge1xuICAgICAgICB0aGlzLnRlbmFudHMgPSB0ZW5hbnRzO1xuICAgIH1cbiAgICByZXNvbHZlVGVuYW50KHNpdGUpIHtcbiAgICAgICAgY29uc3Qgc2l0ZVJvb3RQYXRoID0gZ2V0U2l0ZVJvb3RQYXRoKHNpdGUpO1xuICAgICAgICBpZiAoIXNpdGVSb290UGF0aCB8fCBzaXRlUm9vdFBhdGggPT09ICcnKSB7XG4gICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgfVxuICAgICAgICBmb3IgKGNvbnN0IHRlbmFudCBvZiB0aGlzLnRlbmFudHMpIHtcbiAgICAgICAgICAgIGlmICh0ZW5hbnQuc2l0ZXMuc29tZSgoeCkgPT4geC5wYXRoLnRvTG9jYWxlTG93ZXJDYXNlKCkgPT09IHNpdGVSb290UGF0aC50b0xvY2FsZUxvd2VyQ2FzZSgpKSkge1xuICAgICAgICAgICAgICAgIHJldHVybiB0ZW5hbnQ7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/tenant-resolver.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/util.js":
/*!********************************************************************************************************************!*\
  !*** ../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/util.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getSiteIdBySiteName: () => (/* binding */ getSiteIdBySiteName),\n/* harmony export */   getSiteParameterValue: () => (/* binding */ getSiteParameterValue),\n/* harmony export */   getSiteRootPath: () => (/* binding */ getSiteRootPath),\n/* harmony export */   getSiteVirtualFolder: () => (/* binding */ getSiteVirtualFolder),\n/* harmony export */   getTenantSiteBySiteRootPath: () => (/* binding */ getTenantSiteBySiteRootPath)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../constants */ \"(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/constants.js\");\n\nfunction getSiteRootPath(site) {\n    return getSiteParameterValue(site, _constants__WEBPACK_IMPORTED_MODULE_0__.SITE_ROOT_PATH_PARAMETER_NAME);\n}\nfunction getSiteVirtualFolder(site) {\n    let virtualFolder = getSiteParameterValue(site, _constants__WEBPACK_IMPORTED_MODULE_0__.SITE_VIRTUAL_FOLDER_PARAMETER_NAME);\n    if (virtualFolder && virtualFolder !== '/' && virtualFolder.endsWith('/')) {\n        virtualFolder = virtualFolder.substring(0, virtualFolder.length - 1);\n    }\n    return virtualFolder;\n}\nfunction getSiteParameterValue(site, attributeName) {\n    var _a;\n    const attributeValue = (_a = site[attributeName]) === null || _a === void 0 ? void 0 : _a.toString();\n    if (!attributeValue) {\n        console.warn(`Site info '${site.name}' doesn't contain parameter '${attributeName}'.`);\n        return null;\n    }\n    return attributeValue;\n}\nfunction getTenantSiteBySiteRootPath(rootPath, tenant) {\n    const tenantSite = tenant.sites.find((site) => site.path.toLocaleLowerCase() === rootPath.toLocaleLowerCase());\n    return tenantSite !== null && tenantSite !== void 0 ? tenantSite : null;\n}\nfunction getSiteIdBySiteName(site, tenants) {\n    var _a;\n    if (!site) {\n        throw new Error('[GetSiteIdBySiteName] No site provided.');\n    }\n    if (!tenants) {\n        throw new Error('[GetSiteIdBySiteName] No tenants provided.');\n    }\n    const siteRootPath = (_a = getSiteRootPath(site)) === null || _a === void 0 ? void 0 : _a.toLowerCase();\n    if (!siteRootPath) {\n        return undefined;\n    }\n    for (const tenant of tenants) {\n        const tenantSite = tenant.sites.find((x) => x.path.toLowerCase() === siteRootPath);\n        if (tenantSite) {\n            return tenantSite.id;\n        }\n    }\n    return undefined;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/util.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/virtual-folder-aware-site-resolver.js":
/*!**************************************************************************************************************************************************!*\
  !*** ../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/virtual-folder-aware-site-resolver.js ***!
  \**************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VirtualFolderAwareSiteResolver: () => (/* binding */ VirtualFolderAwareSiteResolver)\n/* harmony export */ });\n/* harmony import */ var _sitecore_jss_sitecore_jss_site__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @sitecore-jss/sitecore-jss/site */ \"@sitecore-jss/sitecore-jss/site\");\n/* harmony import */ var _sitecore_jss_sitecore_jss_site__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_sitecore_jss_sitecore_jss_site__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util */ \"(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/util.js\");\n\n\nconst DELIMITERS = /\\||,|;/g;\nclass VirtualFolderAwareSiteResolver extends _sitecore_jss_sitecore_jss_site__WEBPACK_IMPORTED_MODULE_0__.SiteResolver {\n    constructor(sites) {\n        super(sites);\n        this.getByHostAndVirtualFolder = (hostName, path) => {\n            const pathNormalized = this.normalizePath(path);\n            const pathSplitted = pathNormalized.split('/');\n            const requestVirtualFolder = pathSplitted.slice(0, 2).join('/').toLowerCase();\n            const sitesMatchingHostAndVirtualPath = this.sites\n                .filter((site) => {\n                const siteVirtualFolder = (0,_util__WEBPACK_IMPORTED_MODULE_1__.getSiteVirtualFolder)(site);\n                return (siteVirtualFolder &&\n                    siteVirtualFolder !== '/' &&\n                    this.matchHost(hostName, site) &&\n                    requestVirtualFolder === siteVirtualFolder);\n            })\n                // sort sites so site with the most specific virtual path goes up\n                .sort((s1, s2) => this.getSiteVirtualPathSegments(s2).length - this.getSiteVirtualPathSegments(s1).length);\n            if (sitesMatchingHostAndVirtualPath.length > 0) {\n                return sitesMatchingHostAndVirtualPath[0];\n            }\n            const siteResolver = new _sitecore_jss_sitecore_jss_site__WEBPACK_IMPORTED_MODULE_0__.SiteResolver(this.getRootWebsites(this.sites));\n            return siteResolver.getByHost(hostName);\n        };\n        this.getRootWebsites = (sites) => {\n            return sites.filter((site) => (0,_util__WEBPACK_IMPORTED_MODULE_1__.getSiteVirtualFolder)(site) === '/');\n        };\n        this.getSiteVirtualPathSegments = (site) => {\n            var _a, _b;\n            return ((_b = (_a = (0,_util__WEBPACK_IMPORTED_MODULE_1__.getSiteVirtualFolder)(site)) === null || _a === void 0 ? void 0 : _a.split('/').filter((x) => x !== '')) !== null && _b !== void 0 ? _b : []);\n        };\n        this.matchHost = (hostName, site) => {\n            const hostNames = site.hostName.replace(/\\s/g, '').toLocaleLowerCase().split(DELIMITERS);\n            for (const host of hostNames) {\n                if (super.matchesPattern(hostName, host)) {\n                    return true;\n                }\n            }\n            return false;\n        };\n        this.normalizePath = (path) => {\n            let pathNormalized = path;\n            pathNormalized = pathNormalized.replaceAll('\\\\', '/');\n            if (!pathNormalized.startsWith('/')) {\n                pathNormalized = `/${pathNormalized}`;\n            }\n            return pathNormalized;\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/virtual-folder-aware-site-resolver.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/default-locale-url-rewrite-middleware.js":
/*!************************************************************************************************************************************************************!*\
  !*** ../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/default-locale-url-rewrite-middleware.js ***!
  \************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DefaultLocaleUrlRewriteMiddleware: () => (/* binding */ DefaultLocaleUrlRewriteMiddleware)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(pages-dir-node)/../../node_modules/next/server.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_server__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _middleware_util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./middleware-util */ \"(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/middleware-util.js\");\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n\n\nconst FALLBACK_HOST_NAME = 'localhost';\nconst PUBLIC_FILE_PATTERN = /\\.(.*)$/;\nclass DefaultLocaleUrlRewriteMiddleware {\n    constructor(config) {\n        this.config = config;\n        this.handler = (req, res) => __awaiter(this, void 0, void 0, function* () {\n            const hostName = (0,_middleware_util__WEBPACK_IMPORTED_MODULE_1__.getHostName)(req, this.config.defaultHostName) || FALLBACK_HOST_NAME;\n            const pathName = req.nextUrl.pathname;\n            const response = res || next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n            if (this.config.skipForHostName && this.config.skipForHostName(hostName)) {\n                return response;\n            }\n            const site = this.config.siteResolver.getByHostAndVirtualFolder(hostName, pathName);\n            if (!site) {\n                return response;\n            }\n            if (site.language === req.nextUrl.locale) {\n                if (req.nextUrl.pathname.startsWith('/_next') ||\n                    req.nextUrl.pathname.includes('/api/') ||\n                    PUBLIC_FILE_PATTERN.test(req.nextUrl.pathname) ||\n                    req.url.includes('path=')) {\n                    return response;\n                }\n                const reqUrl = new URL(req.url);\n                const reqUrlPath = reqUrl.pathname;\n                if (reqUrlPath.endsWith(`/${site.language}`) || reqUrlPath.startsWith(`/${site.language}/`)) {\n                    const urlStringWithoutDefaultLocale = req.url.replace(reqUrlPath, reqUrlPath.slice(3));\n                    const urlWithoutDefaultLocale = new URL(urlStringWithoutDefaultLocale);\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(urlWithoutDefaultLocale, { status: 301 });\n                }\n            }\n            return response;\n        });\n    }\n    getHandler() {\n        return this.handler;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9wYWNrYWdlcy9jb250ZW50LXRva2Vucy9ub2RlX21vZHVsZXMvQHV4YmVlL3V4YmVlLXNpdGVjb3JlLWhlYWRsZXNzLXN4YS1tdWx0aXNpdGUvZGlzdC9lc20vbWlkZGxld2FyZS9kZWZhdWx0LWxvY2FsZS11cmwtcmV3cml0ZS1taWRkbGV3YXJlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQSxpQkFBaUIsU0FBSSxJQUFJLFNBQUk7QUFDN0IsNEJBQTRCLCtEQUErRCxpQkFBaUI7QUFDNUc7QUFDQSxvQ0FBb0MsTUFBTSwrQkFBK0IsWUFBWTtBQUNyRixtQ0FBbUMsTUFBTSxtQ0FBbUMsWUFBWTtBQUN4RixnQ0FBZ0M7QUFDaEM7QUFDQSxLQUFLO0FBQ0w7QUFDMkM7QUFDSztBQUNoRDtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQSw2QkFBNkIsNkRBQVc7QUFDeEM7QUFDQSxvQ0FBb0MscURBQVk7QUFDaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0Q0FBNEMsY0FBYyxnQ0FBZ0MsY0FBYztBQUN4RztBQUNBO0FBQ0EsMkJBQTJCLHFEQUFZLHFDQUFxQyxhQUFhO0FBQ3pGO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcV29ya1xcVVhCRUVcXEZvdW5kYXRpb25YTUNsb3VkMlxcc3JjXFxoZWFkXFxwYWNrYWdlc1xcY29udGVudC10b2tlbnNcXG5vZGVfbW9kdWxlc1xcQHV4YmVlXFx1eGJlZS1zaXRlY29yZS1oZWFkbGVzcy1zeGEtbXVsdGlzaXRlXFxkaXN0XFxlc21cXG1pZGRsZXdhcmVcXGRlZmF1bHQtbG9jYWxlLXVybC1yZXdyaXRlLW1pZGRsZXdhcmUuanMiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIF9fYXdhaXRlciA9ICh0aGlzICYmIHRoaXMuX19hd2FpdGVyKSB8fCBmdW5jdGlvbiAodGhpc0FyZywgX2FyZ3VtZW50cywgUCwgZ2VuZXJhdG9yKSB7XG4gICAgZnVuY3Rpb24gYWRvcHQodmFsdWUpIHsgcmV0dXJuIHZhbHVlIGluc3RhbmNlb2YgUCA/IHZhbHVlIDogbmV3IFAoZnVuY3Rpb24gKHJlc29sdmUpIHsgcmVzb2x2ZSh2YWx1ZSk7IH0pOyB9XG4gICAgcmV0dXJuIG5ldyAoUCB8fCAoUCA9IFByb21pc2UpKShmdW5jdGlvbiAocmVzb2x2ZSwgcmVqZWN0KSB7XG4gICAgICAgIGZ1bmN0aW9uIGZ1bGZpbGxlZCh2YWx1ZSkgeyB0cnkgeyBzdGVwKGdlbmVyYXRvci5uZXh0KHZhbHVlKSk7IH0gY2F0Y2ggKGUpIHsgcmVqZWN0KGUpOyB9IH1cbiAgICAgICAgZnVuY3Rpb24gcmVqZWN0ZWQodmFsdWUpIHsgdHJ5IHsgc3RlcChnZW5lcmF0b3JbXCJ0aHJvd1wiXSh2YWx1ZSkpOyB9IGNhdGNoIChlKSB7IHJlamVjdChlKTsgfSB9XG4gICAgICAgIGZ1bmN0aW9uIHN0ZXAocmVzdWx0KSB7IHJlc3VsdC5kb25lID8gcmVzb2x2ZShyZXN1bHQudmFsdWUpIDogYWRvcHQocmVzdWx0LnZhbHVlKS50aGVuKGZ1bGZpbGxlZCwgcmVqZWN0ZWQpOyB9XG4gICAgICAgIHN0ZXAoKGdlbmVyYXRvciA9IGdlbmVyYXRvci5hcHBseSh0aGlzQXJnLCBfYXJndW1lbnRzIHx8IFtdKSkubmV4dCgpKTtcbiAgICB9KTtcbn07XG5pbXBvcnQgeyBOZXh0UmVzcG9uc2UgfSBmcm9tICduZXh0L3NlcnZlcic7XG5pbXBvcnQgeyBnZXRIb3N0TmFtZSB9IGZyb20gJy4vbWlkZGxld2FyZS11dGlsJztcbmNvbnN0IEZBTExCQUNLX0hPU1RfTkFNRSA9ICdsb2NhbGhvc3QnO1xuY29uc3QgUFVCTElDX0ZJTEVfUEFUVEVSTiA9IC9cXC4oLiopJC87XG5leHBvcnQgY2xhc3MgRGVmYXVsdExvY2FsZVVybFJld3JpdGVNaWRkbGV3YXJlIHtcbiAgICBjb25zdHJ1Y3Rvcihjb25maWcpIHtcbiAgICAgICAgdGhpcy5jb25maWcgPSBjb25maWc7XG4gICAgICAgIHRoaXMuaGFuZGxlciA9IChyZXEsIHJlcykgPT4gX19hd2FpdGVyKHRoaXMsIHZvaWQgMCwgdm9pZCAwLCBmdW5jdGlvbiogKCkge1xuICAgICAgICAgICAgY29uc3QgaG9zdE5hbWUgPSBnZXRIb3N0TmFtZShyZXEsIHRoaXMuY29uZmlnLmRlZmF1bHRIb3N0TmFtZSkgfHwgRkFMTEJBQ0tfSE9TVF9OQU1FO1xuICAgICAgICAgICAgY29uc3QgcGF0aE5hbWUgPSByZXEubmV4dFVybC5wYXRobmFtZTtcbiAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gcmVzIHx8IE5leHRSZXNwb25zZS5uZXh0KCk7XG4gICAgICAgICAgICBpZiAodGhpcy5jb25maWcuc2tpcEZvckhvc3ROYW1lICYmIHRoaXMuY29uZmlnLnNraXBGb3JIb3N0TmFtZShob3N0TmFtZSkpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gcmVzcG9uc2U7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjb25zdCBzaXRlID0gdGhpcy5jb25maWcuc2l0ZVJlc29sdmVyLmdldEJ5SG9zdEFuZFZpcnR1YWxGb2xkZXIoaG9zdE5hbWUsIHBhdGhOYW1lKTtcbiAgICAgICAgICAgIGlmICghc2l0ZSkge1xuICAgICAgICAgICAgICAgIHJldHVybiByZXNwb25zZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChzaXRlLmxhbmd1YWdlID09PSByZXEubmV4dFVybC5sb2NhbGUpIHtcbiAgICAgICAgICAgICAgICBpZiAocmVxLm5leHRVcmwucGF0aG5hbWUuc3RhcnRzV2l0aCgnL19uZXh0JykgfHxcbiAgICAgICAgICAgICAgICAgICAgcmVxLm5leHRVcmwucGF0aG5hbWUuaW5jbHVkZXMoJy9hcGkvJykgfHxcbiAgICAgICAgICAgICAgICAgICAgUFVCTElDX0ZJTEVfUEFUVEVSTi50ZXN0KHJlcS5uZXh0VXJsLnBhdGhuYW1lKSB8fFxuICAgICAgICAgICAgICAgICAgICByZXEudXJsLmluY2x1ZGVzKCdwYXRoPScpKSB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiByZXNwb25zZTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY29uc3QgcmVxVXJsID0gbmV3IFVSTChyZXEudXJsKTtcbiAgICAgICAgICAgICAgICBjb25zdCByZXFVcmxQYXRoID0gcmVxVXJsLnBhdGhuYW1lO1xuICAgICAgICAgICAgICAgIGlmIChyZXFVcmxQYXRoLmVuZHNXaXRoKGAvJHtzaXRlLmxhbmd1YWdlfWApIHx8IHJlcVVybFBhdGguc3RhcnRzV2l0aChgLyR7c2l0ZS5sYW5ndWFnZX0vYCkpIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgdXJsU3RyaW5nV2l0aG91dERlZmF1bHRMb2NhbGUgPSByZXEudXJsLnJlcGxhY2UocmVxVXJsUGF0aCwgcmVxVXJsUGF0aC5zbGljZSgzKSk7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHVybFdpdGhvdXREZWZhdWx0TG9jYWxlID0gbmV3IFVSTCh1cmxTdHJpbmdXaXRob3V0RGVmYXVsdExvY2FsZSk7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UucmVkaXJlY3QodXJsV2l0aG91dERlZmF1bHRMb2NhbGUsIHsgc3RhdHVzOiAzMDEgfSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIHJlc3BvbnNlO1xuICAgICAgICB9KTtcbiAgICB9XG4gICAgZ2V0SGFuZGxlcigpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuaGFuZGxlcjtcbiAgICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/default-locale-url-rewrite-middleware.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/handle-default-locale-middleware.js":
/*!*******************************************************************************************************************************************************!*\
  !*** ../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/handle-default-locale-middleware.js ***!
  \*******************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HandleDefaultLocaleMiddleware: () => (/* binding */ HandleDefaultLocaleMiddleware)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(pages-dir-node)/../../node_modules/next/server.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_server__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _middleware_util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./middleware-util */ \"(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/middleware-util.js\");\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n\n\nconst FALLBACK_HOST_NAME = 'localhost';\nconst PUBLIC_FILE_PATTERN = /\\.(.*)$/;\nclass HandleDefaultLocaleMiddleware {\n    constructor(config) {\n        this.config = config;\n        this.handler = (req, res) => __awaiter(this, void 0, void 0, function* () {\n            const hostName = (0,_middleware_util__WEBPACK_IMPORTED_MODULE_1__.getHostName)(req, this.config.defaultHostName) || FALLBACK_HOST_NAME;\n            const pathName = req.nextUrl.pathname;\n            const response = res || next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n            if (this.config.skipForHostName && this.config.skipForHostName(hostName)) {\n                return response;\n            }\n            if (req.nextUrl.locale === this.config.defaultLocale) {\n                if (req.nextUrl.pathname.startsWith('/_next') ||\n                    req.nextUrl.pathname.includes('/api/') ||\n                    PUBLIC_FILE_PATTERN.test(req.nextUrl.pathname)) {\n                    return response;\n                }\n                const site = this.config.siteResolver.getByHostAndVirtualFolder(hostName, pathName);\n                if (!site) {\n                    return response;\n                }\n                req.nextUrl.locale = site.language;\n            }\n            return response;\n        });\n    }\n    getHandler() {\n        return this.handler;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/handle-default-locale-middleware.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/middleware-util.js":
/*!**************************************************************************************************************************************!*\
  !*** ../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/middleware-util.js ***!
  \**************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getHostName: () => (/* binding */ getHostName)\n/* harmony export */ });\nfunction getHostName(req, defaultHostName) {\n    var _a;\n    return ((_a = req.headers.get('host')) === null || _a === void 0 ? void 0 : _a.split(':')[0]) || defaultHostName;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9wYWNrYWdlcy9jb250ZW50LXRva2Vucy9ub2RlX21vZHVsZXMvQHV4YmVlL3V4YmVlLXNpdGVjb3JlLWhlYWRsZXNzLXN4YS1tdWx0aXNpdGUvZGlzdC9lc20vbWlkZGxld2FyZS9taWRkbGV3YXJlLXV0aWwuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcV29ya1xcVVhCRUVcXEZvdW5kYXRpb25YTUNsb3VkMlxcc3JjXFxoZWFkXFxwYWNrYWdlc1xcY29udGVudC10b2tlbnNcXG5vZGVfbW9kdWxlc1xcQHV4YmVlXFx1eGJlZS1zaXRlY29yZS1oZWFkbGVzcy1zeGEtbXVsdGlzaXRlXFxkaXN0XFxlc21cXG1pZGRsZXdhcmVcXG1pZGRsZXdhcmUtdXRpbC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gZ2V0SG9zdE5hbWUocmVxLCBkZWZhdWx0SG9zdE5hbWUpIHtcbiAgICB2YXIgX2E7XG4gICAgcmV0dXJuICgoX2EgPSByZXEuaGVhZGVycy5nZXQoJ2hvc3QnKSkgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLnNwbGl0KCc6JylbMF0pIHx8IGRlZmF1bHRIb3N0TmFtZTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/middleware-util.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/site-virtual-folder-middleware.js":
/*!*****************************************************************************************************************************************************!*\
  !*** ../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/site-virtual-folder-middleware.js ***!
  \*****************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SiteVirtualFolderMiddleware: () => (/* binding */ SiteVirtualFolderMiddleware)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(pages-dir-node)/../../node_modules/next/server.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_server__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _middleware_util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./middleware-util */ \"(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/middleware-util.js\");\n/* harmony import */ var _sitecore_jss_sitecore_jss_site__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @sitecore-jss/sitecore-jss/site */ \"@sitecore-jss/sitecore-jss/site\");\n/* harmony import */ var _sitecore_jss_sitecore_jss_site__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_sitecore_jss_sitecore_jss_site__WEBPACK_IMPORTED_MODULE_2__);\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n\n\n\nconst FALLBACK_HOST_NAME = 'localhost';\nconst SITE_PARAMETER_NAME = 'sc_site';\nconst REWRITE_HTTP_HEADER_PARAMETER_NAME = 'x-sc-rewrite';\nclass SiteVirtualFolderMiddleware {\n    constructor(config) {\n        this.config = config;\n        this.handler = (req, res) => __awaiter(this, void 0, void 0, function* () {\n            const hostName = (0,_middleware_util__WEBPACK_IMPORTED_MODULE_1__.getHostName)(req, this.config.defaultHostName) || FALLBACK_HOST_NAME;\n            const pathName = req.nextUrl.pathname;\n            let response = res || next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n            const initialSiteRewriteData = response.headers.get(REWRITE_HTTP_HEADER_PARAMETER_NAME);\n            if (!initialSiteRewriteData || initialSiteRewriteData === '') {\n                return response;\n            }\n            const site = this.config.siteResolver.getByHostAndVirtualFolder(hostName, pathName);\n            if (!site) {\n                return response;\n            }\n            const rewritePath = (0,_sitecore_jss_sitecore_jss_site__WEBPACK_IMPORTED_MODULE_2__.getSiteRewrite)(pathName, { siteName: site.name });\n            const rewriteUrl = req.nextUrl.clone();\n            rewriteUrl.pathname = rewritePath;\n            response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.rewrite(rewriteUrl);\n            response.cookies.set(SITE_PARAMETER_NAME, site.name, {\n                secure: true,\n                httpOnly: true,\n                sameSite: 'none',\n            });\n            response.headers.set(REWRITE_HTTP_HEADER_PARAMETER_NAME, rewritePath);\n            return response;\n        });\n    }\n    getHandler() {\n        return this.handler;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/site-virtual-folder-middleware.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/site-resolving-plugins/remove-default-site-duplicate-plugin-core.js":
/*!****************************************************************************************************************************************************************************!*\
  !*** ../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/site-resolving-plugins/remove-default-site-duplicate-plugin-core.js ***!
  \****************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RemoveDefaultSiteDuplicatePluginCore: () => (/* binding */ RemoveDefaultSiteDuplicatePluginCore)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../constants */ \"(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/constants.js\");\n\nclass RemoveDefaultSiteDuplicatePluginCore {\n    constructor(config) {\n        this.config = config;\n    }\n    removeDefaultSite(sites) {\n        if (!this.config.defaultSiteName || this.config.defaultSiteName === '') {\n            console.warn('Default site name is not specified.', this);\n            return sites;\n        }\n        const sitesSerialized = JSON.parse(this.config.sitesString || '[]');\n        if (!sitesSerialized || sitesSerialized.length === 0) {\n            return sites;\n        }\n        for (let i = 0; i < sites.length; i++) {\n            // Find a site with at least one of extended attribute absent, and that has a match in sites present in Sitecore with the same attribute present.\n            // This site should be removed to make sure it is not duplicated in site resolver.\n            if (_constants__WEBPACK_IMPORTED_MODULE_0__.ExtendedSiteAttributes.find((attribute) => !sites[i][attribute] && sitesSerialized.find((site) => site[attribute]))) {\n                sites.splice(i, 1);\n                break;\n            }\n        }\n        return sites;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/content-tokens/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/site-resolving-plugins/remove-default-site-duplicate-plugin-core.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/config-plugins/default-site-root-path-plugin-core.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/config-plugins/default-site-root-path-plugin-core.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DefaultSiteRootPathPluginCore: () => (/* binding */ DefaultSiteRootPathPluginCore)\n/* harmony export */ });\n/* harmony import */ var _lib_util__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/util */ \"(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/util.js\");\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n\nclass DefaultSiteRootPathPluginCore {\n    constructor(config) {\n        this.config = config;\n    }\n    initializeDefaultSiteRootPath() {\n        return __awaiter(this, void 0, void 0, function* () {\n            if (!this.config.sitecoreSiteName || this.config.sitecoreSiteName === '') {\n                console.warn('\"sitecoreSiteName\" property is not defined. Skipping default site root path initialization.');\n                return null;\n            }\n            const defaultSiteName = this.config.sitecoreSiteName;\n            const siteInfoCollection = JSON.parse(this.config.sitesString || '[]');\n            let defaultSiteRootPath = null;\n            if (siteInfoCollection && siteInfoCollection.length > 0) {\n                const defaultSiteInfoMatch = siteInfoCollection.find((siteInfo) => siteInfo.name.toLocaleLowerCase() === (defaultSiteName === null || defaultSiteName === void 0 ? void 0 : defaultSiteName.toLocaleLowerCase()));\n                if (defaultSiteInfoMatch) {\n                    defaultSiteRootPath = (0,_lib_util__WEBPACK_IMPORTED_MODULE_0__.getSiteRootPath)(defaultSiteInfoMatch);\n                }\n            }\n            return defaultSiteRootPath;\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/config-plugins/default-site-root-path-plugin-core.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/config-plugins/site-attribute-plugin-core.js":
/*!***********************************************************************************************************************************************************!*\
  !*** ../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/config-plugins/site-attribute-plugin-core.js ***!
  \***********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SiteAttributePluginCore: () => (/* binding */ SiteAttributePluginCore)\n/* harmony export */ });\n/* harmony import */ var _sitecore_jss_sitecore_jss_site__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @sitecore-jss/sitecore-jss/site */ \"@sitecore-jss/sitecore-jss/site\");\n/* harmony import */ var _sitecore_jss_sitecore_jss_site__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_sitecore_jss_sitecore_jss_site__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_graphql_site_attribute_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/graphql-site-attribute-service */ \"(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/graphql-site-attribute-service.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../constants */ \"(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/constants.js\");\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n\n\n\nclass SiteAttributePluginCore {\n    constructor(config) {\n        this.config = config;\n    }\n    initializeSiteAttributes() {\n        return __awaiter(this, void 0, void 0, function* () {\n            const result = [];\n            const clientFactory = this.config.clientFactory;\n            try {\n                let configSites = this.parseSitesFromSitesString();\n                if (!configSites) {\n                    // No Sitecore multisite addon installed. Fetch site info by our own.\n                    configSites = yield this.fetchSiteInfo(clientFactory);\n                }\n                console.log('Fetching site attributes');\n                const siteAttributeService = new _lib_graphql_site_attribute_service__WEBPACK_IMPORTED_MODULE_1__.GraphQLSiteAttributeService({\n                    clientFactory,\n                    fetch: fetch,\n                });\n                const siteAttributes = yield siteAttributeService.fetchSiteAttributes();\n                configSites.forEach((siteInfo) => {\n                    const site = siteAttributes.find((x) => x.name.toLocaleLowerCase() === siteInfo.name.toLocaleLowerCase());\n                    const extendedSiteProperties = {};\n                    if (site) {\n                        _constants__WEBPACK_IMPORTED_MODULE_2__.ExtendedSiteAttributes.forEach((attributeName) => {\n                            const siteAttribute = site.attributes.find((attr) => attr.key === attributeName);\n                            if (siteAttribute) {\n                                extendedSiteProperties[attributeName] = siteAttribute.value;\n                            }\n                        });\n                        // Sitecore Experience Edge doesn't return 'rootPath' attribute in the site attributes object.\n                        // Set it explicitly.\n                        extendedSiteProperties[_constants__WEBPACK_IMPORTED_MODULE_2__.SITE_ROOT_PATH_PARAMETER_NAME] = site.rootPath;\n                    }\n                    result.push(Object.assign({}, siteInfo, extendedSiteProperties));\n                });\n            }\n            catch (error) {\n                console.error('Error at fetching site root paths');\n                console.error(error);\n                return null;\n            }\n            return result;\n        });\n    }\n    parseSitesFromSitesString() {\n        var _a;\n        if (!this.config.sitesString || this.config.sitesString === '') {\n            return null;\n        }\n        return (_a = JSON.parse(this.config.sitesString)) !== null && _a !== void 0 ? _a : [];\n    }\n    fetchSiteInfo(clientFactory) {\n        return __awaiter(this, void 0, void 0, function* () {\n            const siteInfoService = new _sitecore_jss_sitecore_jss_site__WEBPACK_IMPORTED_MODULE_0__.GraphQLSiteInfoService({ clientFactory });\n            return yield siteInfoService.fetchSiteInfo();\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/config-plugins/site-attribute-plugin-core.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/config-plugins/tenant-plugin-core.js":
/*!***************************************************************************************************************************************************!*\
  !*** ../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/config-plugins/tenant-plugin-core.js ***!
  \***************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TenantPluginCore: () => (/* binding */ TenantPluginCore)\n/* harmony export */ });\n/* harmony import */ var _lib_graphql_tenant_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/graphql-tenant-service */ \"(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/graphql-tenant-service.js\");\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n\nclass TenantPluginCore {\n    constructor(config) {\n        this.config = config;\n    }\n    getTenants() {\n        return __awaiter(this, void 0, void 0, function* () {\n            const clientFactory = this.config.clientFactory;\n            const fetch = this.config.fetch;\n            try {\n                const tenantService = new _lib_graphql_tenant_service__WEBPACK_IMPORTED_MODULE_0__.GraphQLTenantService({\n                    clientFactory,\n                    fetch,\n                });\n                const tenantResult = yield tenantService.fetchTenantData();\n                return tenantResult !== null && tenantResult !== void 0 ? tenantResult : [];\n            }\n            catch (error) {\n                console.error('Error at fetching tenants');\n                console.error(error);\n                return null;\n            }\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/config-plugins/tenant-plugin-core.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/constants.js":
/*!***************************************************************************************************************************!*\
  !*** ../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/constants.js ***!
  \***************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ExtendedSiteAttributes: () => (/* binding */ ExtendedSiteAttributes),\n/* harmony export */   SITE_ROOT_PATH_PARAMETER_NAME: () => (/* binding */ SITE_ROOT_PATH_PARAMETER_NAME),\n/* harmony export */   SITE_VIRTUAL_FOLDER_PARAMETER_NAME: () => (/* binding */ SITE_VIRTUAL_FOLDER_PARAMETER_NAME)\n/* harmony export */ });\nconst ExtendedSiteAttributes = ['virtualFolder'];\n// Parameters\nconst SITE_ROOT_PATH_PARAMETER_NAME = 'rootPath';\nconst SITE_VIRTUAL_FOLDER_PARAMETER_NAME = 'virtualFolder';\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9wYWNrYWdlcy9yYWktZXhoaWJpdG9yLXBvcnRhbC9ub2RlX21vZHVsZXMvQHV4YmVlL3V4YmVlLXNpdGVjb3JlLWhlYWRsZXNzLXN4YS1tdWx0aXNpdGUvZGlzdC9lc20vY29uc3RhbnRzLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFPO0FBQ1A7QUFDTztBQUNBIiwic291cmNlcyI6WyJEOlxcV29ya1xcVVhCRUVcXEZvdW5kYXRpb25YTUNsb3VkMlxcc3JjXFxoZWFkXFxwYWNrYWdlc1xccmFpLWV4aGliaXRvci1wb3J0YWxcXG5vZGVfbW9kdWxlc1xcQHV4YmVlXFx1eGJlZS1zaXRlY29yZS1oZWFkbGVzcy1zeGEtbXVsdGlzaXRlXFxkaXN0XFxlc21cXGNvbnN0YW50cy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgRXh0ZW5kZWRTaXRlQXR0cmlidXRlcyA9IFsndmlydHVhbEZvbGRlciddO1xuLy8gUGFyYW1ldGVyc1xuZXhwb3J0IGNvbnN0IFNJVEVfUk9PVF9QQVRIX1BBUkFNRVRFUl9OQU1FID0gJ3Jvb3RQYXRoJztcbmV4cG9ydCBjb25zdCBTSVRFX1ZJUlRVQUxfRk9MREVSX1BBUkFNRVRFUl9OQU1FID0gJ3ZpcnR1YWxGb2xkZXInO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/constants.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/index.js":
/*!***********************************************************************************************************************!*\
  !*** ../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/index.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _lib_graphql_site_attribute_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/graphql-site-attribute-service */ \"(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/graphql-site-attribute-service.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _lib_graphql_site_attribute_service__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _lib_graphql_site_attribute_service__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _lib_graphql_tenant_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/graphql-tenant-service */ \"(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/graphql-tenant-service.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _lib_graphql_tenant_service__WEBPACK_IMPORTED_MODULE_1__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _lib_graphql_tenant_service__WEBPACK_IMPORTED_MODULE_1__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _config_plugins_site_attribute_plugin_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./config-plugins/site-attribute-plugin-core */ \"(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/config-plugins/site-attribute-plugin-core.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _config_plugins_site_attribute_plugin_core__WEBPACK_IMPORTED_MODULE_2__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _config_plugins_site_attribute_plugin_core__WEBPACK_IMPORTED_MODULE_2__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _config_plugins_default_site_root_path_plugin_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./config-plugins/default-site-root-path-plugin-core */ \"(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/config-plugins/default-site-root-path-plugin-core.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _config_plugins_default_site_root_path_plugin_core__WEBPACK_IMPORTED_MODULE_3__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _config_plugins_default_site_root_path_plugin_core__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _config_plugins_tenant_plugin_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./config-plugins/tenant-plugin-core */ \"(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/config-plugins/tenant-plugin-core.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _config_plugins_tenant_plugin_core__WEBPACK_IMPORTED_MODULE_4__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _config_plugins_tenant_plugin_core__WEBPACK_IMPORTED_MODULE_4__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _site_resolving_plugins_remove_default_site_duplicate_plugin_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./site-resolving-plugins/remove-default-site-duplicate-plugin-core */ \"(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/site-resolving-plugins/remove-default-site-duplicate-plugin-core.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _site_resolving_plugins_remove_default_site_duplicate_plugin_core__WEBPACK_IMPORTED_MODULE_5__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _site_resolving_plugins_remove_default_site_duplicate_plugin_core__WEBPACK_IMPORTED_MODULE_5__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _lib_virtual_folder_aware_site_resolver__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./lib/virtual-folder-aware-site-resolver */ \"(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/virtual-folder-aware-site-resolver.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _lib_virtual_folder_aware_site_resolver__WEBPACK_IMPORTED_MODULE_6__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _lib_virtual_folder_aware_site_resolver__WEBPACK_IMPORTED_MODULE_6__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _lib_tenant_resolver__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./lib/tenant-resolver */ \"(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/tenant-resolver.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _lib_tenant_resolver__WEBPACK_IMPORTED_MODULE_7__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _lib_tenant_resolver__WEBPACK_IMPORTED_MODULE_7__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _middleware_site_virtual_folder_middleware__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./middleware/site-virtual-folder-middleware */ \"(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/site-virtual-folder-middleware.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _middleware_site_virtual_folder_middleware__WEBPACK_IMPORTED_MODULE_8__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _middleware_site_virtual_folder_middleware__WEBPACK_IMPORTED_MODULE_8__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _middleware_handle_default_locale_middleware__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./middleware/handle-default-locale-middleware */ \"(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/handle-default-locale-middleware.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _middleware_handle_default_locale_middleware__WEBPACK_IMPORTED_MODULE_9__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _middleware_handle_default_locale_middleware__WEBPACK_IMPORTED_MODULE_9__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _middleware_default_locale_url_rewrite_middleware__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./middleware/default-locale-url-rewrite-middleware */ \"(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/default-locale-url-rewrite-middleware.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _middleware_default_locale_url_rewrite_middleware__WEBPACK_IMPORTED_MODULE_10__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _middleware_default_locale_url_rewrite_middleware__WEBPACK_IMPORTED_MODULE_10__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./constants */ \"(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/constants.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _constants__WEBPACK_IMPORTED_MODULE_11__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _constants__WEBPACK_IMPORTED_MODULE_11__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _lib_util__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./lib/util */ \"(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/util.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _lib_util__WEBPACK_IMPORTED_MODULE_12__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _lib_util__WEBPACK_IMPORTED_MODULE_12__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n// services\n\n\n// configuration plugins\n\n\n\n\n// site resolver\n\n// tenant resolver\n\n// middleware\n\n\n\n// constants\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9wYWNrYWdlcy9yYWktZXhoaWJpdG9yLXBvcnRhbC9ub2RlX21vZHVsZXMvQHV4YmVlL3V4YmVlLXNpdGVjb3JlLWhlYWRsZXNzLXN4YS1tdWx0aXNpdGUvZGlzdC9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQTtBQUNxRDtBQUNSO0FBQzdDO0FBQzREO0FBQ1E7QUFDaEI7QUFDK0I7QUFDbkY7QUFDeUQ7QUFDekQ7QUFDc0M7QUFDdEM7QUFDNEQ7QUFDRTtBQUNLO0FBQ25FO0FBQzRCO0FBQ0QiLCJzb3VyY2VzIjpbIkQ6XFxXb3JrXFxVWEJFRVxcRm91bmRhdGlvblhNQ2xvdWQyXFxzcmNcXGhlYWRcXHBhY2thZ2VzXFxyYWktZXhoaWJpdG9yLXBvcnRhbFxcbm9kZV9tb2R1bGVzXFxAdXhiZWVcXHV4YmVlLXNpdGVjb3JlLWhlYWRsZXNzLXN4YS1tdWx0aXNpdGVcXGRpc3RcXGVzbVxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc2VydmljZXNcbmV4cG9ydCAqIGZyb20gJy4vbGliL2dyYXBocWwtc2l0ZS1hdHRyaWJ1dGUtc2VydmljZSc7XG5leHBvcnQgKiBmcm9tICcuL2xpYi9ncmFwaHFsLXRlbmFudC1zZXJ2aWNlJztcbi8vIGNvbmZpZ3VyYXRpb24gcGx1Z2luc1xuZXhwb3J0ICogZnJvbSAnLi9jb25maWctcGx1Z2lucy9zaXRlLWF0dHJpYnV0ZS1wbHVnaW4tY29yZSc7XG5leHBvcnQgKiBmcm9tICcuL2NvbmZpZy1wbHVnaW5zL2RlZmF1bHQtc2l0ZS1yb290LXBhdGgtcGx1Z2luLWNvcmUnO1xuZXhwb3J0ICogZnJvbSAnLi9jb25maWctcGx1Z2lucy90ZW5hbnQtcGx1Z2luLWNvcmUnO1xuZXhwb3J0ICogZnJvbSAnLi9zaXRlLXJlc29sdmluZy1wbHVnaW5zL3JlbW92ZS1kZWZhdWx0LXNpdGUtZHVwbGljYXRlLXBsdWdpbi1jb3JlJztcbi8vIHNpdGUgcmVzb2x2ZXJcbmV4cG9ydCAqIGZyb20gJy4vbGliL3ZpcnR1YWwtZm9sZGVyLWF3YXJlLXNpdGUtcmVzb2x2ZXInO1xuLy8gdGVuYW50IHJlc29sdmVyXG5leHBvcnQgKiBmcm9tICcuL2xpYi90ZW5hbnQtcmVzb2x2ZXInO1xuLy8gbWlkZGxld2FyZVxuZXhwb3J0ICogZnJvbSAnLi9taWRkbGV3YXJlL3NpdGUtdmlydHVhbC1mb2xkZXItbWlkZGxld2FyZSc7XG5leHBvcnQgKiBmcm9tICcuL21pZGRsZXdhcmUvaGFuZGxlLWRlZmF1bHQtbG9jYWxlLW1pZGRsZXdhcmUnO1xuZXhwb3J0ICogZnJvbSAnLi9taWRkbGV3YXJlL2RlZmF1bHQtbG9jYWxlLXVybC1yZXdyaXRlLW1pZGRsZXdhcmUnO1xuLy8gY29uc3RhbnRzXG5leHBvcnQgKiBmcm9tICcuL2NvbnN0YW50cyc7XG5leHBvcnQgKiBmcm9tICcuL2xpYi91dGlsJztcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/index.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/graphql-site-attribute-service.js":
/*!****************************************************************************************************************************************************!*\
  !*** ../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/graphql-site-attribute-service.js ***!
  \****************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GraphQLSiteAttributeService: () => (/* binding */ GraphQLSiteAttributeService)\n/* harmony export */ });\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nconst query = `\r\n  {\r\n    site {\r\n      siteInfoCollection {\r\n        name,\r\n        rootPath,\r\n        attributes {\r\n          key,\r\n          value\r\n        }\r\n      }\r\n    }\r\n  }\r\n`;\nclass GraphQLSiteAttributeService {\n    get query() {\n        return query;\n    }\n    constructor(options) {\n        this.graphQlClient = options.clientFactory({\n            fetch: options.fetch,\n        });\n    }\n    fetchSiteAttributes() {\n        return __awaiter(this, void 0, void 0, function* () {\n            var _a;\n            const queryResult = yield this.graphQlClient.request(this.query);\n            const siteData = (_a = queryResult === null || queryResult === void 0 ? void 0 : queryResult.site) === null || _a === void 0 ? void 0 : _a.siteInfoCollection;\n            if (!siteData) {\n                throw new Error('No site data from site collection.');\n            }\n            const result = [];\n            siteData.forEach((site) => result.push(site));\n            return result;\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/graphql-site-attribute-service.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/graphql-tenant-service.js":
/*!********************************************************************************************************************************************!*\
  !*** ../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/graphql-tenant-service.js ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GraphQLTenantService: () => (/* binding */ GraphQLTenantService)\n/* harmony export */ });\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nconst TENANT_TEMPLATE_ID = '78180355F0A24161A34C3069A9E17539';\nconst CONTENT_ROOT_PATH_ID = '0DE95AE441AB4D019EB067441B7C2450';\nconst TENANT_SHARED_SITES_FIELD_NAME = 'SharedSites';\nconst SITE_ROOT_TEMPLATE_ID = 'A2B9FDC3F641496694A5B63944DC39DE';\nconst TENANTS_PAGESIZE = '50';\nconst SITES_PAGESIZE = '200';\nconst query = `\r\n  query TenantQuery {\r\n    tenants: search(where: {\r\n      AND: [\r\n        {\r\n          name: \"_templates\",\r\n          value: \"${TENANT_TEMPLATE_ID}\",\r\n          operator: CONTAINS\r\n        },\r\n        {\r\n          name: \"_path\",\r\n          value: \"${CONTENT_ROOT_PATH_ID}\",\r\n          operator: CONTAINS\r\n        },\r\n        { name: \"_language\", value: \"en\" }\r\n      ]\r\n    }, first: ${TENANTS_PAGESIZE}) {\r\n      results {\r\n        id,\r\n        path,\r\n        sharedSites: field(name: \"${TENANT_SHARED_SITES_FIELD_NAME}\") {\r\n          ... on MultilistField {\r\n            targetItems {\r\n              id,\r\n              path\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    sites: search(where: {\r\n      AND: [\r\n        {\r\n          name: \"_templates\",\r\n          value: \"${SITE_ROOT_TEMPLATE_ID}\",\r\n          operator: CONTAINS\r\n        },\r\n        {\r\n          name: \"_path\",\r\n          value: \"${CONTENT_ROOT_PATH_ID}\",\r\n          operator: CONTAINS\r\n        }\r\n      ]\r\n    }, first: ${SITES_PAGESIZE}){\r\n      results {\r\n        id,\r\n        path\r\n      }\r\n    },\r\n    siteNames: site {\r\n      siteInfoCollection {\r\n        name,\r\n        rootPath\r\n      }\r\n    }\r\n  }\r\n`;\nclass GraphQLTenantService {\n    constructor(options) {\n        this.graphQlClient = options.clientFactory({\n            fetch: options.fetch,\n        });\n    }\n    fetchTenantData() {\n        return __awaiter(this, void 0, void 0, function* () {\n            const queryResult = yield this.graphQlClient.request(query);\n            if (!queryResult) {\n                console.warn('No data from TenantQuery.');\n                return undefined;\n            }\n            const tenantQueryResponse = queryResult.tenants.results;\n            const sitesQueryResults = queryResult.sites.results;\n            const result = [];\n            //Remove duplicates from site list\n            const allSites = sitesQueryResults.filter((site, index, self) => {\n                return self.findIndex((p) => p.id === site.id) === index;\n            });\n            //All tenant paths sorted by length, so we can check if a site is part of a tenant, most specific first\n            const allTenantPaths = tenantQueryResponse\n                .map((tenant) => tenant.path)\n                .sort((a, b) => b.length - a.length);\n            tenantQueryResponse.forEach((tenant) => {\n                result.push({\n                    id: tenant.id,\n                    path: tenant.path,\n                    sharedSites: tenant.sharedSites.targetItems\n                        .map((x) => {\n                        return {\n                            id: x.id,\n                            name: this.getSiteNameByRootPath(x.path, queryResult.siteNames.siteInfoCollection),\n                            path: x.path,\n                        };\n                    })\n                        .filter((x) => x.name !== ''),\n                    sites: this.getAllSitesForTenant(allSites, allTenantPaths, tenant.path, queryResult.siteNames.siteInfoCollection),\n                });\n            });\n            return result;\n        });\n    }\n    getAllSitesForTenant(sites, allTenantPaths, tenantPath, siteInfoCollection) {\n        const result = [];\n        sites.forEach((site) => {\n            if (site.path.toLocaleLowerCase().startsWith(tenantPath.toLocaleLowerCase())) {\n                //Site is part of this tenant, but also check if this is the most specific tenant\n                const matchedTenant = allTenantPaths.find((x) => site.path.toLocaleLowerCase().startsWith(x.toLocaleLowerCase()));\n                if (matchedTenant && matchedTenant.toLocaleLowerCase() === tenantPath.toLocaleLowerCase()) {\n                    result.push({\n                        id: site.id,\n                        name: this.getSiteNameByRootPath(site.path, siteInfoCollection),\n                        path: site.path,\n                    });\n                }\n            }\n        });\n        return result.filter((x) => x.name !== '');\n    }\n    getSiteNameByRootPath(rootPath, siteInfoQueryResult) {\n        const rootPathNormalized = rootPath.toLowerCase();\n        const siteInfoMatch = siteInfoQueryResult.find((site) => site.rootPath.toLowerCase() === rootPathNormalized);\n        if (!siteInfoMatch) {\n            console.warn(`[GraphQLTenantService] Couldn't find tenant site info match by root path (${rootPathNormalized}).`);\n            return '';\n        }\n        return siteInfoMatch.name;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/graphql-tenant-service.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/tenant-resolver.js":
/*!*************************************************************************************************************************************!*\
  !*** ../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/tenant-resolver.js ***!
  \*************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TenantResolver: () => (/* binding */ TenantResolver)\n/* harmony export */ });\n/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util */ \"(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/util.js\");\n\nclass TenantResolver {\n    constructor(tenants) {\n        this.tenants = tenants;\n    }\n    resolveTenant(site) {\n        const siteRootPath = (0,_util__WEBPACK_IMPORTED_MODULE_0__.getSiteRootPath)(site);\n        if (!siteRootPath || siteRootPath === '') {\n            return null;\n        }\n        for (const tenant of this.tenants) {\n            if (tenant.sites.some((x) => x.path.toLocaleLowerCase() === siteRootPath.toLocaleLowerCase())) {\n                return tenant;\n            }\n        }\n        return null;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9wYWNrYWdlcy9yYWktZXhoaWJpdG9yLXBvcnRhbC9ub2RlX21vZHVsZXMvQHV4YmVlL3V4YmVlLXNpdGVjb3JlLWhlYWRsZXNzLXN4YS1tdWx0aXNpdGUvZGlzdC9lc20vbGliL3RlbmFudC1yZXNvbHZlci5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF5QztBQUNsQztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCLHNEQUFlO0FBQzVDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxXb3JrXFxVWEJFRVxcRm91bmRhdGlvblhNQ2xvdWQyXFxzcmNcXGhlYWRcXHBhY2thZ2VzXFxyYWktZXhoaWJpdG9yLXBvcnRhbFxcbm9kZV9tb2R1bGVzXFxAdXhiZWVcXHV4YmVlLXNpdGVjb3JlLWhlYWRsZXNzLXN4YS1tdWx0aXNpdGVcXGRpc3RcXGVzbVxcbGliXFx0ZW5hbnQtcmVzb2x2ZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZ2V0U2l0ZVJvb3RQYXRoIH0gZnJvbSAnLi91dGlsJztcbmV4cG9ydCBjbGFzcyBUZW5hbnRSZXNvbHZlciB7XG4gICAgY29uc3RydWN0b3IodGVuYW50cykge1xuICAgICAgICB0aGlzLnRlbmFudHMgPSB0ZW5hbnRzO1xuICAgIH1cbiAgICByZXNvbHZlVGVuYW50KHNpdGUpIHtcbiAgICAgICAgY29uc3Qgc2l0ZVJvb3RQYXRoID0gZ2V0U2l0ZVJvb3RQYXRoKHNpdGUpO1xuICAgICAgICBpZiAoIXNpdGVSb290UGF0aCB8fCBzaXRlUm9vdFBhdGggPT09ICcnKSB7XG4gICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgfVxuICAgICAgICBmb3IgKGNvbnN0IHRlbmFudCBvZiB0aGlzLnRlbmFudHMpIHtcbiAgICAgICAgICAgIGlmICh0ZW5hbnQuc2l0ZXMuc29tZSgoeCkgPT4geC5wYXRoLnRvTG9jYWxlTG93ZXJDYXNlKCkgPT09IHNpdGVSb290UGF0aC50b0xvY2FsZUxvd2VyQ2FzZSgpKSkge1xuICAgICAgICAgICAgICAgIHJldHVybiB0ZW5hbnQ7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/tenant-resolver.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/util.js":
/*!**************************************************************************************************************************!*\
  !*** ../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/util.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getSiteIdBySiteName: () => (/* binding */ getSiteIdBySiteName),\n/* harmony export */   getSiteParameterValue: () => (/* binding */ getSiteParameterValue),\n/* harmony export */   getSiteRootPath: () => (/* binding */ getSiteRootPath),\n/* harmony export */   getSiteVirtualFolder: () => (/* binding */ getSiteVirtualFolder),\n/* harmony export */   getTenantSiteBySiteRootPath: () => (/* binding */ getTenantSiteBySiteRootPath)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../constants */ \"(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/constants.js\");\n\nfunction getSiteRootPath(site) {\n    return getSiteParameterValue(site, _constants__WEBPACK_IMPORTED_MODULE_0__.SITE_ROOT_PATH_PARAMETER_NAME);\n}\nfunction getSiteVirtualFolder(site) {\n    let virtualFolder = getSiteParameterValue(site, _constants__WEBPACK_IMPORTED_MODULE_0__.SITE_VIRTUAL_FOLDER_PARAMETER_NAME);\n    if (virtualFolder && virtualFolder !== '/' && virtualFolder.endsWith('/')) {\n        virtualFolder = virtualFolder.substring(0, virtualFolder.length - 1);\n    }\n    return virtualFolder;\n}\nfunction getSiteParameterValue(site, attributeName) {\n    var _a;\n    const attributeValue = (_a = site[attributeName]) === null || _a === void 0 ? void 0 : _a.toString();\n    if (!attributeValue) {\n        console.warn(`Site info '${site.name}' doesn't contain parameter '${attributeName}'.`);\n        return null;\n    }\n    return attributeValue;\n}\nfunction getTenantSiteBySiteRootPath(rootPath, tenant) {\n    const tenantSite = tenant.sites.find((site) => site.path.toLocaleLowerCase() === rootPath.toLocaleLowerCase());\n    return tenantSite !== null && tenantSite !== void 0 ? tenantSite : null;\n}\nfunction getSiteIdBySiteName(site, tenants) {\n    var _a;\n    if (!site) {\n        throw new Error('[GetSiteIdBySiteName] No site provided.');\n    }\n    if (!tenants) {\n        throw new Error('[GetSiteIdBySiteName] No tenants provided.');\n    }\n    const siteRootPath = (_a = getSiteRootPath(site)) === null || _a === void 0 ? void 0 : _a.toLowerCase();\n    if (!siteRootPath) {\n        return undefined;\n    }\n    for (const tenant of tenants) {\n        const tenantSite = tenant.sites.find((x) => x.path.toLowerCase() === siteRootPath);\n        if (tenantSite) {\n            return tenantSite.id;\n        }\n    }\n    return undefined;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/util.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/virtual-folder-aware-site-resolver.js":
/*!********************************************************************************************************************************************************!*\
  !*** ../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/virtual-folder-aware-site-resolver.js ***!
  \********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VirtualFolderAwareSiteResolver: () => (/* binding */ VirtualFolderAwareSiteResolver)\n/* harmony export */ });\n/* harmony import */ var _sitecore_jss_sitecore_jss_site__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @sitecore-jss/sitecore-jss/site */ \"@sitecore-jss/sitecore-jss/site\");\n/* harmony import */ var _sitecore_jss_sitecore_jss_site__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_sitecore_jss_sitecore_jss_site__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util */ \"(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/util.js\");\n\n\nconst DELIMITERS = /\\||,|;/g;\nclass VirtualFolderAwareSiteResolver extends _sitecore_jss_sitecore_jss_site__WEBPACK_IMPORTED_MODULE_0__.SiteResolver {\n    constructor(sites) {\n        super(sites);\n        this.getByHostAndVirtualFolder = (hostName, path) => {\n            const pathNormalized = this.normalizePath(path);\n            const pathSplitted = pathNormalized.split('/');\n            const requestVirtualFolder = pathSplitted.slice(0, 2).join('/').toLowerCase();\n            const sitesMatchingHostAndVirtualPath = this.sites\n                .filter((site) => {\n                const siteVirtualFolder = (0,_util__WEBPACK_IMPORTED_MODULE_1__.getSiteVirtualFolder)(site);\n                return (siteVirtualFolder &&\n                    siteVirtualFolder !== '/' &&\n                    this.matchHost(hostName, site) &&\n                    requestVirtualFolder === siteVirtualFolder);\n            })\n                // sort sites so site with the most specific virtual path goes up\n                .sort((s1, s2) => this.getSiteVirtualPathSegments(s2).length - this.getSiteVirtualPathSegments(s1).length);\n            if (sitesMatchingHostAndVirtualPath.length > 0) {\n                return sitesMatchingHostAndVirtualPath[0];\n            }\n            const siteResolver = new _sitecore_jss_sitecore_jss_site__WEBPACK_IMPORTED_MODULE_0__.SiteResolver(this.getRootWebsites(this.sites));\n            return siteResolver.getByHost(hostName);\n        };\n        this.getRootWebsites = (sites) => {\n            return sites.filter((site) => (0,_util__WEBPACK_IMPORTED_MODULE_1__.getSiteVirtualFolder)(site) === '/');\n        };\n        this.getSiteVirtualPathSegments = (site) => {\n            var _a, _b;\n            return ((_b = (_a = (0,_util__WEBPACK_IMPORTED_MODULE_1__.getSiteVirtualFolder)(site)) === null || _a === void 0 ? void 0 : _a.split('/').filter((x) => x !== '')) !== null && _b !== void 0 ? _b : []);\n        };\n        this.matchHost = (hostName, site) => {\n            const hostNames = site.hostName.replace(/\\s/g, '').toLocaleLowerCase().split(DELIMITERS);\n            for (const host of hostNames) {\n                if (super.matchesPattern(hostName, host)) {\n                    return true;\n                }\n            }\n            return false;\n        };\n        this.normalizePath = (path) => {\n            let pathNormalized = path;\n            pathNormalized = pathNormalized.replaceAll('\\\\', '/');\n            if (!pathNormalized.startsWith('/')) {\n                pathNormalized = `/${pathNormalized}`;\n            }\n            return pathNormalized;\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/virtual-folder-aware-site-resolver.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/default-locale-url-rewrite-middleware.js":
/*!******************************************************************************************************************************************************************!*\
  !*** ../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/default-locale-url-rewrite-middleware.js ***!
  \******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DefaultLocaleUrlRewriteMiddleware: () => (/* binding */ DefaultLocaleUrlRewriteMiddleware)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(pages-dir-node)/../../node_modules/next/server.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_server__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _middleware_util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./middleware-util */ \"(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/middleware-util.js\");\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n\n\nconst FALLBACK_HOST_NAME = 'localhost';\nconst PUBLIC_FILE_PATTERN = /\\.(.*)$/;\nclass DefaultLocaleUrlRewriteMiddleware {\n    constructor(config) {\n        this.config = config;\n        this.handler = (req, res) => __awaiter(this, void 0, void 0, function* () {\n            const hostName = (0,_middleware_util__WEBPACK_IMPORTED_MODULE_1__.getHostName)(req, this.config.defaultHostName) || FALLBACK_HOST_NAME;\n            const pathName = req.nextUrl.pathname;\n            const response = res || next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n            if (this.config.skipForHostName && this.config.skipForHostName(hostName)) {\n                return response;\n            }\n            const site = this.config.siteResolver.getByHostAndVirtualFolder(hostName, pathName);\n            if (!site) {\n                return response;\n            }\n            if (site.language === req.nextUrl.locale) {\n                if (req.nextUrl.pathname.startsWith('/_next') ||\n                    req.nextUrl.pathname.includes('/api/') ||\n                    PUBLIC_FILE_PATTERN.test(req.nextUrl.pathname) ||\n                    req.url.includes('path=')) {\n                    return response;\n                }\n                const reqUrl = new URL(req.url);\n                const reqUrlPath = reqUrl.pathname;\n                if (reqUrlPath.endsWith(`/${site.language}`) || reqUrlPath.startsWith(`/${site.language}/`)) {\n                    const urlStringWithoutDefaultLocale = req.url.replace(reqUrlPath, reqUrlPath.slice(3));\n                    const urlWithoutDefaultLocale = new URL(urlStringWithoutDefaultLocale);\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(urlWithoutDefaultLocale, { status: 301 });\n                }\n            }\n            return response;\n        });\n    }\n    getHandler() {\n        return this.handler;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/default-locale-url-rewrite-middleware.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/handle-default-locale-middleware.js":
/*!*************************************************************************************************************************************************************!*\
  !*** ../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/handle-default-locale-middleware.js ***!
  \*************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HandleDefaultLocaleMiddleware: () => (/* binding */ HandleDefaultLocaleMiddleware)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(pages-dir-node)/../../node_modules/next/server.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_server__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _middleware_util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./middleware-util */ \"(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/middleware-util.js\");\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n\n\nconst FALLBACK_HOST_NAME = 'localhost';\nconst PUBLIC_FILE_PATTERN = /\\.(.*)$/;\nclass HandleDefaultLocaleMiddleware {\n    constructor(config) {\n        this.config = config;\n        this.handler = (req, res) => __awaiter(this, void 0, void 0, function* () {\n            const hostName = (0,_middleware_util__WEBPACK_IMPORTED_MODULE_1__.getHostName)(req, this.config.defaultHostName) || FALLBACK_HOST_NAME;\n            const pathName = req.nextUrl.pathname;\n            const response = res || next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n            if (this.config.skipForHostName && this.config.skipForHostName(hostName)) {\n                return response;\n            }\n            if (req.nextUrl.locale === this.config.defaultLocale) {\n                if (req.nextUrl.pathname.startsWith('/_next') ||\n                    req.nextUrl.pathname.includes('/api/') ||\n                    PUBLIC_FILE_PATTERN.test(req.nextUrl.pathname)) {\n                    return response;\n                }\n                const site = this.config.siteResolver.getByHostAndVirtualFolder(hostName, pathName);\n                if (!site) {\n                    return response;\n                }\n                req.nextUrl.locale = site.language;\n            }\n            return response;\n        });\n    }\n    getHandler() {\n        return this.handler;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/handle-default-locale-middleware.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/middleware-util.js":
/*!********************************************************************************************************************************************!*\
  !*** ../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/middleware-util.js ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getHostName: () => (/* binding */ getHostName)\n/* harmony export */ });\nfunction getHostName(req, defaultHostName) {\n    var _a;\n    return ((_a = req.headers.get('host')) === null || _a === void 0 ? void 0 : _a.split(':')[0]) || defaultHostName;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9wYWNrYWdlcy9yYWktZXhoaWJpdG9yLXBvcnRhbC9ub2RlX21vZHVsZXMvQHV4YmVlL3V4YmVlLXNpdGVjb3JlLWhlYWRsZXNzLXN4YS1tdWx0aXNpdGUvZGlzdC9lc20vbWlkZGxld2FyZS9taWRkbGV3YXJlLXV0aWwuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcV29ya1xcVVhCRUVcXEZvdW5kYXRpb25YTUNsb3VkMlxcc3JjXFxoZWFkXFxwYWNrYWdlc1xccmFpLWV4aGliaXRvci1wb3J0YWxcXG5vZGVfbW9kdWxlc1xcQHV4YmVlXFx1eGJlZS1zaXRlY29yZS1oZWFkbGVzcy1zeGEtbXVsdGlzaXRlXFxkaXN0XFxlc21cXG1pZGRsZXdhcmVcXG1pZGRsZXdhcmUtdXRpbC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gZ2V0SG9zdE5hbWUocmVxLCBkZWZhdWx0SG9zdE5hbWUpIHtcbiAgICB2YXIgX2E7XG4gICAgcmV0dXJuICgoX2EgPSByZXEuaGVhZGVycy5nZXQoJ2hvc3QnKSkgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLnNwbGl0KCc6JylbMF0pIHx8IGRlZmF1bHRIb3N0TmFtZTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/middleware-util.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/site-virtual-folder-middleware.js":
/*!***********************************************************************************************************************************************************!*\
  !*** ../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/site-virtual-folder-middleware.js ***!
  \***********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SiteVirtualFolderMiddleware: () => (/* binding */ SiteVirtualFolderMiddleware)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(pages-dir-node)/../../node_modules/next/server.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_server__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _middleware_util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./middleware-util */ \"(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/middleware-util.js\");\n/* harmony import */ var _sitecore_jss_sitecore_jss_site__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @sitecore-jss/sitecore-jss/site */ \"@sitecore-jss/sitecore-jss/site\");\n/* harmony import */ var _sitecore_jss_sitecore_jss_site__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_sitecore_jss_sitecore_jss_site__WEBPACK_IMPORTED_MODULE_2__);\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n\n\n\nconst FALLBACK_HOST_NAME = 'localhost';\nconst SITE_PARAMETER_NAME = 'sc_site';\nconst REWRITE_HTTP_HEADER_PARAMETER_NAME = 'x-sc-rewrite';\nclass SiteVirtualFolderMiddleware {\n    constructor(config) {\n        this.config = config;\n        this.handler = (req, res) => __awaiter(this, void 0, void 0, function* () {\n            const hostName = (0,_middleware_util__WEBPACK_IMPORTED_MODULE_1__.getHostName)(req, this.config.defaultHostName) || FALLBACK_HOST_NAME;\n            const pathName = req.nextUrl.pathname;\n            let response = res || next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n            const initialSiteRewriteData = response.headers.get(REWRITE_HTTP_HEADER_PARAMETER_NAME);\n            if (!initialSiteRewriteData || initialSiteRewriteData === '') {\n                return response;\n            }\n            const site = this.config.siteResolver.getByHostAndVirtualFolder(hostName, pathName);\n            if (!site) {\n                return response;\n            }\n            const rewritePath = (0,_sitecore_jss_sitecore_jss_site__WEBPACK_IMPORTED_MODULE_2__.getSiteRewrite)(pathName, { siteName: site.name });\n            const rewriteUrl = req.nextUrl.clone();\n            rewriteUrl.pathname = rewritePath;\n            response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.rewrite(rewriteUrl);\n            response.cookies.set(SITE_PARAMETER_NAME, site.name, {\n                secure: true,\n                httpOnly: true,\n                sameSite: 'none',\n            });\n            response.headers.set(REWRITE_HTTP_HEADER_PARAMETER_NAME, rewritePath);\n            return response;\n        });\n    }\n    getHandler() {\n        return this.handler;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/site-virtual-folder-middleware.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/site-resolving-plugins/remove-default-site-duplicate-plugin-core.js":
/*!**********************************************************************************************************************************************************************************!*\
  !*** ../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/site-resolving-plugins/remove-default-site-duplicate-plugin-core.js ***!
  \**********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RemoveDefaultSiteDuplicatePluginCore: () => (/* binding */ RemoveDefaultSiteDuplicatePluginCore)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../constants */ \"(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/constants.js\");\n\nclass RemoveDefaultSiteDuplicatePluginCore {\n    constructor(config) {\n        this.config = config;\n    }\n    removeDefaultSite(sites) {\n        if (!this.config.defaultSiteName || this.config.defaultSiteName === '') {\n            console.warn('Default site name is not specified.', this);\n            return sites;\n        }\n        const sitesSerialized = JSON.parse(this.config.sitesString || '[]');\n        if (!sitesSerialized || sitesSerialized.length === 0) {\n            return sites;\n        }\n        for (let i = 0; i < sites.length; i++) {\n            // Find a site with at least one of extended attribute absent, and that has a match in sites present in Sitecore with the same attribute present.\n            // This site should be removed to make sure it is not duplicated in site resolver.\n            if (_constants__WEBPACK_IMPORTED_MODULE_0__.ExtendedSiteAttributes.find((attribute) => !sites[i][attribute] && sitesSerialized.find((site) => site[attribute]))) {\n                sites.splice(i, 1);\n                break;\n            }\n        }\n        return sites;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/rai-exhibitor-portal/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/site-resolving-plugins/remove-default-site-duplicate-plugin-core.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/config-plugins/default-site-root-path-plugin-core.js":
/*!******************************************************************************************************************************************************!*\
  !*** ../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/config-plugins/default-site-root-path-plugin-core.js ***!
  \******************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DefaultSiteRootPathPluginCore: () => (/* binding */ DefaultSiteRootPathPluginCore)\n/* harmony export */ });\n/* harmony import */ var _lib_util__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/util */ \"(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/util.js\");\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n\nclass DefaultSiteRootPathPluginCore {\n    constructor(config) {\n        this.config = config;\n    }\n    initializeDefaultSiteRootPath() {\n        return __awaiter(this, void 0, void 0, function* () {\n            if (!this.config.sitecoreSiteName || this.config.sitecoreSiteName === '') {\n                console.warn('\"sitecoreSiteName\" property is not defined. Skipping default site root path initialization.');\n                return null;\n            }\n            const defaultSiteName = this.config.sitecoreSiteName;\n            const siteInfoCollection = JSON.parse(this.config.sitesString || '[]');\n            let defaultSiteRootPath = null;\n            if (siteInfoCollection && siteInfoCollection.length > 0) {\n                const defaultSiteInfoMatch = siteInfoCollection.find((siteInfo) => siteInfo.name.toLocaleLowerCase() === (defaultSiteName === null || defaultSiteName === void 0 ? void 0 : defaultSiteName.toLocaleLowerCase()));\n                if (defaultSiteInfoMatch) {\n                    defaultSiteRootPath = (0,_lib_util__WEBPACK_IMPORTED_MODULE_0__.getSiteRootPath)(defaultSiteInfoMatch);\n                }\n            }\n            return defaultSiteRootPath;\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/config-plugins/default-site-root-path-plugin-core.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/config-plugins/site-attribute-plugin-core.js":
/*!**********************************************************************************************************************************************!*\
  !*** ../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/config-plugins/site-attribute-plugin-core.js ***!
  \**********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SiteAttributePluginCore: () => (/* binding */ SiteAttributePluginCore)\n/* harmony export */ });\n/* harmony import */ var _sitecore_jss_sitecore_jss_site__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @sitecore-jss/sitecore-jss/site */ \"@sitecore-jss/sitecore-jss/site\");\n/* harmony import */ var _sitecore_jss_sitecore_jss_site__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_sitecore_jss_sitecore_jss_site__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_graphql_site_attribute_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/graphql-site-attribute-service */ \"(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/graphql-site-attribute-service.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../constants */ \"(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/constants.js\");\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n\n\n\nclass SiteAttributePluginCore {\n    constructor(config) {\n        this.config = config;\n    }\n    initializeSiteAttributes() {\n        return __awaiter(this, void 0, void 0, function* () {\n            const result = [];\n            const clientFactory = this.config.clientFactory;\n            try {\n                let configSites = this.parseSitesFromSitesString();\n                if (!configSites) {\n                    // No Sitecore multisite addon installed. Fetch site info by our own.\n                    configSites = yield this.fetchSiteInfo(clientFactory);\n                }\n                console.log('Fetching site attributes');\n                const siteAttributeService = new _lib_graphql_site_attribute_service__WEBPACK_IMPORTED_MODULE_1__.GraphQLSiteAttributeService({\n                    clientFactory,\n                    fetch: fetch,\n                });\n                const siteAttributes = yield siteAttributeService.fetchSiteAttributes();\n                configSites.forEach((siteInfo) => {\n                    const site = siteAttributes.find((x) => x.name.toLocaleLowerCase() === siteInfo.name.toLocaleLowerCase());\n                    const extendedSiteProperties = {};\n                    if (site) {\n                        _constants__WEBPACK_IMPORTED_MODULE_2__.ExtendedSiteAttributes.forEach((attributeName) => {\n                            const siteAttribute = site.attributes.find((attr) => attr.key === attributeName);\n                            if (siteAttribute) {\n                                extendedSiteProperties[attributeName] = siteAttribute.value;\n                            }\n                        });\n                        // Sitecore Experience Edge doesn't return 'rootPath' attribute in the site attributes object.\n                        // Set it explicitly.\n                        extendedSiteProperties[_constants__WEBPACK_IMPORTED_MODULE_2__.SITE_ROOT_PATH_PARAMETER_NAME] = site.rootPath;\n                    }\n                    result.push(Object.assign({}, siteInfo, extendedSiteProperties));\n                });\n            }\n            catch (error) {\n                console.error('Error at fetching site root paths');\n                console.error(error);\n                return null;\n            }\n            return result;\n        });\n    }\n    parseSitesFromSitesString() {\n        var _a;\n        if (!this.config.sitesString || this.config.sitesString === '') {\n            return null;\n        }\n        return (_a = JSON.parse(this.config.sitesString)) !== null && _a !== void 0 ? _a : [];\n    }\n    fetchSiteInfo(clientFactory) {\n        return __awaiter(this, void 0, void 0, function* () {\n            const siteInfoService = new _sitecore_jss_sitecore_jss_site__WEBPACK_IMPORTED_MODULE_0__.GraphQLSiteInfoService({ clientFactory });\n            return yield siteInfoService.fetchSiteInfo();\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/config-plugins/site-attribute-plugin-core.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/config-plugins/tenant-plugin-core.js":
/*!**************************************************************************************************************************************!*\
  !*** ../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/config-plugins/tenant-plugin-core.js ***!
  \**************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TenantPluginCore: () => (/* binding */ TenantPluginCore)\n/* harmony export */ });\n/* harmony import */ var _lib_graphql_tenant_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/graphql-tenant-service */ \"(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/graphql-tenant-service.js\");\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n\nclass TenantPluginCore {\n    constructor(config) {\n        this.config = config;\n    }\n    getTenants() {\n        return __awaiter(this, void 0, void 0, function* () {\n            const clientFactory = this.config.clientFactory;\n            const fetch = this.config.fetch;\n            try {\n                const tenantService = new _lib_graphql_tenant_service__WEBPACK_IMPORTED_MODULE_0__.GraphQLTenantService({\n                    clientFactory,\n                    fetch,\n                });\n                const tenantResult = yield tenantService.fetchTenantData();\n                return tenantResult !== null && tenantResult !== void 0 ? tenantResult : [];\n            }\n            catch (error) {\n                console.error('Error at fetching tenants');\n                console.error(error);\n                return null;\n            }\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/config-plugins/tenant-plugin-core.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/constants.js":
/*!**************************************************************************************************************!*\
  !*** ../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/constants.js ***!
  \**************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ExtendedSiteAttributes: () => (/* binding */ ExtendedSiteAttributes),\n/* harmony export */   SITE_ROOT_PATH_PARAMETER_NAME: () => (/* binding */ SITE_ROOT_PATH_PARAMETER_NAME),\n/* harmony export */   SITE_VIRTUAL_FOLDER_PARAMETER_NAME: () => (/* binding */ SITE_VIRTUAL_FOLDER_PARAMETER_NAME)\n/* harmony export */ });\nconst ExtendedSiteAttributes = ['virtualFolder'];\n// Parameters\nconst SITE_ROOT_PATH_PARAMETER_NAME = 'rootPath';\nconst SITE_VIRTUAL_FOLDER_PARAMETER_NAME = 'virtualFolder';\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9wYWNrYWdlcy9zaXRlbWFwL25vZGVfbW9kdWxlcy9AdXhiZWUvdXhiZWUtc2l0ZWNvcmUtaGVhZGxlc3Mtc3hhLW11bHRpc2l0ZS9kaXN0L2VzbS9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQU87QUFDUDtBQUNPO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxXb3JrXFxVWEJFRVxcRm91bmRhdGlvblhNQ2xvdWQyXFxzcmNcXGhlYWRcXHBhY2thZ2VzXFxzaXRlbWFwXFxub2RlX21vZHVsZXNcXEB1eGJlZVxcdXhiZWUtc2l0ZWNvcmUtaGVhZGxlc3Mtc3hhLW11bHRpc2l0ZVxcZGlzdFxcZXNtXFxjb25zdGFudHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IEV4dGVuZGVkU2l0ZUF0dHJpYnV0ZXMgPSBbJ3ZpcnR1YWxGb2xkZXInXTtcbi8vIFBhcmFtZXRlcnNcbmV4cG9ydCBjb25zdCBTSVRFX1JPT1RfUEFUSF9QQVJBTUVURVJfTkFNRSA9ICdyb290UGF0aCc7XG5leHBvcnQgY29uc3QgU0lURV9WSVJUVUFMX0ZPTERFUl9QQVJBTUVURVJfTkFNRSA9ICd2aXJ0dWFsRm9sZGVyJztcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/constants.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/index.js":
/*!**********************************************************************************************************!*\
  !*** ../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/index.js ***!
  \**********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _lib_graphql_site_attribute_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/graphql-site-attribute-service */ \"(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/graphql-site-attribute-service.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _lib_graphql_site_attribute_service__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _lib_graphql_site_attribute_service__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _lib_graphql_tenant_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/graphql-tenant-service */ \"(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/graphql-tenant-service.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _lib_graphql_tenant_service__WEBPACK_IMPORTED_MODULE_1__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _lib_graphql_tenant_service__WEBPACK_IMPORTED_MODULE_1__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _config_plugins_site_attribute_plugin_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./config-plugins/site-attribute-plugin-core */ \"(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/config-plugins/site-attribute-plugin-core.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _config_plugins_site_attribute_plugin_core__WEBPACK_IMPORTED_MODULE_2__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _config_plugins_site_attribute_plugin_core__WEBPACK_IMPORTED_MODULE_2__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _config_plugins_default_site_root_path_plugin_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./config-plugins/default-site-root-path-plugin-core */ \"(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/config-plugins/default-site-root-path-plugin-core.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _config_plugins_default_site_root_path_plugin_core__WEBPACK_IMPORTED_MODULE_3__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _config_plugins_default_site_root_path_plugin_core__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _config_plugins_tenant_plugin_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./config-plugins/tenant-plugin-core */ \"(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/config-plugins/tenant-plugin-core.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _config_plugins_tenant_plugin_core__WEBPACK_IMPORTED_MODULE_4__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _config_plugins_tenant_plugin_core__WEBPACK_IMPORTED_MODULE_4__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _site_resolving_plugins_remove_default_site_duplicate_plugin_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./site-resolving-plugins/remove-default-site-duplicate-plugin-core */ \"(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/site-resolving-plugins/remove-default-site-duplicate-plugin-core.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _site_resolving_plugins_remove_default_site_duplicate_plugin_core__WEBPACK_IMPORTED_MODULE_5__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _site_resolving_plugins_remove_default_site_duplicate_plugin_core__WEBPACK_IMPORTED_MODULE_5__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _lib_virtual_folder_aware_site_resolver__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./lib/virtual-folder-aware-site-resolver */ \"(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/virtual-folder-aware-site-resolver.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _lib_virtual_folder_aware_site_resolver__WEBPACK_IMPORTED_MODULE_6__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _lib_virtual_folder_aware_site_resolver__WEBPACK_IMPORTED_MODULE_6__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _lib_tenant_resolver__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./lib/tenant-resolver */ \"(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/tenant-resolver.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _lib_tenant_resolver__WEBPACK_IMPORTED_MODULE_7__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _lib_tenant_resolver__WEBPACK_IMPORTED_MODULE_7__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _middleware_site_virtual_folder_middleware__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./middleware/site-virtual-folder-middleware */ \"(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/site-virtual-folder-middleware.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _middleware_site_virtual_folder_middleware__WEBPACK_IMPORTED_MODULE_8__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _middleware_site_virtual_folder_middleware__WEBPACK_IMPORTED_MODULE_8__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _middleware_handle_default_locale_middleware__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./middleware/handle-default-locale-middleware */ \"(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/handle-default-locale-middleware.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _middleware_handle_default_locale_middleware__WEBPACK_IMPORTED_MODULE_9__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _middleware_handle_default_locale_middleware__WEBPACK_IMPORTED_MODULE_9__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _middleware_default_locale_url_rewrite_middleware__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./middleware/default-locale-url-rewrite-middleware */ \"(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/default-locale-url-rewrite-middleware.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _middleware_default_locale_url_rewrite_middleware__WEBPACK_IMPORTED_MODULE_10__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _middleware_default_locale_url_rewrite_middleware__WEBPACK_IMPORTED_MODULE_10__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./constants */ \"(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/constants.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _constants__WEBPACK_IMPORTED_MODULE_11__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _constants__WEBPACK_IMPORTED_MODULE_11__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _lib_util__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./lib/util */ \"(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/util.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _lib_util__WEBPACK_IMPORTED_MODULE_12__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _lib_util__WEBPACK_IMPORTED_MODULE_12__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n// services\n\n\n// configuration plugins\n\n\n\n\n// site resolver\n\n// tenant resolver\n\n// middleware\n\n\n\n// constants\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9wYWNrYWdlcy9zaXRlbWFwL25vZGVfbW9kdWxlcy9AdXhiZWUvdXhiZWUtc2l0ZWNvcmUtaGVhZGxlc3Mtc3hhLW11bHRpc2l0ZS9kaXN0L2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBO0FBQ3FEO0FBQ1I7QUFDN0M7QUFDNEQ7QUFDUTtBQUNoQjtBQUMrQjtBQUNuRjtBQUN5RDtBQUN6RDtBQUNzQztBQUN0QztBQUM0RDtBQUNFO0FBQ0s7QUFDbkU7QUFDNEI7QUFDRCIsInNvdXJjZXMiOlsiRDpcXFdvcmtcXFVYQkVFXFxGb3VuZGF0aW9uWE1DbG91ZDJcXHNyY1xcaGVhZFxccGFja2FnZXNcXHNpdGVtYXBcXG5vZGVfbW9kdWxlc1xcQHV4YmVlXFx1eGJlZS1zaXRlY29yZS1oZWFkbGVzcy1zeGEtbXVsdGlzaXRlXFxkaXN0XFxlc21cXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNlcnZpY2VzXG5leHBvcnQgKiBmcm9tICcuL2xpYi9ncmFwaHFsLXNpdGUtYXR0cmlidXRlLXNlcnZpY2UnO1xuZXhwb3J0ICogZnJvbSAnLi9saWIvZ3JhcGhxbC10ZW5hbnQtc2VydmljZSc7XG4vLyBjb25maWd1cmF0aW9uIHBsdWdpbnNcbmV4cG9ydCAqIGZyb20gJy4vY29uZmlnLXBsdWdpbnMvc2l0ZS1hdHRyaWJ1dGUtcGx1Z2luLWNvcmUnO1xuZXhwb3J0ICogZnJvbSAnLi9jb25maWctcGx1Z2lucy9kZWZhdWx0LXNpdGUtcm9vdC1wYXRoLXBsdWdpbi1jb3JlJztcbmV4cG9ydCAqIGZyb20gJy4vY29uZmlnLXBsdWdpbnMvdGVuYW50LXBsdWdpbi1jb3JlJztcbmV4cG9ydCAqIGZyb20gJy4vc2l0ZS1yZXNvbHZpbmctcGx1Z2lucy9yZW1vdmUtZGVmYXVsdC1zaXRlLWR1cGxpY2F0ZS1wbHVnaW4tY29yZSc7XG4vLyBzaXRlIHJlc29sdmVyXG5leHBvcnQgKiBmcm9tICcuL2xpYi92aXJ0dWFsLWZvbGRlci1hd2FyZS1zaXRlLXJlc29sdmVyJztcbi8vIHRlbmFudCByZXNvbHZlclxuZXhwb3J0ICogZnJvbSAnLi9saWIvdGVuYW50LXJlc29sdmVyJztcbi8vIG1pZGRsZXdhcmVcbmV4cG9ydCAqIGZyb20gJy4vbWlkZGxld2FyZS9zaXRlLXZpcnR1YWwtZm9sZGVyLW1pZGRsZXdhcmUnO1xuZXhwb3J0ICogZnJvbSAnLi9taWRkbGV3YXJlL2hhbmRsZS1kZWZhdWx0LWxvY2FsZS1taWRkbGV3YXJlJztcbmV4cG9ydCAqIGZyb20gJy4vbWlkZGxld2FyZS9kZWZhdWx0LWxvY2FsZS11cmwtcmV3cml0ZS1taWRkbGV3YXJlJztcbi8vIGNvbnN0YW50c1xuZXhwb3J0ICogZnJvbSAnLi9jb25zdGFudHMnO1xuZXhwb3J0ICogZnJvbSAnLi9saWIvdXRpbCc7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/index.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/graphql-site-attribute-service.js":
/*!***************************************************************************************************************************************!*\
  !*** ../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/graphql-site-attribute-service.js ***!
  \***************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GraphQLSiteAttributeService: () => (/* binding */ GraphQLSiteAttributeService)\n/* harmony export */ });\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nconst query = `\r\n  {\r\n    site {\r\n      siteInfoCollection {\r\n        name,\r\n        rootPath,\r\n        attributes {\r\n          key,\r\n          value\r\n        }\r\n      }\r\n    }\r\n  }\r\n`;\nclass GraphQLSiteAttributeService {\n    get query() {\n        return query;\n    }\n    constructor(options) {\n        this.graphQlClient = options.clientFactory({\n            fetch: options.fetch,\n        });\n    }\n    fetchSiteAttributes() {\n        return __awaiter(this, void 0, void 0, function* () {\n            var _a;\n            const queryResult = yield this.graphQlClient.request(this.query);\n            const siteData = (_a = queryResult === null || queryResult === void 0 ? void 0 : queryResult.site) === null || _a === void 0 ? void 0 : _a.siteInfoCollection;\n            if (!siteData) {\n                throw new Error('No site data from site collection.');\n            }\n            const result = [];\n            siteData.forEach((site) => result.push(site));\n            return result;\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/graphql-site-attribute-service.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/graphql-tenant-service.js":
/*!*******************************************************************************************************************************!*\
  !*** ../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/graphql-tenant-service.js ***!
  \*******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GraphQLTenantService: () => (/* binding */ GraphQLTenantService)\n/* harmony export */ });\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nconst TENANT_TEMPLATE_ID = '78180355F0A24161A34C3069A9E17539';\nconst CONTENT_ROOT_PATH_ID = '0DE95AE441AB4D019EB067441B7C2450';\nconst TENANT_SHARED_SITES_FIELD_NAME = 'SharedSites';\nconst SITE_ROOT_TEMPLATE_ID = 'A2B9FDC3F641496694A5B63944DC39DE';\nconst TENANTS_PAGESIZE = '50';\nconst SITES_PAGESIZE = '200';\nconst query = `\r\n  query TenantQuery {\r\n    tenants: search(where: {\r\n      AND: [\r\n        {\r\n          name: \"_templates\",\r\n          value: \"${TENANT_TEMPLATE_ID}\",\r\n          operator: CONTAINS\r\n        },\r\n        {\r\n          name: \"_path\",\r\n          value: \"${CONTENT_ROOT_PATH_ID}\",\r\n          operator: CONTAINS\r\n        },\r\n        { name: \"_language\", value: \"en\" }\r\n      ]\r\n    }, first: ${TENANTS_PAGESIZE}) {\r\n      results {\r\n        id,\r\n        path,\r\n        sharedSites: field(name: \"${TENANT_SHARED_SITES_FIELD_NAME}\") {\r\n          ... on MultilistField {\r\n            targetItems {\r\n              id,\r\n              path\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    sites: search(where: {\r\n      AND: [\r\n        {\r\n          name: \"_templates\",\r\n          value: \"${SITE_ROOT_TEMPLATE_ID}\",\r\n          operator: CONTAINS\r\n        },\r\n        {\r\n          name: \"_path\",\r\n          value: \"${CONTENT_ROOT_PATH_ID}\",\r\n          operator: CONTAINS\r\n        }\r\n      ]\r\n    }, first: ${SITES_PAGESIZE}){\r\n      results {\r\n        id,\r\n        path\r\n      }\r\n    },\r\n    siteNames: site {\r\n      siteInfoCollection {\r\n        name,\r\n        rootPath\r\n      }\r\n    }\r\n  }\r\n`;\nclass GraphQLTenantService {\n    constructor(options) {\n        this.graphQlClient = options.clientFactory({\n            fetch: options.fetch,\n        });\n    }\n    fetchTenantData() {\n        return __awaiter(this, void 0, void 0, function* () {\n            const queryResult = yield this.graphQlClient.request(query);\n            if (!queryResult) {\n                console.warn('No data from TenantQuery.');\n                return undefined;\n            }\n            const tenantQueryResponse = queryResult.tenants.results;\n            const sitesQueryResults = queryResult.sites.results;\n            const result = [];\n            //Remove duplicates from site list\n            const allSites = sitesQueryResults.filter((site, index, self) => {\n                return self.findIndex((p) => p.id === site.id) === index;\n            });\n            //All tenant paths sorted by length, so we can check if a site is part of a tenant, most specific first\n            const allTenantPaths = tenantQueryResponse\n                .map((tenant) => tenant.path)\n                .sort((a, b) => b.length - a.length);\n            tenantQueryResponse.forEach((tenant) => {\n                result.push({\n                    id: tenant.id,\n                    path: tenant.path,\n                    sharedSites: tenant.sharedSites.targetItems\n                        .map((x) => {\n                        return {\n                            id: x.id,\n                            name: this.getSiteNameByRootPath(x.path, queryResult.siteNames.siteInfoCollection),\n                            path: x.path,\n                        };\n                    })\n                        .filter((x) => x.name !== ''),\n                    sites: this.getAllSitesForTenant(allSites, allTenantPaths, tenant.path, queryResult.siteNames.siteInfoCollection),\n                });\n            });\n            return result;\n        });\n    }\n    getAllSitesForTenant(sites, allTenantPaths, tenantPath, siteInfoCollection) {\n        const result = [];\n        sites.forEach((site) => {\n            if (site.path.toLocaleLowerCase().startsWith(tenantPath.toLocaleLowerCase())) {\n                //Site is part of this tenant, but also check if this is the most specific tenant\n                const matchedTenant = allTenantPaths.find((x) => site.path.toLocaleLowerCase().startsWith(x.toLocaleLowerCase()));\n                if (matchedTenant && matchedTenant.toLocaleLowerCase() === tenantPath.toLocaleLowerCase()) {\n                    result.push({\n                        id: site.id,\n                        name: this.getSiteNameByRootPath(site.path, siteInfoCollection),\n                        path: site.path,\n                    });\n                }\n            }\n        });\n        return result.filter((x) => x.name !== '');\n    }\n    getSiteNameByRootPath(rootPath, siteInfoQueryResult) {\n        const rootPathNormalized = rootPath.toLowerCase();\n        const siteInfoMatch = siteInfoQueryResult.find((site) => site.rootPath.toLowerCase() === rootPathNormalized);\n        if (!siteInfoMatch) {\n            console.warn(`[GraphQLTenantService] Couldn't find tenant site info match by root path (${rootPathNormalized}).`);\n            return '';\n        }\n        return siteInfoMatch.name;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/graphql-tenant-service.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/tenant-resolver.js":
/*!************************************************************************************************************************!*\
  !*** ../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/tenant-resolver.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TenantResolver: () => (/* binding */ TenantResolver)\n/* harmony export */ });\n/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util */ \"(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/util.js\");\n\nclass TenantResolver {\n    constructor(tenants) {\n        this.tenants = tenants;\n    }\n    resolveTenant(site) {\n        const siteRootPath = (0,_util__WEBPACK_IMPORTED_MODULE_0__.getSiteRootPath)(site);\n        if (!siteRootPath || siteRootPath === '') {\n            return null;\n        }\n        for (const tenant of this.tenants) {\n            if (tenant.sites.some((x) => x.path.toLocaleLowerCase() === siteRootPath.toLocaleLowerCase())) {\n                return tenant;\n            }\n        }\n        return null;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9wYWNrYWdlcy9zaXRlbWFwL25vZGVfbW9kdWxlcy9AdXhiZWUvdXhiZWUtc2l0ZWNvcmUtaGVhZGxlc3Mtc3hhLW11bHRpc2l0ZS9kaXN0L2VzbS9saWIvdGVuYW50LXJlc29sdmVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXlDO0FBQ2xDO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2QkFBNkIsc0RBQWU7QUFDNUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXFdvcmtcXFVYQkVFXFxGb3VuZGF0aW9uWE1DbG91ZDJcXHNyY1xcaGVhZFxccGFja2FnZXNcXHNpdGVtYXBcXG5vZGVfbW9kdWxlc1xcQHV4YmVlXFx1eGJlZS1zaXRlY29yZS1oZWFkbGVzcy1zeGEtbXVsdGlzaXRlXFxkaXN0XFxlc21cXGxpYlxcdGVuYW50LXJlc29sdmVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGdldFNpdGVSb290UGF0aCB9IGZyb20gJy4vdXRpbCc7XG5leHBvcnQgY2xhc3MgVGVuYW50UmVzb2x2ZXIge1xuICAgIGNvbnN0cnVjdG9yKHRlbmFudHMpIHtcbiAgICAgICAgdGhpcy50ZW5hbnRzID0gdGVuYW50cztcbiAgICB9XG4gICAgcmVzb2x2ZVRlbmFudChzaXRlKSB7XG4gICAgICAgIGNvbnN0IHNpdGVSb290UGF0aCA9IGdldFNpdGVSb290UGF0aChzaXRlKTtcbiAgICAgICAgaWYgKCFzaXRlUm9vdFBhdGggfHwgc2l0ZVJvb3RQYXRoID09PSAnJykge1xuICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgIH1cbiAgICAgICAgZm9yIChjb25zdCB0ZW5hbnQgb2YgdGhpcy50ZW5hbnRzKSB7XG4gICAgICAgICAgICBpZiAodGVuYW50LnNpdGVzLnNvbWUoKHgpID0+IHgucGF0aC50b0xvY2FsZUxvd2VyQ2FzZSgpID09PSBzaXRlUm9vdFBhdGgudG9Mb2NhbGVMb3dlckNhc2UoKSkpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gdGVuYW50O1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIHJldHVybiBudWxsO1xuICAgIH1cbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/tenant-resolver.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/util.js":
/*!*************************************************************************************************************!*\
  !*** ../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/util.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getSiteIdBySiteName: () => (/* binding */ getSiteIdBySiteName),\n/* harmony export */   getSiteParameterValue: () => (/* binding */ getSiteParameterValue),\n/* harmony export */   getSiteRootPath: () => (/* binding */ getSiteRootPath),\n/* harmony export */   getSiteVirtualFolder: () => (/* binding */ getSiteVirtualFolder),\n/* harmony export */   getTenantSiteBySiteRootPath: () => (/* binding */ getTenantSiteBySiteRootPath)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../constants */ \"(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/constants.js\");\n\nfunction getSiteRootPath(site) {\n    return getSiteParameterValue(site, _constants__WEBPACK_IMPORTED_MODULE_0__.SITE_ROOT_PATH_PARAMETER_NAME);\n}\nfunction getSiteVirtualFolder(site) {\n    let virtualFolder = getSiteParameterValue(site, _constants__WEBPACK_IMPORTED_MODULE_0__.SITE_VIRTUAL_FOLDER_PARAMETER_NAME);\n    if (virtualFolder && virtualFolder !== '/' && virtualFolder.endsWith('/')) {\n        virtualFolder = virtualFolder.substring(0, virtualFolder.length - 1);\n    }\n    return virtualFolder;\n}\nfunction getSiteParameterValue(site, attributeName) {\n    var _a;\n    const attributeValue = (_a = site[attributeName]) === null || _a === void 0 ? void 0 : _a.toString();\n    if (!attributeValue) {\n        console.warn(`Site info '${site.name}' doesn't contain parameter '${attributeName}'.`);\n        return null;\n    }\n    return attributeValue;\n}\nfunction getTenantSiteBySiteRootPath(rootPath, tenant) {\n    const tenantSite = tenant.sites.find((site) => site.path.toLocaleLowerCase() === rootPath.toLocaleLowerCase());\n    return tenantSite !== null && tenantSite !== void 0 ? tenantSite : null;\n}\nfunction getSiteIdBySiteName(site, tenants) {\n    var _a;\n    if (!site) {\n        throw new Error('[GetSiteIdBySiteName] No site provided.');\n    }\n    if (!tenants) {\n        throw new Error('[GetSiteIdBySiteName] No tenants provided.');\n    }\n    const siteRootPath = (_a = getSiteRootPath(site)) === null || _a === void 0 ? void 0 : _a.toLowerCase();\n    if (!siteRootPath) {\n        return undefined;\n    }\n    for (const tenant of tenants) {\n        const tenantSite = tenant.sites.find((x) => x.path.toLowerCase() === siteRootPath);\n        if (tenantSite) {\n            return tenantSite.id;\n        }\n    }\n    return undefined;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/util.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/virtual-folder-aware-site-resolver.js":
/*!*******************************************************************************************************************************************!*\
  !*** ../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/virtual-folder-aware-site-resolver.js ***!
  \*******************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VirtualFolderAwareSiteResolver: () => (/* binding */ VirtualFolderAwareSiteResolver)\n/* harmony export */ });\n/* harmony import */ var _sitecore_jss_sitecore_jss_site__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @sitecore-jss/sitecore-jss/site */ \"@sitecore-jss/sitecore-jss/site\");\n/* harmony import */ var _sitecore_jss_sitecore_jss_site__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_sitecore_jss_sitecore_jss_site__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util */ \"(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/util.js\");\n\n\nconst DELIMITERS = /\\||,|;/g;\nclass VirtualFolderAwareSiteResolver extends _sitecore_jss_sitecore_jss_site__WEBPACK_IMPORTED_MODULE_0__.SiteResolver {\n    constructor(sites) {\n        super(sites);\n        this.getByHostAndVirtualFolder = (hostName, path) => {\n            const pathNormalized = this.normalizePath(path);\n            const pathSplitted = pathNormalized.split('/');\n            const requestVirtualFolder = pathSplitted.slice(0, 2).join('/').toLowerCase();\n            const sitesMatchingHostAndVirtualPath = this.sites\n                .filter((site) => {\n                const siteVirtualFolder = (0,_util__WEBPACK_IMPORTED_MODULE_1__.getSiteVirtualFolder)(site);\n                return (siteVirtualFolder &&\n                    siteVirtualFolder !== '/' &&\n                    this.matchHost(hostName, site) &&\n                    requestVirtualFolder === siteVirtualFolder);\n            })\n                // sort sites so site with the most specific virtual path goes up\n                .sort((s1, s2) => this.getSiteVirtualPathSegments(s2).length - this.getSiteVirtualPathSegments(s1).length);\n            if (sitesMatchingHostAndVirtualPath.length > 0) {\n                return sitesMatchingHostAndVirtualPath[0];\n            }\n            const siteResolver = new _sitecore_jss_sitecore_jss_site__WEBPACK_IMPORTED_MODULE_0__.SiteResolver(this.getRootWebsites(this.sites));\n            return siteResolver.getByHost(hostName);\n        };\n        this.getRootWebsites = (sites) => {\n            return sites.filter((site) => (0,_util__WEBPACK_IMPORTED_MODULE_1__.getSiteVirtualFolder)(site) === '/');\n        };\n        this.getSiteVirtualPathSegments = (site) => {\n            var _a, _b;\n            return ((_b = (_a = (0,_util__WEBPACK_IMPORTED_MODULE_1__.getSiteVirtualFolder)(site)) === null || _a === void 0 ? void 0 : _a.split('/').filter((x) => x !== '')) !== null && _b !== void 0 ? _b : []);\n        };\n        this.matchHost = (hostName, site) => {\n            const hostNames = site.hostName.replace(/\\s/g, '').toLocaleLowerCase().split(DELIMITERS);\n            for (const host of hostNames) {\n                if (super.matchesPattern(hostName, host)) {\n                    return true;\n                }\n            }\n            return false;\n        };\n        this.normalizePath = (path) => {\n            let pathNormalized = path;\n            pathNormalized = pathNormalized.replaceAll('\\\\', '/');\n            if (!pathNormalized.startsWith('/')) {\n                pathNormalized = `/${pathNormalized}`;\n            }\n            return pathNormalized;\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/lib/virtual-folder-aware-site-resolver.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/default-locale-url-rewrite-middleware.js":
/*!*****************************************************************************************************************************************************!*\
  !*** ../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/default-locale-url-rewrite-middleware.js ***!
  \*****************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DefaultLocaleUrlRewriteMiddleware: () => (/* binding */ DefaultLocaleUrlRewriteMiddleware)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(pages-dir-node)/../../node_modules/next/server.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_server__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _middleware_util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./middleware-util */ \"(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/middleware-util.js\");\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n\n\nconst FALLBACK_HOST_NAME = 'localhost';\nconst PUBLIC_FILE_PATTERN = /\\.(.*)$/;\nclass DefaultLocaleUrlRewriteMiddleware {\n    constructor(config) {\n        this.config = config;\n        this.handler = (req, res) => __awaiter(this, void 0, void 0, function* () {\n            const hostName = (0,_middleware_util__WEBPACK_IMPORTED_MODULE_1__.getHostName)(req, this.config.defaultHostName) || FALLBACK_HOST_NAME;\n            const pathName = req.nextUrl.pathname;\n            const response = res || next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n            if (this.config.skipForHostName && this.config.skipForHostName(hostName)) {\n                return response;\n            }\n            const site = this.config.siteResolver.getByHostAndVirtualFolder(hostName, pathName);\n            if (!site) {\n                return response;\n            }\n            if (site.language === req.nextUrl.locale) {\n                if (req.nextUrl.pathname.startsWith('/_next') ||\n                    req.nextUrl.pathname.includes('/api/') ||\n                    PUBLIC_FILE_PATTERN.test(req.nextUrl.pathname) ||\n                    req.url.includes('path=')) {\n                    return response;\n                }\n                const reqUrl = new URL(req.url);\n                const reqUrlPath = reqUrl.pathname;\n                if (reqUrlPath.endsWith(`/${site.language}`) || reqUrlPath.startsWith(`/${site.language}/`)) {\n                    const urlStringWithoutDefaultLocale = req.url.replace(reqUrlPath, reqUrlPath.slice(3));\n                    const urlWithoutDefaultLocale = new URL(urlStringWithoutDefaultLocale);\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(urlWithoutDefaultLocale, { status: 301 });\n                }\n            }\n            return response;\n        });\n    }\n    getHandler() {\n        return this.handler;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/default-locale-url-rewrite-middleware.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/handle-default-locale-middleware.js":
/*!************************************************************************************************************************************************!*\
  !*** ../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/handle-default-locale-middleware.js ***!
  \************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HandleDefaultLocaleMiddleware: () => (/* binding */ HandleDefaultLocaleMiddleware)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(pages-dir-node)/../../node_modules/next/server.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_server__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _middleware_util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./middleware-util */ \"(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/middleware-util.js\");\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n\n\nconst FALLBACK_HOST_NAME = 'localhost';\nconst PUBLIC_FILE_PATTERN = /\\.(.*)$/;\nclass HandleDefaultLocaleMiddleware {\n    constructor(config) {\n        this.config = config;\n        this.handler = (req, res) => __awaiter(this, void 0, void 0, function* () {\n            const hostName = (0,_middleware_util__WEBPACK_IMPORTED_MODULE_1__.getHostName)(req, this.config.defaultHostName) || FALLBACK_HOST_NAME;\n            const pathName = req.nextUrl.pathname;\n            const response = res || next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n            if (this.config.skipForHostName && this.config.skipForHostName(hostName)) {\n                return response;\n            }\n            if (req.nextUrl.locale === this.config.defaultLocale) {\n                if (req.nextUrl.pathname.startsWith('/_next') ||\n                    req.nextUrl.pathname.includes('/api/') ||\n                    PUBLIC_FILE_PATTERN.test(req.nextUrl.pathname)) {\n                    return response;\n                }\n                const site = this.config.siteResolver.getByHostAndVirtualFolder(hostName, pathName);\n                if (!site) {\n                    return response;\n                }\n                req.nextUrl.locale = site.language;\n            }\n            return response;\n        });\n    }\n    getHandler() {\n        return this.handler;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/handle-default-locale-middleware.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/middleware-util.js":
/*!*******************************************************************************************************************************!*\
  !*** ../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/middleware-util.js ***!
  \*******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getHostName: () => (/* binding */ getHostName)\n/* harmony export */ });\nfunction getHostName(req, defaultHostName) {\n    var _a;\n    return ((_a = req.headers.get('host')) === null || _a === void 0 ? void 0 : _a.split(':')[0]) || defaultHostName;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9wYWNrYWdlcy9zaXRlbWFwL25vZGVfbW9kdWxlcy9AdXhiZWUvdXhiZWUtc2l0ZWNvcmUtaGVhZGxlc3Mtc3hhLW11bHRpc2l0ZS9kaXN0L2VzbS9taWRkbGV3YXJlL21pZGRsZXdhcmUtdXRpbC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxXb3JrXFxVWEJFRVxcRm91bmRhdGlvblhNQ2xvdWQyXFxzcmNcXGhlYWRcXHBhY2thZ2VzXFxzaXRlbWFwXFxub2RlX21vZHVsZXNcXEB1eGJlZVxcdXhiZWUtc2l0ZWNvcmUtaGVhZGxlc3Mtc3hhLW11bHRpc2l0ZVxcZGlzdFxcZXNtXFxtaWRkbGV3YXJlXFxtaWRkbGV3YXJlLXV0aWwuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIGdldEhvc3ROYW1lKHJlcSwgZGVmYXVsdEhvc3ROYW1lKSB7XG4gICAgdmFyIF9hO1xuICAgIHJldHVybiAoKF9hID0gcmVxLmhlYWRlcnMuZ2V0KCdob3N0JykpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS5zcGxpdCgnOicpWzBdKSB8fCBkZWZhdWx0SG9zdE5hbWU7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/middleware-util.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/site-virtual-folder-middleware.js":
/*!**********************************************************************************************************************************************!*\
  !*** ../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/site-virtual-folder-middleware.js ***!
  \**********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SiteVirtualFolderMiddleware: () => (/* binding */ SiteVirtualFolderMiddleware)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(pages-dir-node)/../../node_modules/next/server.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_server__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _middleware_util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./middleware-util */ \"(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/middleware-util.js\");\n/* harmony import */ var _sitecore_jss_sitecore_jss_site__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @sitecore-jss/sitecore-jss/site */ \"@sitecore-jss/sitecore-jss/site\");\n/* harmony import */ var _sitecore_jss_sitecore_jss_site__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_sitecore_jss_sitecore_jss_site__WEBPACK_IMPORTED_MODULE_2__);\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n\n\n\nconst FALLBACK_HOST_NAME = 'localhost';\nconst SITE_PARAMETER_NAME = 'sc_site';\nconst REWRITE_HTTP_HEADER_PARAMETER_NAME = 'x-sc-rewrite';\nclass SiteVirtualFolderMiddleware {\n    constructor(config) {\n        this.config = config;\n        this.handler = (req, res) => __awaiter(this, void 0, void 0, function* () {\n            const hostName = (0,_middleware_util__WEBPACK_IMPORTED_MODULE_1__.getHostName)(req, this.config.defaultHostName) || FALLBACK_HOST_NAME;\n            const pathName = req.nextUrl.pathname;\n            let response = res || next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n            const initialSiteRewriteData = response.headers.get(REWRITE_HTTP_HEADER_PARAMETER_NAME);\n            if (!initialSiteRewriteData || initialSiteRewriteData === '') {\n                return response;\n            }\n            const site = this.config.siteResolver.getByHostAndVirtualFolder(hostName, pathName);\n            if (!site) {\n                return response;\n            }\n            const rewritePath = (0,_sitecore_jss_sitecore_jss_site__WEBPACK_IMPORTED_MODULE_2__.getSiteRewrite)(pathName, { siteName: site.name });\n            const rewriteUrl = req.nextUrl.clone();\n            rewriteUrl.pathname = rewritePath;\n            response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.rewrite(rewriteUrl);\n            response.cookies.set(SITE_PARAMETER_NAME, site.name, {\n                secure: true,\n                httpOnly: true,\n                sameSite: 'none',\n            });\n            response.headers.set(REWRITE_HTTP_HEADER_PARAMETER_NAME, rewritePath);\n            return response;\n        });\n    }\n    getHandler() {\n        return this.handler;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/middleware/site-virtual-folder-middleware.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/site-resolving-plugins/remove-default-site-duplicate-plugin-core.js":
/*!*********************************************************************************************************************************************************************!*\
  !*** ../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/site-resolving-plugins/remove-default-site-duplicate-plugin-core.js ***!
  \*********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RemoveDefaultSiteDuplicatePluginCore: () => (/* binding */ RemoveDefaultSiteDuplicatePluginCore)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../constants */ \"(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/constants.js\");\n\nclass RemoveDefaultSiteDuplicatePluginCore {\n    constructor(config) {\n        this.config = config;\n    }\n    removeDefaultSite(sites) {\n        if (!this.config.defaultSiteName || this.config.defaultSiteName === '') {\n            console.warn('Default site name is not specified.', this);\n            return sites;\n        }\n        const sitesSerialized = JSON.parse(this.config.sitesString || '[]');\n        if (!sitesSerialized || sitesSerialized.length === 0) {\n            return sites;\n        }\n        for (let i = 0; i < sites.length; i++) {\n            // Find a site with at least one of extended attribute absent, and that has a match in sites present in Sitecore with the same attribute present.\n            // This site should be removed to make sure it is not duplicated in site resolver.\n            if (_constants__WEBPACK_IMPORTED_MODULE_0__.ExtendedSiteAttributes.find((attribute) => !sites[i][attribute] && sitesSerialized.find((site) => site[attribute]))) {\n                sites.splice(i, 1);\n                break;\n            }\n        }\n        return sites;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../packages/sitemap/node_modules/@uxbee/uxbee-sitecore-headless-sxa-multisite/dist/esm/site-resolving-plugins/remove-default-site-duplicate-plugin-core.js\n");

/***/ })

};
;
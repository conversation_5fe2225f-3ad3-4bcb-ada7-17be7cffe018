// import { withActions } from "@storybook/addon-actions/decorator";
import type { Meta, StoryObj } from "@storybook/react";
import { FormProvider, useForm } from "react-hook-form";
import { AnswerValue, Checkbox, InputField } from "ui";
import { useState } from 'react';
import { Label, Radio } from 'flowbite-react';
// More on how to set up stories at: https://storybook.js.org/docs/react/writing-stories/introduction
const meta: Meta<typeof InputField> = {
  title: "ui/Atomic/Atoms/Forms/InputField",
  component: InputField,
  tags: ["ui", "molecules", "forms"],
  parameters: {},
  argTypes: {
    type: {
      options: ["text", "email", "password", "textarea"],
      control: { type: "select" },
      defaultValue: "text",
    },
  },
};

export default meta;

type Story = StoryObj<typeof InputField>;

interface FormFields {
  string: string;
  number: number;
}

export const Default: Story = {
  render: (args) => {
    const methods = useForm<FormFields>();
    const onSubmit = (data: FormFields) => {};

    return (
      <div className="w-screen h-screen p-5 gap-y-[10px]">
        <FormProvider {...methods}>
          <form
            className="overflow-auto"
            onSubmit={methods.handleSubmit(onSubmit)}
            noValidate
          >
            <InputField {...args} />
          </form>
        </FormProvider>
      </div>
    );
  },
  args: {
    label: "Default",
    name: "string",
    errorMessage: "",
    tooltip: "default tooltip",
    isDisabled: false,
    isRequired: false,
    type: "text",
  },
};

export const Disabled: Story = {
  render: (args) => {
    const methods = useForm<FormFields>();
    const onSubmit = (data: FormFields) => {};

    return (
      <div className="w-screen h-screen p-5 gap-y-[10px]">
        <FormProvider {...methods}>
          <form
            className="overflow-auto"
            onSubmit={methods.handleSubmit(onSubmit)}
            noValidate
          >
            <InputField {...args} />
          </form>
        </FormProvider>
      </div>
    );
  },
  args: {
    label: "Geef een getal in",
    name: "number",
    errorMessage: "",
    isDisabled: true,
    isRequired: false,
    type: "number",
  },
};

export const DefaultUpload: Story = {
  render: (args) => {
    const methods = useForm<FormFields>();
    const onSubmit = (data: FormFields) => {};

    return (
      <div className="w-screen h-screen p-5 gap-y-[10px]">
        <FormProvider {...methods}>
          <form
            className="overflow-auto"
            onSubmit={methods.handleSubmit(onSubmit)}
            noValidate
          >
            <InputField {...args} />
          </form>
        </FormProvider>
      </div>
    );
  },
  args: {
    label: "Upload",
    name: "string",
    errorMessage: "",
    isDisabled: false,
    isRequired: false,
    type: "file",
  },
};

export const DefaultNumber: Story = {
  render: (args) => {
    const methods = useForm<FormFields>();
    const onSubmit = (data: FormFields) => {};

    return (
      <div className="w-screen h-screen p-5 gap-y-[10px]">
        <FormProvider {...methods}>
          <form
            className="overflow-auto"
            onSubmit={methods.handleSubmit(onSubmit)}
            noValidate
          >
            <InputField {...args} />
          </form>
        </FormProvider>
      </div>
    );
  },
  args: {
    label: "Geef een getal in",
    name: "number",
    errorMessage: "",
    isDisabled: false,
    isRequired: false,
    type: "number",
  },
};

export const DefaultEmail: Story = {
  render: (args) => {
    const methods = useForm<FormFields>();
    const onSubmit = (data: FormFields) => {};

    return (
      <div className="w-screen h-screen p-5 gap-y-[10px]">
        <FormProvider {...methods}>
          <form
            className="overflow-auto"
            onSubmit={methods.handleSubmit(onSubmit)}
            noValidate
          >
            <InputField {...args} />
          </form>
        </FormProvider>
      </div>
    );
  },
  args: {
    label: "E-mail",
    name: "string",
    errorMessage: "",
    isDisabled: false,
    isRequired: false,
    type: "email",
  },
};

export const DefaultDate: Story = {
  render: (args) => {
    const methods = useForm<FormFields>();
    const onSubmit = (data: FormFields) => {};

    return (
      <div className="w-screen h-screen p-5 gap-y-[10px]">
        <FormProvider {...methods}>
          <form
            className="overflow-auto"
            onSubmit={methods.handleSubmit(onSubmit)}
            noValidate
          >
            <InputField {...args} />
          </form>
        </FormProvider>
      </div>
    );
  },
  args: {
    label: "Start datum",
    name: "string",
    errorMessage: "",
    isDisabled: false,
    isRequired: false,
    type: "date",
  },
};

export const DefaultTextArea: Story = {
  render: (args) => {
    const methods = useForm<FormFields>();
    const onSubmit = (data: FormFields) => {};

    return (
      <div className="w-screen h-screen p-5 gap-y-[10px]">
        <FormProvider {...methods}>
          <form
            className="overflow-auto"
            onSubmit={methods.handleSubmit(onSubmit)}
            noValidate
          >
            <InputField {...args} />
          </form>
        </FormProvider>
      </div>
    );
  },
  args: {
    label: "Textarea",
    name: "string",
    errorMessage: "",
    isDisabled: false,
    isRequired: false,
    type: "textarea",
  },
};

export const DefaultRadioButton: Story = {
  render: (args) => {
    const methods = useForm<FormFields>();
    const onSubmit = (data: FormFields) => {};

    const onUpdateValue = (value: string) => {
      console.log(value, "value active of radio");
    };

    const radioData = [
      {
        name: "radio",
        value: "option-1",
        description:
          "You have an official confirmation of patent agency that the request is still in process",
        answer:'den haag',
        label:'label-1',
      },
    ];

    return (
      <div className="w-screen h-screen p-5 gap-y-[10px] flex justify-start items-center">
        <FormProvider {...methods}>
          <form
            className="overflow-auto flex justify-center items-center w-full flex-col gap-y-4"
            onSubmit={methods.handleSubmit(onSubmit)}
            noValidate
          >
              {radioData?.map((radio, id) => {
                return (
                  <InputField
                      key={id}
                      type="radio"
                      name={radio.name}
                      label={radio.label}
                      isRequired={true}
                      tooltip={'tooltip broh'}
                      errorMessage={'error message is here'}
                      answers={[{ answer: 'bayu', value: 'bayu' } as AnswerValue, { answer: 'saya', value: 'saya' } as AnswerValue, { answer: 'laper', value: 'laper' } as AnswerValue]}
                      description={radio.description}
                    />
                );
              })}
          </form>
        </FormProvider>
      </div>
    );
  },
  args: {
    label: "Radio Button Label",
    name: "string",
    errorMessage: "",
    isDisabled: false,
    isRequired: false,
    type: "radio",
  },
};


export const DefaultCheckbox: Story = {
  render: (args) => {
    const methods = useForm<FormFields>();
    const onSubmit = (data: FormFields) => {};

    const checkboxObj = [
      {
        name: "Retail price of the product",
        selected: false
      },
      {
        name: "Online price of the product",
        selected: false
      },
      {
        name: "fake price of the product",
        selected: false
      },
    ];

    const [checkboxData, setCheckboxData] = useState(checkboxObj);
    const onUpdateCheckbox=(name: string)=>{
      const updatedData = checkboxData.map((val,id)=>{
        console.log(val,'valueee updated', name)
        if(val.name === name){
          return {
            name: val.name,
            selected: true
          }
        }else {
          return {
            name: val.name,
            selected: val.selected
          }
        }
      })
      console.log(updatedData,'updated data');
      setCheckboxData(updatedData);
    }

    return (
      <div className="w-screen h-screen p-5 gap-y-[10px] flex justify-center items-center">
        <FormProvider {...methods}>
          <form
            className=""
            onSubmit={methods.handleSubmit(onSubmit)}
            noValidate
          >
            <InputField {...args}>
              {checkboxData?.map((checkbox, id) => {
                return (
                  <Checkbox
                    onUpdateValue={()=>onUpdateCheckbox(checkbox.name)}
                    value={checkbox.name}
                    selected={checkbox.selected}
                  />
                );
              })}
            </InputField>
          </form>
        </FormProvider>
      </div>
    );
  },
  args: {
    label: "Radio Button Label",
    name: "string",
    errorMessage: "",
    isDisabled: false,
    isRequired: false,
    type: "checkbox",
  },
};


export const DefaultSingleCheckbox: Story = {
  render: (args) => {
    const methods = useForm<FormFields>();
    const onSubmit = (data: FormFields) => {};

    // const checkboxObj = [
    //   {
    //     name: "Retail price of the product",
    //     selected: false
    //   },
    //   {
    //     name: "Online price of the product",
    //     selected: false
    //   },
    //   {
    //     name: "fake price of the product",
    //     selected: false
    //   },
    // ];

    // const [checkboxData, setCheckboxData] = useState(checkboxObj);
    // const onUpdateCheckbox=(name: string)=>{
    //   const updatedData = checkboxData.map((val,id)=>{
    //     console.log(val,'valueee updated', name)
    //     if(val.name === name){
    //       return {
    //         name: val.name,
    //         selected: true
    //       }
    //     }else {
    //       return {
    //         name: val.name,
    //         selected: val.selected
    //       }
    //     }
    //   })
    //   console.log(updatedData,'updated data');
    //   setCheckboxData(updatedData);
    // }

    let Answers = [
      {
        Answer: 'This product is intellectual property and/or copyright of the undersigned',
        url: '/blueprint/data/awards-forms/awards-form/design/intellectual-property-and-or-copyright/intellectual-property-and-or-copyright/this-product-is-intellectual-property-and-or-copyright-of-the-undersigned',
        type: 'AnswerOption',
        name: 'answerOption-31'
      },
    ]
    return (
      <div className="w-screen h-screen p-5 gap-y-[10px] flex justify-center items-center">
        <FormProvider {...methods}>
          <form
            className=""
            onSubmit={methods.handleSubmit(onSubmit)}
            noValidate
          >
            <InputField {...args}    
              answers={Answers.map(
                (ans) => ({ answer: ans.Answer, value: ans.name }) // Ensure this line is correctly formatted
              )}>
            </InputField>
          </form>
        </FormProvider>
      </div>
    );
  },
  args: {
    label: "Radio Button Label",
    name: "string",
    errorMessage: "",
    isDisabled: false,
    isRequired: false,
    type: "checkbox",
    isWithoutLabel: true
  },
};

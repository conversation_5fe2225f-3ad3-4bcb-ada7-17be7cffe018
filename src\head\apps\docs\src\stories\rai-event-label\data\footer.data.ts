import { RAIFooterComponent } from "rai-event-label";

export const LabelFooterData: Partial<RAIFooterComponent.FooterProps> = {
  params: {
    name: "Footer",
    componentName: "Footer",
    tag: "h1",
  },
  rendering: {
    uid: "{00000000-0000-0000-0000-000000000000}",
    componentName: "Footer",
    dataSource: "{00000000-0000-0000-0000-000000000000}",
  },
  fields: {
    data: {
      site: {
        root: {
          items: [
            {
              name: "Structure",
              structure: {
                footer: [
                  {
                    logos: {
                      partners: [
                        {
                          logogroups: {
                            groups: [
                              {
                                displayName: "Organised By",
                                logogroup: {
                                  logo: [
                                    {
                                      displayName: "RAI",
                                      title: {
                                        value: "RAI",
                                      },
                                      link: {
                                        jsonValue: {
                                          value: {
                                            href: "#",
                                          },
                                        },
                                      },
                                      image: {
                                        jsonValue: {
                                          value: {
                                            src: "https://assets-prd.raicore.com/-/media/project/rai-amsterdam/shared/master/rai-amsterdam.png?h=82&iar=0&mh=85&mw=85&w=83&rev=a9cc0a35055140f28d7ae3d3fea19d35&hash=517E880D471A39F4087F53C4A9B2D6BE",
                                          },
                                        },
                                      },
                                    },
                                  ],
                                },
                              },
                              {
                                displayName: "Supported By",
                                logogroup: {
                                  logo: [
                                    {
                                      displayName: "RAI",
                                      title: {
                                        value: "RAI",
                                      },
                                      link: {
                                        jsonValue: {
                                          value: {
                                            href: "#",
                                          },
                                        },
                                      },
                                      image: {
                                        jsonValue: {
                                          value: {
                                            src: "https://assets-prd.raicore.com/-/media/project/rai-amsterdam/shared/master/rai-amsterdam.png?h=82&iar=0&mh=85&mw=85&w=83&rev=a9cc0a35055140f28d7ae3d3fea19d35&hash=517E880D471A39F4087F53C4A9B2D6BE",
                                          },
                                        },
                                      },
                                    },
                                  ],
                                },
                              },
                            ],
                          },
                        },
                      ],
                    },
                    footercontent: {
                      contentblocks: [
                        {
                          name: "Content Blocks",
                          contentblockgroup: {
                            contentblock: [
                              {
                                name: "About us",
                                title: {
                                  value: "About us",
                                },
                                content: {
                                  value:
                                    "GreenTech is the global meeting place for all professionals involved in horticulture technology. GreenTech focuses on the early stages of the horticulture chain and production issues relevant to growers. GreenTech offers two yearly exhibitions.",
                                },
                              },
                              {
                                name: "Newsletter",
                                title: {
                                  value: "Newsletter",
                                },
                                content: {
                                  value:
                                    'Receive the best newsletter on horticulture - straight to your inbox! <br/> <br/>  <a class="btn-footer-solid" href="#">Subscribe</a>',
                                },
                              },
                              {
                                name: "Contact us",
                                title: {
                                  value: "Contact us",
                                },
                                content: {
                                  value:
                                    `<div class="mt-4 text-white break-words"><p>$(beursnaam)<br>
                                    Europaplein 24<br>
                                    1078 GZ Amsterdam<br>
                                    P.O. Box 77777<br>
                                    1070 MS Amsterdam<br>
                                    The Netherlands<br>
                                    <br>
                                    <em class="fa-solid fa-circle-info" aria-hidden="true"></em>&nbsp;&nbsp; <a href="http://www.rai.nl">Contact</a><br>
                                    <em class="fa fa-phone" aria-hidden="true"></em>&nbsp;&nbsp; <a href="tel:+31205491212">+31 (0)20 549 12 12</a></p>
                                    <p><em class="fa fa-map-marker" aria-hidden="true"></em>&nbsp;&nbsp; <a href="http://www.rai.nl" target="_blank">Get directions</a><br>
                                    <em class="fa fa-envelope-o" aria-hidden="true"></em>&nbsp;&nbsp; <EMAIL><br>
                                    <em class="fa fa-globe" aria-hidden="true"></em>&nbsp;&nbsp; <a href="http://www.rai.nl">Contact</a></p></div>
                                    `,
                                },
                              },
                            ],
                          },
                        },
                      ],
                    },
                    navigation: {
                      navigationgroups: [
                        {
                          name: "Footer Navigation",
                          navigationitem: {
                            results: [
                              {
                                name: "Privacy statement",
                                link: {
                                  jsonValue: {
                                    value: {
                                      href: "",
                                    },
                                  },
                                },
                                icon: {
                                  value: "",
                                },
                              },
                              {
                                name: "Disclaimer",
                                link: {
                                  jsonValue: {
                                    value: {
                                      href: "",
                                    },
                                  },
                                },
                                icon: {
                                  value: "",
                                },
                              },
                              {
                                name: "Cookie settings",
                                link: {
                                  jsonValue: {
                                    value: {
                                      href: "",
                                    },
                                  },
                                },
                                icon: {
                                  value: "",
                                },
                              },
                              {
                                name: "Sitemap",
                                link: {
                                  jsonValue: {
                                    value: {
                                      href: "",
                                    },
                                  },
                                },
                                icon: {
                                  value: "",
                                },
                              },
                            ],
                          },
                        },
                        {
                          name: "Other Services",
                          navigationitem: {
                            results: [
                              {
                                name: "Android",
                                link: {
                                  jsonValue: {
                                    value: {
                                      href: "",
                                    },
                                  },
                                },
                                icon: {
                                  value: "",
                                },
                              },
                              {
                                name: "Apple",
                                link: {
                                  jsonValue: {
                                    value: {
                                      href: "",
                                    },
                                  },
                                },
                                icon: {
                                  value: "",
                                },
                              },
                            ],
                          },
                        },
                      ],
                    },
                  },
                ],
              },
            },
          ],
        },
        settings: {
          jsssettings: [
            {
              socialplatformfolder: {
                socialplatforms: [
                  {
                    name: "Social Platforms",
                    socialplatformgroup: {
                      socialplatform: [
                        {
                          name: "Facebook",
                          follow: {
                            jsonValue: {
                              value: {
                                href: "https://www.facebook.com/AmsterdamRAI/",
                                linktype: "external",
                                url: "https://www.facebook.com/AmsterdamRAI/",
                                anchor: "data",
                                target: "data",
                              },
                            },
                          },
                          share: {
                            jsonValue: {
                              value: {
                                href: "https://www.facebook.com/sharer.php?u=",
                                linktype: "external",
                                url: "https://www.facebook.com/sharer.php?u=",
                                anchor: "data",
                                target: "data",
                              },
                            },
                          },
                          icon: {
                            value: "fa-brands fa-facebook-f",
                          },
                        },
                        {
                          name: "Linked in",
                          follow: {
                            jsonValue: {
                              value: {
                                href: "https://www.facebook.com/AmsterdamRAI/",
                                linktype: "external",
                                url: "https://www.facebook.com/AmsterdamRAI/",
                                anchor: "data",
                                target: "data",
                              },
                            },
                          },
                          share: {
                            jsonValue: {
                              value: {
                                href: "https://www.facebook.com/sharer.php?u=",
                                linktype: "external",
                                url: "https://www.facebook.com/sharer.php?u=",
                                anchor: "data",
                                target: "data",
                              },
                            },
                          },
                          icon: {
                            value: "fa-brands fa-linkedin-in",
                          },
                        },
                        {
                          name: "Instagram",
                          follow: {
                            jsonValue: {
                              value: {
                                href: "https://www.facebook.com/AmsterdamRAI/",
                                linktype: "external",
                                url: "https://www.facebook.com/AmsterdamRAI/",
                                anchor: "data",
                                target: "data",
                              },
                            },
                          },
                          share: {
                            jsonValue: {
                              value: {
                                href: "https://www.facebook.com/sharer.php?u=",
                                linktype: "external",
                                url: "https://www.facebook.com/sharer.php?u=",
                                anchor: "data",
                                target: "data",
                              },
                            },
                          },
                          icon: {
                            value: "fa-brands fa-instagram",
                          },
                        },
                        {
                          name: "Youtube",
                          follow: {
                            jsonValue: {
                              value: {
                                href: "https://www.facebook.com/AmsterdamRAI/",
                                linktype: "external",
                                url: "https://www.facebook.com/AmsterdamRAI/",
                                anchor: "data",
                                target: "data",
                              },
                            },
                          },
                          share: {
                            jsonValue: {
                              value: {
                                href: "https://www.facebook.com/sharer.php?u=",
                                linktype: "external",
                                url: "https://www.facebook.com/sharer.php?u=",
                                anchor: "data",
                                target: "data",
                              },
                            },
                          },
                          icon: {
                            value: "fa-brands fa-youtube",
                          },
                        },
                      ],
                    },
                  },
                ],
              },
            },
          ],
        },
      },
    },
  },
};

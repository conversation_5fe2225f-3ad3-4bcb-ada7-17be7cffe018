import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { WithSitecoreContextDecorator } from "@uxbee/storybook-mock-sc";
import { SessionDetailInternal } from "programs";
import { SpeakerMockData } from "../data/speaker-mock-data";
import { SessionMockData } from "../data/session-mock-data";

const meta: Meta<typeof SessionDetailInternal> = {
    title: "RAI/Programs/SessionDetail",
    component: SessionDetailInternal,
    tags: ["autodocs", "rai", "ep"],
    argTypes: {},
    decorators: [
        (Story, context) =>
            WithSitecoreContextDecorator(
                () => (
                    <div className="min-h-screen bg-[#fafafa]">
                        <Story />
                    </div>
                ),
                context,
                SessionDetailInternal,
                false
            ),
    ],
};

export default meta;

type Story = StoryObj<typeof SessionDetailInternal>;

export const Default: Story = {
    args: {
        language: "en",
        session: SessionMockData[0],
        speakers: [
            {
                speaker: SpeakerMockData[0],
                programs: SessionMockData.slice(1, 2)
            },
            {
                speaker: SpeakerMockData[1], 
                programs: [],
            },
            {
                speaker: SpeakerMockData[2],
                programs: SessionMockData.slice(4, 2),
            },
            {
                speaker: SpeakerMockData[3],
                programs: []
            },
            {
                speaker: SpeakerMockData[4], 
                programs: []
            },
            {
                speaker: SpeakerMockData[5],
                programs: []
            },
        ],
        getParentPageUrl: () => "/programs",
        buildSessionDetailUrl: () => '/session_detail',
        buildSpeakerDetailUrl: () => '/speaker_detail',
        buildCalendarIcsFileUrl: () => '/session_ics',
        texts: {
            back: 'Back',
            speakers: 'Speakers',
            hideSessions: 'Hide Sessions',
            showSessions: 'Show Sessions',
        },
        timezoneOffset: 0
    },
};

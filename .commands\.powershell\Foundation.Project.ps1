function Copy-Default-Jss-Workspace-Plugins() {
    param(
        [ValidateNotNull()]
        [string]
        $TurboPath,
        [ValidateNotNull()]
        [string]
        $TemplatesRootPath,
        [object]
        $Config,
        [string]
        $WorkspaceName = "web"
    )

    $AppsPath = Join-Path $TurboPath "/apps"
    $TargetWorkspacePath = Join-Path $AppsPath $WorkspaceName

    $Config.settings.jssAppPlugins | ForEach-Object {
        $Source = Join-Path $TemplatesRootPath $_.source
        $Target = Join-Path $TargetWorkspacePath $_.target
        Write-Host "Copying JSS workspace plugin files: " $_.name
        # Copy template files as is
        Copy-Files -Source $Source -Target $Target

        if($_.files) {
            $_.files | ForEach-Object {
                $CopiedFilePath = Join-Path $TargetWorkspacePath $_.path

                # If replacement values are present in the config we should replace tokens
                if($_.replacementValues) {
                    $FileContent = Get-Content $CopiedFilePath
                    $_.replacementValues | ForEach-Object {
                        $Key = '${' + $_.key + '}'
                        $FileContent = $FileContent.replace($Key, $_.value)
                    }

                    Set-Content -Path $CopiedFilePath -Value $FileContent
                }

                # Rename file if a new name is defined
                if($_.newName) {                    
                    Rename-Item -Path $CopiedFilePath -NewName $_.newName                    
                }
            }
        }
    }
}

function Invoke-Sitecore-Headless-Modules-Installation() {
    param(
        [ValidateNotNullOrEmpty()]
        [string]
        $TurboPath,
        [ValidateNotNull()]
        [object]
        $Config
    )

    $Apps = Join-Path $TurboPath "apps"
    Push-Location $TurboPath
    $Config.settings.sitecoreHeadlessModules.appsToInstall | ForEach-Object {
        Write-Host "Installing 'add-uxbee-sitecore-headless-modules' NPX package on top of workspace: " $_        
        $WorkspacePath = Join-Path $Apps $_
        Write-Host "Workspace path is " $WorkspacePath
        npx -y @uxbee/add-uxbee-sitecore-headless-modules@latest --install-all-modules --workspace $_ --workspace-path $WorkspacePath
    }
    Pop-Location
}
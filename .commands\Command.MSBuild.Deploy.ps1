Push-Location $PSScriptRoot
Push-Location -Path ..
$ContextDir = Get-Location
Pop-Location

Push-Location $ContextDir
# Publish all projects in the solution using their "DockerDeploy" publish profiles
$solutionPath = Get-ChildItem *.sln -Name

# Need to check if VSSetup is installed, and if not then install it
if (-not(Get-Module -ListAvailable -Name VSSetup)) {
    Write-Host "Installing VSSetup module"
    Install-Module VSSetup -Scope CurrentUser -Force
} 
 
# Use Get-VSSetupInstance to get local path to VS installation
$instance = Get-VSSetupInstance -All | Select-VSSetupInstance -Require 'Microsoft.Component.MSBuild' -Latest
$installDir = $instance.installationPath

# This is specific for VS2019. Using future VS versions may require changing this if the relative path to MSBuill.exe changes
$msBuild = $installDir + '\MSBuild\Current\Bin\MSBuild.exe' # VS2019

Write-Host "--- Publishing all projects ---"
#Restoring nuget packages
& $msBuild $solutionPath -t:restore -p:RestorePackagesConfig=true
# The actual command - publishing using the DockerDeploy profile
& $msBuild $solutionPath /t:build /p:DeployOnBuild=true /p:PublishProfile=Local;

Pop-Location

Pop-Location
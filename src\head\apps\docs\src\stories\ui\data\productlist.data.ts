import { NewProduct } from "company-profile-data";

export const ProductListData = [
  {
    id: '1',
    image: 'https://placehold.co/600x400',
    title: 'THOA Portable Wireless DIM',
    content: 'Comfort and  entertainment afloat lorem ipsum bubur sumsum lorem ipsum bubur susum lorem ipsum bubur sumsum lorem ipsum bubur sumsum',
    badgeTitle: 'Submitted for Award',
    status: 'success',
    awardLink: '/registerlink',
    editProductLink: '/editlink',
    registrationStatus: 'new',
  },
  {
    id: '2',
    image: 'https://placehold.co/600x400',
    title: 'IPhone portable',
    content: 'Comfort and  entertainment afloat',
    badgeTitle: 'Submitted for Award',
    status: 'success',
    awardLink: '/registerlink',
    editProductLink: '/editlink',
    registrationStatus: 'submitted',
  },
  {
    id: '3',
    image: 'https://placehold.co/600x400',
    title: 'Head portable',
    content: 'Comfort and  entertainment afloat',
    badgeTitle: 'Submitted for Award',
    status: 'neutral',
    awardLink: '/registerlink',
    editProductLink: '/editlink',
    registrationStatus: 'draft',
  },
  {
    id: '4',
    image: 'https://placehold.co/600x400',
    title: 'THOA Portable Wireless DIM',
    content: 'Comfort and  entertainment afloat',
    badgeTitle: 'Submitted for Award',
    status: 'success',
    awardLink: '/registerlink',
    editProductLink: '/editlink',
    registrationStatus: 'submitted',
  },
  {
    id: '5',
    image: 'https://placehold.co/600x400',
    title: 'IPhone portable',
    content: 'Comfort and  entertainment afloat',
    badgeTitle: 'Submitted for Award',
    status: 'success',
    awardLink: '/registerlink',
    editProductLink: '/editlink',
    registrationStatus: 'new',
  },
  {
    id: '6',
    image: 'https://placehold.co/600x400',
    title: 'Head portable',
    content: 'Comfort and  entertainment afloat',
    badgeTitle: 'Submitted for Award',
    status: 'neutral',
    awardLink: '/registerlink',
    editProductLink: '/editlink',
    registrationStatus: 'new',
  },
  {
    id: '7',
    image: 'https://placehold.co/600x400',
    title: 'THOA Portable Wireless DIM',
    content: 'Comfort and  entertainment afloat',
    badgeTitle: 'Submitted for Award',
    status: 'success',
    awardLink: '/registerlink',
    editProductLink: '/editlink',
    registrationStatus: 'new',
  },
  {
    id: '8',
    image: 'https://placehold.co/600x400',
    title: 'IPhone portable',
    content: 'Comfort and  entertainment afloat',
    badgeTitle: 'Submitted for Award',
    status: 'success',
    awardLink: '/registerlink',
    editProductLink: '/editlink',
    registrationStatus: 'new',
  },
  {
    id: '9',
    image: 'https://placehold.co/600x400',
    title: 'Head portable',
    content: 'Comfort and  entertainment afloat',
    badgeTitle: 'Submitted for Award',
    status: 'neutral',
    awardLink: '/registerlink',
    editProductLink: '/editlink',
    registrationStatus: 'draft',
  },
  {
    id: '10',
    image: 'https://placehold.co/600x400',
    title: 'Head portable',
    content: 'Comfort and  entertainment afloat',
    badgeTitle: 'Submitted for Award',
    status: 'neutral',
    awardLink: '/registerlink',
    editProductLink: '/editlink',
    registrationStatus: 'new',
  },
] as NewProduct[];

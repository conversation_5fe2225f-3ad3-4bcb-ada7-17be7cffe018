﻿import type { StorybookConfig } from "@storybook/nextjs";
import path, { dirname, join } from "path";
import webpack from "webpack";

const config: StorybookConfig = {
  stories: ["../src/**/*.mdx", "../src/**/*.stories.@(js|jsx|mjs|ts|tsx)"],
  staticDirs: ["../public"],
  addons: [
    getAbsolutePath("@storybook/addon-links"),
    getAbsolutePath("@storybook/addon-essentials"),
    getAbsolutePath("@storybook/addon-actions"),
    getAbsolutePath("@storybook/addon-a11y"),
    {
      name: "@storybook/addon-themes",
      options: {
        default: "rai",
      },
    },
    getAbsolutePath("@storybook/addon-viewport"),
    {
      name: "@storybook/addon-styling",
      options: {
        postCss: true,
      },
    },
    getAbsolutePath("@storybook/addon-interactions"),
  ],
  framework: {
    name: getAbsolutePath("@storybook/nextjs"),
    options: {},
  },
  docs: {
    autodocs: "tag",
  },
  webpackFinal: async (config) => {
    // Ensure plugin array exists
    if (!config.plugins) config.plugins = [];

    // Add IgnorePlugin for applicationinsights
    config.plugins.push(
      new webpack.IgnorePlugin({
        resourceRegExp: /^applicationinsights$/,
      })
    );

    // Add rule for Markdown (.md) files
    config.module?.rules?.push({
      test: /\.md$/,
      use: "raw-loader",
      type: 'javascript/auto',
    });

    return config;
  },
};

export default config;

// Helper function to get absolute path of a Storybook addon
function getAbsolutePath(value: string): string {
  return dirname(require.resolve(join(value, "package.json")));
}

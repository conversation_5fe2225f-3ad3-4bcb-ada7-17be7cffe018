Push-Location $PSScriptRoot
# Define the script context
Push-Location -Path ..
$ContextDir = Get-Location
Pop-Location

Push-Location $ContextDir

Write-Host "=============== Closing down Develop Environment =====================" -ForegroundColor Green
Write-Host "Down containers..." -ForegroundColor Yellow
try {
  docker compose down
  
  if ($LASTEXITCODE -ne 0) {
    Write-Error "Container down failed, see errors above."
  }
  
  Write-Host "- Kill all running nginx processes." -ForegroundColor Yellow
  .\execute.ps1 kill.nginx

  Write-Host "- Kill all running turbo processes." -ForegroundColor Yellow
  .\execute.ps1 kill.turbo
  
}
finally {
}
Pop-Location
Pop-Location

export const ContentBlockDefaultData1 = {
  params: {
    name: "ContentBlock",
    componentName: "ContentBlock",
    tag: "h1",
    alignment: "left",
  },
  rendering: {
    uid: "{00000000-0000-0000-0000-000000000000}",
    componentName: "ContentBlock",
    dataSource: "{00000000-0000-0000-0000-000000000000}",
  },
  fields: {
    heading: {
      value: "de perfecte partner voor jouw Sitecore-project",
    },
    content: {
      value:
        "Kies je voor Sitecore dan is uxbee de aangewezen samenwerkingspartner voor een geslaagde ontwikkeling en implementatie. In tegenstelling tot andere technologiepartners, is er binnen ons team veel senioriteit op het gebied van informatietechnologie én marketing. Tel daar onze ervaring in complexe implementaties bij op, en je beschikt over alle kennis en vaardigheden die nodig zijn voor een succesvol Sitecore project.",
    },
  },
};

export const ContentBlockDefaultData2 = {
  params: {
    name: "ContentBlock",
    componentName: "ContentBlock",
    tag: "h3",
    alignment: "center",
  },
  rendering: {
    uid: "{00000000-0000-0000-0000-000000000000}",
    componentName: "ContentBlock",
    dataSource: "{00000000-0000-0000-0000-000000000000}",
  },
  fields: {
    heading: {
      value: "Klaar voor jouw digitale revolutie?",
    },
    content: {
      value:
        "Uxbee is jouw partner in e-commerce en marketingtechnologie. Jij weet wat jouw doelstellingen zijn. Wij weten deze te vertalen naar een platform waarmee jij digitaal volwassen wordt. Samen komen we tot de ideale oplossing voor jouw organisatie, zo simpel als 1 + 1 = 2.",
    },
  },
};

export const ContentBlockDefaultDataNoDatasource = {
  params: {
    name: "ContentBlock",
    componentName: "ContentBlock",
    tag: "h1",
    alignment: "left",
  },
  rendering: {
    uid: "{00000000-0000-0000-0000-000000000000}",
    componentName: "ContentBlock",
  },
};

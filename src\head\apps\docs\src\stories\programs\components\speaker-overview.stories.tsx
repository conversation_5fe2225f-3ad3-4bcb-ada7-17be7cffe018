import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { WithSitecoreContextDecorator } from "@uxbee/storybook-mock-sc";
import { SpeakersOverviewInternal } from "programs";
import { SpeakerMockData } from "../data/speaker-mock-data";

const meta: Meta<typeof SpeakersOverviewInternal> = {
    title: "RAI/Programs/SpeakersOverview",
    component: SpeakersOverviewInternal,
    tags: ["autodocs", "rai", "ep"],
    argTypes: {},
    decorators: [
        (Story, context) =>
            WithSitecoreContextDecorator(
                () => (
                    <div className="min-h-screen bg-[#fafafa]">
                        <Story />
                    </div>
                ),
                context,
                SpeakersOverviewInternal,
                false
            ),
    ],
};

export default meta;

type Story = StoryObj<typeof SpeakersOverviewInternal>;

export const Default: Story = {
    args: {
        speakers: SpeakerMockData.slice(0, 6),
        facets: [
            {
                name: "location",
                label: "Location",
                value: [
                    {
                        count: 3,
                        id: "facet_value_1",
                        text: "Intertraffic Summit Theatre 1"
                    },
                    {
                        count: 3,
                        id: "facet_value_2",
                        text: "Intertraffic Summit Theatre 3"
                    }
                ]
            },
        ],
        hasNext: false,
        selectedFacets: [ "facet_value_2" ],
        onClearAllFilter: () => {},
        onUpdateSearchText: (searchText) => {},
        onUpdateFacet: (facetData: { checked: boolean, value: string }) => {},
        updateSearchResults: () => {},
        buildSpeakerDetailsUrl: () => '/speaker_details',
        texts: {
            noSpeakersFound: "No speakers found.",
            filter: "Filter",
            clearAll: "CLEAR ALL",
            applyFilter: "APPLY FILTER",
            openFilter: "OPEN FILTER",
            loading: "LOADING..."
        },
    },
};

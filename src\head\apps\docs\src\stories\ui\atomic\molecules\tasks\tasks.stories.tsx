import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { Tasks } from "ui";
import { tasksData } from "../../../data/tasks.data";
const meta: Meta<typeof Tasks> = {
  title: "ui/Atomic/Molecules/Tasks/Tasks",
  component: Tasks,
  tags: ["autodocs", "ui", "molecules", "tasks"],
  parameters: {
  },
  argTypes: {},
};
export default meta;
type Story = StoryObj<typeof Tasks>;
export const Schema: Story = {
  render: (args) => {
    return (
        <div className="h-[28rem] px-4 lg:px-36 py-6 bg-neutral-50">
          <Tasks {...args} />
        </div>
    );
  },
  args: {
    tasks: tasksData,
    language: "en",
  }
};
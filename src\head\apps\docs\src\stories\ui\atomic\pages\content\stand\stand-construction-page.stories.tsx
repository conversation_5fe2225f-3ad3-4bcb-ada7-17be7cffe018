import type { <PERSON>a, StoryObj } from "@storybook/react";
import { StandConstructionPage } from "ui";
const meta: Meta<typeof StandConstructionPage> = {
  title: "ui/Atomic/Pages/Content/Stand/StandConstruction/StandConstructionPage",
  component: StandConstructionPage,
  tags: ["autodocs", "ui", "molecules", "home-page"],
  parameters: {},
  argTypes: {},
};
export default meta;
type Story = StoryObj<typeof StandConstructionPage>;
export const Schema: Story = {
  render: (args) => {
    return (
      <div className="h-[40rem]">
        <StandConstructionPage {...args} />
      </div>
    );
  },
  args: {},
};

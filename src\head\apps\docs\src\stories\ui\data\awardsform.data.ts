import { AwardsFormStructure, FormStep, IntroAwardData, RadiobuttonQuestion } from "ui";
import { Questionnaire } from 'company-profile-data';

const step1: FormStep = {
  "StepName": "Category",
  "StepTitle": "Select the category in which your entry belongs",
  "StepDescription": "Are you still in doubt which category to choose?",
  "children": [
      {
          "CanSelectMultipleItems": false,
          "Question": "Which category you want to participate in",
          "children": [
              {
                  "Answer": "Comfort and Entertainment afloat",
                  "url": "/blueprint/data/awards/test-award/award-form/category/which-category-you-want-to-participate-in/comfort-and-entertainment-afloat",
                  "type": "CardQuestionOption",
                  "name": "stepid"
              },
              {
                  "Answer": "Navigation and communication",
                  "url": "/blueprint/data/awards/test-award/award-form/category/which-category-you-want-to-participate-in/navigation-and-communication",
                  "type": "CardQuestionOption",
                  "name": "stepid2"
              },
              {
                  "Answer": "Personal equipment",
                  "url": "/blueprint/data/awards/test-award/award-form/category/which-category-you-want-to-participate-in/personal-equipment",
                  "type": "CardQuestionOption",
                  "name": "stepid3"
              }
          ],
          "url": "/blueprint/data/awards/test-award/award-form/category/which-category-you-want-to-participate-in",
          "type": "CardQuestion",
          "name": "stepid",
          "order": 1,
          "answer": "Comfort and Entertainment afloat",
          "Description": "",
          "Tooltip": "",
          "isRequired": true,
          "HideLabel":false
      }
  ],
  "url": "/blueprint/data/awards/test-award/award-form/category",
  "type": "FormStep",
  "name": "stepid",
  "StepNumber": 1,
  "IsSummary": false
}
const step2: FormStep = {
  "StepName": "General",
  "StepTitle": "General",
  "StepDescription": "Please fill in the manufacturer and contact details.",
  "children": [
      {
          "Title": "Manufacturer",
          "Description": "Details from manufacturer.",
          "children": [
              {
                  "Question": "Name of original manufacturer",
                  "Tooltip": "",
                  "isRequired": true,
                  "maxLength": 50,
                  "IsMultiline": false,
                  "isEmail": false,
                  "onlyNumbers": false,
                  "isUrl": false,
                  "isVideo": false,
                  "url": "/blueprint/data/awards/test-award/award-form/general/manufacturer/name-of-original-manufacturer",
                  "type": "TextQuestion",
                  "name": "stepid2",
                  "Description": "",
                  "order": 2,
                  "answer": "Bayu van der saar",
                  "rows": 3,
                  "HideLabel":false
              },
              {
                  "Question": "Origin country for manufacturer",
                  "Tooltip": "",
                  "isRequired": true,
                  "IsMultiline": false,
                  "isEmail": false,
                  "onlyNumbers": false,
                  "isUrl": false,
                  "isVideo": false,
                  "url": "/blueprint/data/awards/test-award/award-form/general/manufacturer/origin-country-for-manufacturer",
                  "type": "TextQuestion",
                  "name": "stepid2",
                  "Description": "",
                  "order": 3,
                  "answer": "Netherland",
                  "rows": 3,
                  "HideLabel":false
              }
          ],
          "url": "/blueprint/data/awards/test-award/award-form/general/manufacturer",
          "type": "FormStepSection",
          "name": "stepid2",
          "CanSelectMultipleItems": false
      },
      {
          "Title": "Contact",
          "Description": "Contact details.",
          "children": [
              {
                  "Question": "Name contact",
                  "Tooltip": "lorem ipsum sum sum bubur sumsum in the 100k maybe lorem ipsum sum sum bubur sumsum in netherland 100k maybe lorem ipsum sum sum bubur sumsum in netherland 100k maybe",
                  "isRequired": true,
                  "maxLength": 50,
                  "IsMultiline": false,
                  "isEmail": false,
                  "onlyNumbers": false,
                  "isUrl": false,
                  "isVideo": false,
                  "url": "/blueprint/data/awards/test-award/award-form/general/contact/name-contact",
                  "type": "TextQuestion",
                  "name": "e698b441-791e-4da9-96a9-29590c1ce0b3",
                  "Description": "",
                  "order": 4,
                  "answer": "Bryan",
                  "rows": 3,
                  "HideLabel":false
              },
            //   {
            //       "Question": "Video",
            //       "Tooltip": "",
            //       "isRequired": false,
            //       "IsMultiline": false,
            //       "isEmail": false,
            //       "isUrl": false,
            //       "isVideo": true,
            //       "url": "/blueprint/data/awards/test-award/award-form/general/contact/video",
            //       "type": "TextQuestion",
            //       "name": "33c62179-7376-4b90-9075-0927c907df20",
            //       "Description": "",
            //       "order": 5,
            //       "answer": "",
            //       "rows": 3,
            //       "HideLabel":false
            //   },
            //   {
            //       "Question": "Email contact",
            //       "Tooltip": "This is the email of the contact",
            //       "isRequired": true,
            //       "IsMultiline": false,
            //       "isEmail": true,
            //       "isUrl": false,
            //       "isVideo": false,
            //       "url": "/blueprint/data/awards/test-award/award-form/general/contact/email-contact",
            //       "type": "TextQuestion",
            //       "name": "0165bf34-08e8-4813-b803-fc952585f0be",
            //       "Description": "",
            //       "order": 6,
            //       "answer": "<EMAIL>",
            //       "rows": 3,
            //       "HideLabel":false
            //   },
          ],
          "url": "/blueprint/data/awards/test-award/award-form/general/contact",
          "type": "FormStepSection",
          "name": "stepid2",
          "CanSelectMultipleItems": false
      }
  ],
  "url": "/blueprint/data/awards/test-award/award-form/general",
  "type": "FormStep",
  "name": "stepid2",
  "StepNumber": 2,
  "IsSummary": false
}
const step3: FormStep = {
  "StepName": "Design",
  "StepTitle": "Design",
  "StepDescription": "Please fill in the information regarding the design.",
  "children": [
      {
          "Title": "Designer",
          "Description": "<p>Details from the designer</p>",
          "children": [
              {
                  "Question": "Name of designer",
                  "Tooltip": "",
                  "isRequired": true,
                  "maxLength": 50,
                  "IsMultiline": false,
                  "isEmail": false,
                  "onlyNumbers": false,
                  "isUrl": false,
                  "isVideo": false,
                  "url": "/blueprint/data/awards/test-award/award-form/design/designer/name-of-designer",
                  "type": "TextQuestion",
                  "name": "stepid0",
                  "Description": "",
                  "order": 7,
                  "answer": "Jesse putra",
                  "rows": 3,
                  "HideLabel":false
              },
              {
                  "Question": "Country of designer",
                  "Tooltip": "",
                  "isRequired": true,
                  "Answers": [],
                  "url": "/blueprint/data/awards/test-award/award-form/design/designer/country-of-designer",
                  "type": "DropdownQuestion",
                  "name": "stepid0",
                  "isCountryDropdown": true,
                  "Description": "",
                  "order": 8,
                  "answer": "Brazil",
                  "HideLabel":false
              },
              {
                  "Question": "dropdown",
                  "Tooltip": "",
                  "isRequired": true,
                  "Answers": [
                      {
                          "Answer": "option1",
                          "url": "/blueprint/data/awards/test-award/award-form/design/designer/dropdown/option1",
                          "type": "AnswerOption",
                          "name": "stepid0"
                      },
                      {
                          "Answer": "option2",
                          "url": "/blueprint/data/awards/test-award/award-form/design/designer/dropdown/option2",
                          "type": "AnswerOption",
                          "name": "stepid0"
                      }
                  ],
                  "url": "/blueprint/data/awards/test-award/award-form/design/designer/dropdown",
                  "type": "DropdownQuestion",
                  "name": "stepid0",
                  "isCountryDropdown": false,
                  "Description": "",
                  "order": 9,
                  "answer": "option1",
                  "HideLabel":false
              },
              {
                  "Question": "multiline",
                  "Tooltip": "",
                  "isRequired": true,
                  "IsMultiline": true,
                  "isEmail": false,
                  "onlyNumbers": false,
                  "isUrl": false,
                  "isVideo": false,
                  "url": "/blueprint/data/awards/test-award/award-form/design/designer/multiline",
                  "type": "TextQuestion",
                  "name": "stepid0",
                  "Description": "",
                  "order": 10,
                  "answer": "lorem ipsum bubur sumsum",
                  "rows": 3,
                  "HideLabel":false
              },
              {
                "Question": "multiline 6 rows",
                "Tooltip": "",
                "isRequired": true,
                "IsMultiline": true,
                "isEmail": false,
                "onlyNumbers": false,
                "isUrl": false,
                "isVideo": false,
                "url": "/blueprint/data/awards/test-award/award-form/design/designer/multiline",
                "type": "TextQuestion",
                "name": "stepid0",
                "Description": "",
                "order": 10,
                "answer": "lorem ipsum bubur blok m",
                "rows": 6,
                "HideLabel":false
            },
            {
                "Question": "checkbox",
                "Tooltip": "",
                "isRequired": false,
                "Answers": [
                    {
                        "Answer": "answer1 <b>bold</b>",
                        "url": "/blueprint/data/awards/test-award/award-form/design/designer/checkbox/answer1",
                        "type": "AnswerOption",
                        "name": "stepid0"
                    },
                    {
                        "Answer": "answer2",
                        "url": "/blueprint/data/awards/test-award/award-form/design/designer/checkbox/answer2",
                        "type": "AnswerOption",
                        "name": "stepid10"
                    }
                ],
                "url": "/blueprint/data/awards/test-award/award-form/design/designer/checkbox",
                "type": "CheckboxQuestion",
                "name": "stepid0",
                "Description": "",
                "order": 11,
                "answer": "",
                "HideLabel": false
            }
          ],
          "url": "/blueprint/data/awards/test-award/award-form/design/designer",
          "type": "FormStepSection",
          "name": "75801ed8-93b5-4328-b921-f0faf8e3377b",
          "CanSelectMultipleItems": false
      },
      {
          "Title": "Intellectual property and/or copyright*",
          "Description": "Please indicate one of the following options",
          "children": [
              {
                  "Question": "Intellectual property and/or copyright",
                  "Tooltip": "",
                  "isRequired": true,
                  "Answers": [
                      {
                          "Answer": "This <b>product</b> is <a href='/'>intellectual</a> property and/or copyright of the undersigned",
                          "url": "/blueprint/data/awards/test-award/award-form/design/intellectual-property-and-or-copyright/intellectual-property-and-or-copyright/this-product-is-intellectual-property-and-or-copyright-of-the-undersigned",
                          "type": "AnswerOption",
                          "name": "stepid20"
                      },
                      {
                          "Answer": "You have an official confirmation of patent agency that the request is still in process",
                          "url": "/blueprint/data/awards/test-award/award-form/design/intellectual-property-and-or-copyright/intellectual-property-and-or-copyright/you-have-an-official-confirmation-of-patent-agency-that-the-request-is-still-in-process",
                          "type": "AnswerOption",
                          "name": "stepid21"
                      },
                      {
                          "Answer": "You are supplying written approval of the holder of the intellectual property rights stating that you are allowed to enter the DAME competition with this product.",
                          "url": "/blueprint/data/awards/test-award/award-form/design/intellectual-property-and-or-copyright/intellectual-property-and-or-copyright/you-are-supplying-written-approval-of-the-holder",
                          "type": "AnswerOption",
                          "name": "stepid22"
                      }
                  ],
                  "url": "/blueprint/data/awards/test-award/award-form/design/intellectual-property-and-or-copyright/intellectual-property-and-or-copyright",
                  "type": "RadiobuttonQuestion",
                  "name": "stepid20",
                  "Description": "",
                  "questionId": 0,
                  "order": 12,
                  "answer": ""
              } as RadiobuttonQuestion,
              {
                  "Question": "test",
                  "Tooltip": "",
                  "isRequired": false,
                  "IsMultiline": false,
                  "isEmail": false,
                  "onlyNumbers": false,
                  "isUrl": false,
                  "isVideo": false,
                  "url": "/blueprint/data/awards/test-award/award-form/design/intellectual-property-and-or-copyright/test",
                  "type": "TextQuestion",
                  "name": "adcd1752-9b1d-4eff-b39d-de8293afce10",
                  "Description": "",
                  "onlyShowWhen": {
                      "questionName": "stepid20",
                      "answerName": "3a82844d-1a92-4093-ac3a-e6c5bdcb429b"
                  },
                  "order": 13,
                  "answer": "",
                  "rows": 3,
                  "HideLabel":false
              }
          ],
          "url": "/blueprint/data/awards/test-award/award-form/design/intellectual-property-and-or-copyright",
          "type": "FormStepSection",
          "name": "dc7c7c03-78b3-4c2d-aa53-d8cbc5d0ddf5",
          "CanSelectMultipleItems": false
      }
  ],
  "url": "/blueprint/data/awards/test-award/award-form/design",
  "type": "FormStep",
  "name": "stepid0",
  "StepNumber": 3,
  "IsSummary": false
}

const summaryStep: FormStep = {
  "StepName": "Summary",
  "StepTitle": "Summary",
  "StepDescription": "<p>An overview of you entry</p>",
  "children": [
      {
          "Title": "Terms and conditions",
          "Description": "",
          "children": [
              {
                  "Question": "Agree with conditions",
                  "Tooltip": "",
                  "isRequired": true,
                  "Answers": [
                      {
                          "Answer": "I hereby declare that I have read and agree with the DAME 2024 Specific Terms and Conditions and believe the product entered meets all criteria. My product will be on the market for sale by 15 November 2023, the first day of the METSTRADE Show. If in course of time this appears not to be achievable, I will inform the METSTRADE project team in writing (<NAME_EMAIL>), before 4th October 2023 and I will also withdraw my participation in the DAME Awards.*",
                          "url": "/blueprint/data/awards/test-award/award-form/summary/terms-and-conditions/agree-with-conditions/agree",
                          "type": "AnswerOption",
                          "name": "09682c92-dfc3-489f-8ce8-a6606561af32"
                      }
                  ],
                  "url": "/blueprint/data/awards/test-award/award-form/summary/terms-and-conditions/agree-with-conditions",
                  "type": "CheckboxQuestion",
                  "name": "aa69ac1d-78f0-4f7a-bef1-9762cfef2d46",
                  "Description": "",
                  "order": 14,
                  "answer": "",
                  "HideLabel": true
              }
          ],
          "url": "/blueprint/data/awards/test-award/award-form/summary/terms-and-conditions",
          "type": "FormStepSection",
          "name": "d34ecf04-c761-43ed-ab9a-8d65eebffad8",
          "CanSelectMultipleItems": false
      }
  ],
  "url": "/blueprint/data/awards/test-award/award-form/summary",
  "type": "SummaryFormStep",
  "name": "3314a3f3-afc4-47d6-9122-022af7008713",
  "StepNumber": 4,
  "IsSummary": true
}

const IntroProps: IntroAwardData = {
    icon: "https://placehold.co/80x64",
    title: "Before you start",
    description: "Take the following information into account before starting your registration.",
    buttonText: "Start registration",
    cancelButtonText: "Cancel",
    cancelButtonLink: "/",
    listItems: [
        {
            icon: 'fa-thin fa-trophy',
            title: 'Winner per category',
            description:
            'For each category a jury will select nominees. The winners will be announced at the event.',
            readMoreButton: 'Read more',
            modalTitle: 'This is the modal title',
            modalDescription: '<p>This is the <b>html</b> modal description</p>',
            modalButton: 'Got it',
        },
        {
            icon: 'fa-thin fa-flag',
            title: 'Information Publishing',
            description:
            'Your submission will be shared with the jury. Please note that nominees will be published on the website.',
            readMoreButton: 'Read more',
            modalTitle: 'This is the modal title',
            modalDescription: '<p>This is the <b>html</b> modal description</p>',
            modalButton: 'Got it',
        },
        {
            icon: 'fa-thin fa-list',
            title: 'Registration fee € 150.00 (excl. VAT)',
            description:
            'The DAME Awards\' total registration fees are contributed to a designated charity.',
            readMoreButton: 'Read more',
            modalTitle: 'This is the modal title',
            modalDescription: '<p>This is the <b>html</b> modal description</p>',
            modalButton: 'Got it',
        },
        {
            icon: 'fa-thin fa-cloud-arrow-down',
            title: 'Track your progress',
            description:
            'Your progress will be saved, so you can take breaks without losing work.',
            readMoreButton: 'Read more',
            modalTitle: 'This is the modal title',
            modalDescription: '<p>This is the <b>html</b> modal description</p>',
            modalButton: 'Got it',
        },
    ]
}

export const AwardsFormStructureData: AwardsFormStructure =
{
    QuestionnaireId: 'questionnaire-1',
    QuestionnaireName: 'Test questionnaire',
    QuestionnareStatus: 'DRAFT',
    BackgroundImage: '/images/Background.png',
    ProductFallbackImage: '/images/logo/rai-logo.png',
    ThankYouPage: '/',
    FormSteps: [
        step1,
        step2,
        step3,
        summaryStep,
    ],
    Introduction: IntroProps,
    ClosePage: "/",
    SummaryClosePage: "/",
    HelpModalTitle: "How can we help you?",
    HelpModalDescription: "If you are unsure, you can always get in contact with our customer service. We are available from Monday to Friday, 8:00-16:00.",
    HelpModalPhoneNumber: "020 549 12 12",
    HelpModalEmail: "<EMAIL>",
    CloseModalTitle: "Are you sure you want to quit?",
    CloseModalDescription: "If you close this screen, your participation is not final. The system will save your current progress. You can submit or edit your registration until September 15th 2024.",
    CloseModalCancelButton: "No, cancel",
    CloseModalQuitButton: "Yes, quit",
    ProductTitle: "Your Product",
    ProductCompany: "Company",
    ProductIntroductionDate: "Introduction Date",
    ProductAbout: "About",
    SummaryTitleWhenClosed: "Summary title",
    SummaryDescriptionWhenClosed: "Summary description after the registration for the awards is closed",
    SummaryCloseButton: "Close",
    BackButton: "Back",
    RequiredFooter: "All fields marked * must be completed.",
    SupportedFileFormatsImage: "Supported file formats .JPG, .JPEG, .PNG",
    SupportedFileFormatsDocument: "Supported file formats .PDF",
    FileSizeLimit: "2 MB limit.",
    NumberOfFilesAllowed: "Max {MaxFiles} file(s) allowed.",
    NextButton: "Next",
    ReviewSummaryButton: "Review your Summary",
    SubmitAwardButton: "Submit Your Registration"
};

export const QuestionnaireData: Questionnaire = {
  questionnaireId: "test-questionnaire",
  questionnaireStatus: "DRAFT",
  companyName: "ERPI",
  answers: [
    {
        questionId: "question-1",
        questionText: "What is the category?",
        answerId: "",
        answerText: "<ul><li>SummaryTitleCard -1</li> <li>SummaryTitleCard -2 </li></ul>",
        profileServiceId: "",
        stepName: "Category",
        stepId: "stepid",
        order: 0,
        questionType: "",
        files: []
    },
    {
        questionId: "question-2",
        questionText: "Name of original manufacturer",
        answerId: "",
        answerText: "Erdi",
        profileServiceId: "",
        stepName: "General",
        stepId: "stepid2",
        order: 1,
        questionType: "",
        files: []
    },
    {
        questionId: "question-3",
        questionText: "Origin of original manufacturer",
        answerId: "",
        answerText: "The Netherlands",
        profileServiceId: "",
        stepName: "General",
        stepId: "stepid2",
        order: 2,
        questionType: "",
        files: []
    },
    {
        questionId: "question-3",
        questionText: "Origin of original manufacturer",
        answerId: "",
        answerText: "The Indonesia",
        profileServiceId: "",
        stepName: "General",
        stepId: "stepid2",
        order: 2,
        questionType: "",
        files: []
    },
    {
        questionId: "question-4",
        questionText: "Name of designer",
        answerId: "",
        answerText: "John doe",
        profileServiceId: "",
        stepName: "Design",
        stepId: "stepid3",
        order: 3,
        questionType: "",
        files: []
    },
    {
        questionId: "question-5",
        questionText: "Country of designer",
        answerId: "",
        answerText: "The Netherlands",
        profileServiceId: "",
        stepName: "Design",
        stepId: "stepid3",
        order: 4,
        questionType: "",
        files: []
    },
    {
        questionId: "question-6",
        questionText: "images multiple",
        answerId: "",
        answerText: "",
        profileServiceId: "",
        stepName: "Design",
        stepId: "stepid3",
        order: 4,
        questionType: "DOCUMENT",
        files: [ 
            {
                lastModified: 0,
                name: 'image1.png',
                size: 11123,
                type: 'image/png',
                webkitRelativePath: '',
                imageDataUrl: 'https://stpsaccwe.blob.core.windows.net/media-blobs/de03e85ce029744c9b19596ba1a3a59e.png',
                isValid: true,
                imageWidth: 0,
                imageHeight: 0
            }, 
            {
                lastModified: 0,
                name: 'image2.png',
                size: 11123,
                type: 'image/png',
                webkitRelativePath: '',
                imageDataUrl: 'https://stpsaccwe.blob.core.windows.net/media-blobs/de03e85ce029744c9b19596ba1a3a59e.png',
                isValid: true,
                imageWidth: 0,
                imageHeight: 0
            }
        ]
    },
    {
        questionId: "question-7",
        questionText: "documents multiple",
        answerId: "",
        answerText: "",
        profileServiceId: "",
        stepName: "Design",
        stepId: "stepid3",
        order: 4,
        questionType: "DOCUMENT",
        files: [ 
            {
                lastModified: 0,
                name: 'doc1.pdf',
                size: 11123,
                type: 'application/pdf',
                webkitRelativePath: '',
                imageDataUrl: 'https://stpsaccwe.blob.core.windows.net/media-blobs/de03e85ce029744c9b19596ba1a3a59e.png',
                isValid: true,
                imageWidth: 0,
                imageHeight: 0
            }, 
            {
                lastModified: 0,
                name: 'doc2.pdf',
                size: 11123,
                type: 'application/pdf',
                webkitRelativePath: '',
                imageDataUrl: 'https://stpsaccwe.blob.core.windows.net/media-blobs/de03e85ce029744c9b19596ba1a3a59e.png',
                isValid: true,
                imageWidth: 0,
                imageHeight: 0
            }
        ]
    },
  ],
  product: {
    name: "THOA portable wireless dim",
    shortDescription: "Because of the structured glass the portable lamp draws a fascinating play of light.",
    longDescription: "Because of the structured glass the portable lamp draws a fascinating play of light. Thanks to dim2warm® technology, dimming not only changes the brightness, but also the color of the light, it gets warmer. The batteries in the lamp will be charged inductive in a special case which can charges and stores 4 lamps. It can be controlled wireless by a smart-home APP to create groups of serval lamps.",
    url: "",
    introductionDate: "2024-06-29T18:16:30.572385",
    publicFrom: "",
    publicUntil: "",
    categories: [],
    sortIndex: {
      sortUpIndex: null,
      currentSortIndex: 0,
      sortDownIndex: null
    },
    isNovelty: true
  },
  profileServiceId: "1"
}



export const AwardsSummarystructureData: AwardsFormStructure =
{
    QuestionnaireId: 'questionnaire-1',
    QuestionnaireName: 'Test questionnaire',
    QuestionnareStatus: 'DRAFT',
    BackgroundImage: '/images/Background.png',
    ProductFallbackImage: '/images/logo/rai-logo.png',
    ThankYouPage: '/',
    FormSteps: [
        // step1,
        // step2,
        // step3,
        summaryStep,
    ],
    Introduction: IntroProps,
    ClosePage: "/",
    SummaryClosePage: "/",
    HelpModalTitle: "How can we help you?",
    HelpModalDescription: "If you are unsure, you can always get in contact with our customer service. We are available from Monday to Friday, 8:00-16:00.",
    HelpModalPhoneNumber: "020 549 12 12",
    HelpModalEmail: "<EMAIL>",
    CloseModalTitle: "Are you sure you want to quit?",
    CloseModalDescription: "If you close this screen, your participation is not final. The system will save your current progress. You can submit or edit your registration until September 15th 2024.",
    CloseModalCancelButton: "No, cancel",
    CloseModalQuitButton: "Yes, quit",
    ProductTitle: "Your Product",
    ProductCompany: "Company",
    ProductIntroductionDate: "Introduction Date",
    ProductAbout: "About",
    SummaryTitleWhenClosed: "Summary title",
    SummaryDescriptionWhenClosed: "Summary description after the registration for the awards is closed",
    SummaryCloseButton: "Close",
    BackButton: "Back",
    RequiredFooter: "All fields marked * must be completed.",
    SupportedFileFormatsImage: "Supported file formats .JPG, .JPEG, .PNG",
    SupportedFileFormatsDocument: "Supported file formats .PDF",
    FileSizeLimit: "2 MB limit.",
    NumberOfFilesAllowed: "Max {MaxFiles} file(s) allowed.",
    NextButton: "Next",
    ReviewSummaryButton: "Review your Summary",
    SubmitAwardButton: "Submit Your Registration"
};
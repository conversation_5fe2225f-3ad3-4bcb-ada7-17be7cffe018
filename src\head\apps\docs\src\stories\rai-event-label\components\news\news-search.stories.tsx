import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { WithSitecoreContextDecorator } from "@uxbee/storybook-mock-sc";
import { NewsItemsMockData } from "../../data/news.data";
import { NewsSearchInternal } from "rai-event-label";

const meta: Meta<typeof NewsSearchInternal> = {
    title: "RAI/Event Label/News/NewsSearch",
    component: NewsSearchInternal,
    tags: ["autodocs", "rai", "ep"],
    argTypes: {},
    decorators: [
        (Story, context) =>
            WithSitecoreContextDecorator(
                () => (
                    <div className="min-h-screen bg-[#fafafa]">
                        <Story />
                    </div>
                ),
                context,
                NewsSearchInternal,
                false
            ),
    ],
};

export default meta;

type Story = StoryObj<typeof NewsSearchInternal>;

export const Tile: Story = {
    args: {
        newsItems: NewsItemsMockData,
        hasNext: false,
        columns: 3,
        showSearchBar: true,
        pageSize: 18,
        language: "en",
        loading: false,
        allNewsUrl: '/news',
        variant: 'tile',
        onUpdateSearchText: () => {},
        updateSearchResult: () => {},
        loadMore: () => {},
        texts: {
          noResultFound: "No results found!",
          allNews: "All News"
        }
    },
};

export const List: Story = {
    args: {
        newsItems: NewsItemsMockData,
        hasNext: false,
        columns: 3,
        showSearchBar: true,
        pageSize: 18,
        language: "en",
        loading: false,
        allNewsUrl: '/news',
        variant: 'list',
        onUpdateSearchText: () => {},
        updateSearchResult: () => {},
        loadMore: () => {},
        texts: {
          noResultFound: "No results found!",
          allNews: "All News"
        }
    },
};

export const Masonry: Story = {
    args: {
        newsItems: NewsItemsMockData,
        hasNext: false,
        columns: 3,
        showSearchBar: true,
        pageSize: 18,
        language: "en",
        loading: false,
        allNewsUrl: '/news',
        variant: 'masonry',
        onUpdateSearchText: () => {},
        updateSearchResult: () => {},
        loadMore: () => {},
        texts: {
          noResultFound: "No results found!",
          allNews: "All News"
        }
    },
};

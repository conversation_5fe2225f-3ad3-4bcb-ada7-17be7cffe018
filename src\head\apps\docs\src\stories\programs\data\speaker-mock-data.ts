import { Speaker } from "programs";

export const SpeakerMockData: Speaker[] = [
    {
        company_name: "Yunex Traffic",
        description: "<PERSON> is the product manager for Yunex Traffic's traffic management system. <PERSON> began his transportation career as an operator at a traffic management center in the United States while studying civil engineering at the University of Washington. After years of experiencing firsthand that efficient and sustainable mobility requires more than just optimizing for cars, <PERSON> moved to Germany to expand his knowledge in urban planning and railway infrastructure, and now holds a master's degree in Transportation Systems from the Technical University of Munich. As a proponent of optimizing mobility for people, not cars, <PERSON> strives every day with his team to enable traffic operators and planners around the world to accelerate their transition towards sustainable mobility. Having worked in high-stress traffic operations environments, <PERSON> also places heavy emphasis on design and usability to improve workflows and make the day-to-day lives of TMC operators easier.",
        full_name: "<PERSON>",
        id: "spkintertrafficams20240001",
        image_url: "https://www.intertraffic.com/amsterdam/-/media/ae29126ba2414061b8f7eeb0f178df00.jpg",
        job_title: "Product manager",
        location: "Intertraffic Summit Theatre 1",
        sessions: [
            "intertrafficams20240002"
        ],
    },
    {
        company_name: "<PERSON><PERSON><PERSON>",
        description: "A serial tech entrepreneur and a commercial lawyer, <PERSON> began his career as an M&A lawyer in the industrial infrastructure and energy sectors. Working with a broad range of his clients that included both, large corporates and innovative startups, inspired him to become an entrepreneur and led him to launching his first company in the green tech (solar energy) field. His passion for advanced technology, AI and data analytics, resulted in establishing Valerann in 2016. \nNative to Israel, Gabriel holds an MBA from London Business School, and LLB (honours) and BA in Economics from Bar Ilan University.",
        full_name: "Gabriel Jacobson",
        id: "spkintertrafficams20240005",
        image_url: "https://www.intertraffic.com/amsterdam/-/media/dd9e44e9d8aa498abcaed023c70631c8.jpg",
        job_title: "CEO",
        location: "Intertraffic Summit Theatre 2",
        sessions: [
            "intertrafficams20240010"
        ],
    },
    {
        company_name: "Xenonatix",
        full_name: "Kris de Meester",
        id: "spkintertrafficams20240010",
        image_url: "https://www.intertraffic.com/amsterdam/-/media/5d4c34f0aa2842769ecc1079edd5b3fe.jpg",
        job_title: "Vice President Sales&BD",
        location: "Intertraffic Summit Theatre 2",
        sessions: [
            "intertrafficams20240012"
        ],
    },
    {
        company_name: "Rijkswaterstaat",
        description: "Michiel Bontenbal is advisor on intelligent and smart traffic management at Rijkswaterstaat. He has been department head for 22 years in Rijkswaterstaat. In 2021 he started with his growing interest for Smart Mobiliy to be project manager C4Safety. This project brings safety to our road inspectors and road workers. Through in-car technique and secured data management, the road user will be utterly served and informed.",
        full_name: "Michiel Bontenbal",
        id: "spkintertrafficams20240014",
        image_url: "https://www.intertraffic.com/amsterdam/-/media/a778a4e812a3471cb128f03622fbb3b8.png",
        job_title: "Project manager",
        location: "Intertraffic Summit Theatre 2",
        sessions: [
            "intertrafficams20240014"
        ],
    },
    {
        company_name: "Nissan Motor Corporation USA",
        full_name: "Liam Pedersen",
        id: "spkintertrafficams20240019",
        image_url: "https://www.intertraffic.com/amsterdam/-/media/82fc9975216a4979b09a7dcf94a54e72.jpg",
        job_title: "Chief Scientist",
        location: "Intertraffic Summit Theatre 3",
        sessions: [
            "intertrafficams20240016"
        ],
    },
    {
        company_name: "European Cyclists' Federation",
        full_name: "Henk Swarttouw",
        id: "spkintertrafficams20240023",
        image_url: "https://www.intertraffic.com/amsterdam/-/media/70bacadcb15b41f9809c09e81a212430.jpg",
        job_title: "President",
        location: "Intertraffic Summit Theatre 4",
        sessions: [
            "intertrafficams20240023",
            "intertrafficams20240035"
        ],
    },
    {
        company_name: "Swarco",
        description: "As an Innovation Manager at SWARCO, Itir Coskun focuses on new trends in the mobility field in order to develop future-proof solutions in the context of ITS. She especially works in the areas of CCAM, MaaS, Multi-modal and Sustainable Mobility Solutions. Holding a Master of Science degree in Transportation Systems with the focus on ITS, Itir Coskun is still involved in academic activities and continues to learn and share knowledge internationally. With her academic background and experiences from several interdisciplinary and international projects, she is dedicated to shape the future of mobility that is sustainable and inclusive for all.",
        full_name: "Itir Coskun",
        id: "spkintertrafficams20240027",
        image_url: "https://www.intertraffic.com/amsterdam/-/media/81ae1baebd9d4e09b09620ae783bcbdf.jpg",
        job_title: "Regional Innovation Manager",
        location: "ITSUP Arena",
        sessions: [
            "intertrafficams20240033",
            "intertrafficams20240196"
        ],
    },
    {
        company_name: "Studio Bereikbaar",
        full_name: "Roland Kager",
        id: "spkintertrafficams20240031",
        image_url: "https://www.intertraffic.com/amsterdam/-/media/f4306023afca4036ab53c800764bbd72.jpg",
        job_title: "Data analist of mobility",
        location: "Intertraffic Summit Theatre 2",
        sessions: [
            "intertrafficams20240034"
        ],
    },
    {
        company_name: "IEM SA",
        full_name: "Emmanuelle Durand",
        id: "spkintertrafficams20240038",
        image_url: "https://www.intertraffic.com/amsterdam/-/media/ce2d45838e8745b5b29fdbacd26956b9.jpg",
        job_title: "Sales Marketing Manager",
        location: "Intertraffic Summit Theatre 4",
        sessions: [
            "intertrafficams20240044"
        ],
    },
    {
        company_name: "Vitronic GmbH",
        description: "Solution Manager at Vitronic. Simon Griffiths is a professional in the field of technology and innovation, currently serving as Solution Manager at VITRONIC in Germany. In this role, he spearheads the development of end-to-end solutions tailored for VITRONIC's customers, with a keen emphasis on cutting-edge technologies such as Machine Vision and Artificial Intelligence. With a rich background as a Technical Director and Chief Technology Officer, Simon brings a wealth of global experience, having lived and worked in the United States, China and Germany. His more than 30-year career has been dedicated to product development and research, showcasing his versatility and proficiency in the ever-evolving tech landscape, specifically focusing on Intelligent Transport Systems (ITS) for the past 15 years.",
        full_name: "Simon Griffith",
        id: "spkintertrafficams20240043",
        image_url: "https://www.intertraffic.com/amsterdam/-/media/f953de536a114643b9d78eb06c3eb2c9.jpg",
        job_title: "Solution Manager",
        location: "Intertraffic Summit Theatre 1",
        sessions: [
            "intertrafficams20240048"
        ],
    }
];

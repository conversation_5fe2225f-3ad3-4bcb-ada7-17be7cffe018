[CmdletBinding()]
param(
    [string]
    $Client_Id = $ENV:XMC_CLIENT_ID,
    [string]
    $Client_Secret = $ENV:XMC_CLIENT_SECRET,
    [switch]
    $Force
)
Push-Location $PSScriptRoot

$CommandDir = Get-Location
Push-Location -Path ..
$ContextDir = Get-Location
Pop-Location
. $CommandDir/.powershell/Foundation.Sitecore.XMCloud.ps1

$UserFile = Join-Path $ContextDir "/.sitecore/user.json"

if(!$Force) {
    Invoke-XMCloud-Login -Client_Id $Client_Id -Client_Secret $Client_Secret -UserFile $UserFile
} else {
    Invoke-XMCloud-Login -Client_Id $Client_Id -Client_Secret $Client_Secret -UserFile $UserFile -Force
}
Pop-Location
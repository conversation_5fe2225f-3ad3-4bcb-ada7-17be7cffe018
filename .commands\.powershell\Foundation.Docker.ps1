function Find-Running-Containers() {
    $hash = docker compose ps --services --filter "status=running"
    return $hash.count
}
function Get-Traefik-Routers-Hosts(){
    param(
        [ValidateNotNullOrEmpty()]
        [string]
        $DockerComposeFilePath,
        [string]
        $HostPattern = "([a-zA-Z0-9._-]+\.(local|nl|com){1})"
    )

    $content = Get-Content -Path $DockerComposeFilePath -Raw

    $patternToExtractValue = "traefik\.http\.routers\.(.*)-secure\.rule=(.*)"
    $taefikRoutersHosts = ([regex]$patternToExtractValue).matches($content)

    $patternToExtractHosts = $HostPattern
    $traefikRoutersHosts = ([regex]$patternToExtractHosts).matches($taefikRoutersHosts.Value)

    $traefikhosts = @()
    foreach ($traefikhost in $traefikRoutersHosts) {
        $traefikhosts += $traefikhost.Value
    }
    return $traefikhosts
}

function Add-Traefik-Routers-Host(){
    param(
        [ValidateNotNullOrEmpty()]
        [string]
        $DockerComposeFilePath,
        [ValidateNotNullOrEmpty()]
        [string]
        $HostName
    )
    $traefikhosts = Get-Traefik-Routers-Hosts -DockerComposeFilePath $DockerComposeFilePath
    $traefikhosts += $HostName
    $wrappedHost = $traefikhosts | ForEach-Object { "Host(``$_``)" }
    $combinedHost = $wrappedHost -join "|"

    $content = Get-Content -Path $DockerComposeFilePath -Raw

    $patternToExtractValue = "(?<=traefik\.http\.routers\.(.*)-secure\.rule=)(.*)(?<!"")"
    $updatedContent = [regex]::Replace($content, $patternToExtractValue, $combinedHost) 

    $updatedContent | Set-Content -Path $DockerComposeFilePath
}
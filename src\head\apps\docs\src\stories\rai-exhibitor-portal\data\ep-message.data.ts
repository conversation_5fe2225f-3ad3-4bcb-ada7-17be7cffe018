export const MessageData = {
    params: {
        name: "EPMessage",
        componentName: "EPMessage",
        tag: "h1",
        GridParameters: "col-span-12 xl:col-span-6 lg:col-span-6"
    },
    rendering: {
        uid: "{00000000-0000-0000-0000-000000000000}",
        componentName: "EPMessage",
        dataSource: "{00000000-0000-0000-0000-000000000000}",
    },
    fields: {
        data: {
            datasource: {
                field: {
                    title: {
                        editable: "true",
                        value: "Main Title"
                    }
                },
                children: {
                    results: [
                        {
                            title: {
                                value: "Block Title 1"
                            },
                            subtitle: {
                                value: "Subtitle 1"
                            },
                            date: {
                                value: "2024-02-12"
                            },
                            content: {
                                value: "Lorem ipsum dolor sit amet, consectetur adipiscing elit."
                            },
                            link: {
                                jsonValue: {
                                    value: {
                                        href: "#",
                                        text: "Example Link",
                                        linktype: "External", // or "Internal"
                                        url: "https://example.com",
                                        anchor: "section1",
                                        target: "_blank", // or "_self", "_parent", "_top"
                                        title: "Example Title",
                                        class: "example-class",
                                        querystring: "?param=value",
                                        id: "example-link"
                                    }
                                }
                            },
                            image: {
                                jsonValue: {
                                    value: {
                                        src: "image.jpg",
                                        alt: "Example Image",
                                        width: "100",
                                        height: "100"
                                    }
                                }
                            }
                        },
                        {
                            title: {
                                value: "Block Title 2"
                            },
                            subtitle: {
                                value: "Subtitle 2"
                            },
                            date: {
                                value: "2024-02-11"
                            },
                            content: {
                                value: "Nulla venenatis mauris eget felis placerat, at fermentum metus malesuada."
                            },
                            link: {
                                jsonValue: {
                                    value: {
                                        href: "#",
                                        text: "Example Link",
                                        linktype: "External", // or "Internal"
                                        url: "https://example.com",
                                        anchor: "section1",
                                        target: "_blank", // or "_self", "_parent", "_top"
                                        title: "Example Title",
                                        class: "example-class",
                                        querystring: "?param=value",
                                        id: "example-link"
                                    }
                                }
                            },
                            image: {
                                jsonValue: {
                                    value: {
                                        src: "image.jpg",
                                        alt: "Example Image",
                                        width: "100",
                                        height: "100"
                                    }
                                }
                            }
                        }
                    ]
                }
            }
        }
    }
}
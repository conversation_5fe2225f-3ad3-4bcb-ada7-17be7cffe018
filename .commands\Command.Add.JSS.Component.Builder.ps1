[CmdletBinding()]
param(
    [string]
    $PatternIdentifier = "sc",
    [string]
    $PatternSeperator = "-",
    [array]
    [ValidateNotNullOrEmpty()]
    $ExcludeApps = @("docs"),
    [string]
    $AppName = "*" #* means all apps in this case
)
Push-Location $PSScriptRoot
# Define the script context
$CommandDir = Get-Location
Push-Location -Path ..
$ContextDir = Get-Location
Pop-Location

# Import the used powershell functions
. $CommandDir/.powershell/Foundation.JSON.ps1

Write-Host "Starting to install the component builder" -ForegroundColor DarkYellow

# Setting some variables
$HasRelevantComponents = $false
$TurboPath = Join-Path $ContextDir "src/head"
$AppsPath = Join-Path $TurboPath "apps"
$TemplatePath = Join-Path $TurboPath "templates/jss-component-builder"
$ComponentBuilderPluginPath = "scripts/generate-component-builder/externalPackageComponentInjectionPluginBase.ts"
$ComponentBuilderPluginTemplate = Join-Path $TemplatePath $ComponentBuilderPluginPath
$ComponentBuilderConfigTemplate = Join-Path $TemplatePath "scripts/generate-component-builder/plugins/plugin.tpl"
$ComponentBuilderConfigFilePattern = 'scripts/generate-component-builder/plugins/${PackageName}-packages.ts'
$Pattern = -join($PatternIdentifier, $PatternSeperator)

Get-ChildItem $AppsPath -Directory -Exclude $ExcludeApps | ForEach-Object {
    if($AppName -eq "*" -or $AppName -eq $_.Name) {
        $WorkspacePath = $_.FullName
        $Workspace = $_.Name
        $PackageJsonFile = Join-Path $_.FullName "package.json"
        # Check if the package file exists
        If(Test-Path -PathType Leaf -Path $PackageJsonFile) {
            Write-Host "Installing Component Builder plugin on workspace" $_.Name -ForegroundColor DarkYellow
            # Read the package.json file
            $PackageJson = Read-JSON-From-File -JSONFile $PackageJsonFile
            $dependencies = $PackageJson.dependencies
            $dependencyMembers = $(Get-Member -InputObject $dependencies)
            $dependencyMembers | ForEach-Object {
                # Find all sitecore related workspace packages
                if($_.name.StartsWith($Pattern)) {
                    $HasRelevantComponents = $true
                    # Add a plugin config file per relevant package.
                    $ComponentBuilderConfigFile = Join-Path $WorkspacePath ($ComponentBuilderConfigFilePattern.Replace('${PackageName}', $_.name))
                    if(!(Test-Path -PathType Leaf -Path $ComponentBuilderConfigFile)) {
                        Copy-Item $ComponentBuilderConfigTemplate -Destination $ComponentBuilderConfigFile -Force
                        # Create the needed variables per package config file
                        $PackageNameBlocks = $_.name.Split($PatternSeperator)
                        $PackagePattern = $PackageNameBlocks[0].ToLower()
                        $PackageName = $PackageNameBlocks[1].ToLower()
                        $PackageName = $PackageName.Replace($PackageName[0],$PackageName[0].ToString().ToUpper())
                        $PluginVaraibleName = -join($PackagePattern, $PackageName)
                        $PackagePattern = $PackagePattern.Replace($PackagePattern[0],$PackagePattern[0].ToString().ToUpper())
                        $ClassName = -join($PackagePattern, $PackageName)
                    
                        # Replace the varaibles in the plugin config file
                        $FileContent = Get-Content $ComponentBuilderConfigFile
                        $FileContent = $FileContent.replace('${packageName}', $_.name)
                        $FileContent = $FileContent.replace('${className}', $ClassName)
                        $FileContent = $FileContent.replace('${pluginVariableName}', $PluginVaraibleName)
                        Set-Content -Path $ComponentBuilderConfigFile -Value $FileContent
                        Write-Host "The component builder plugin config file has been installed for the package" $_.name "on the" $Workspace "workspace" -ForegroundColor DarkYellow
                    } else {
                        Write-Host "The component builder plugin config file has been skipped as it already exists for the package" $_.name "on the" $Workspace "workspace" -ForegroundColor DarkYellow
                    }
                }            
            }        
            if($HasRelevantComponents) {
                $ComponentBuilderPlugin = Join-Path $WorkspacePath $ComponentBuilderPluginPath 
                if(!(Test-Path -PathType Leaf -Path $ComponentBuilderPlugin)) {
                # if any sitecore related workspaces packages are found, copy the plugin root component      
                    Copy-Item $ComponentBuilderPluginTemplate -Destination $ComponentBuilderPlugin -force
                }
                Write-Host "The component builder plugin has been sucesfully installed on" $_.Name "workspace" -ForegroundColor DarkYellow
            } else {
                Write-Host "No relevant packages have been found, skipping installation of the plugin on" $_.Name "workspace" -ForegroundColor DarkYellow
            }
        } else {
            Write-Host "Package file could not be found, the plugin was not installed on the workspace" $_.Name -ForegroundColor DarkYellow
        }
    } else {
        Write-Host "Application" $_.name "name did not match with" $AppName ", skipping installation" -ForegroundColor DarkYellow
    }
}
Pop-Location
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { Stand } from "ui";
import { standData } from "../../../data/stand.data";
const meta: Meta<typeof Stand> = {
  title: "ui/Atomic/Molecules/Stand/Stand",
  component: Stand,
  tags: ["autodocs", "ui", "molecules", "stand"],
  parameters: {},
  argTypes: {},
};
export default meta;
type Story = StoryObj<typeof Stand>;
export const Schema: Story = {
  render: (args) => {
    return (
      <div className="px-4 py-6 lg:px-32 bg-neutral-50">
        <div className="grid gap-x-3 lg:grid-cols-6">
          <div className="lg:col-span-3 ">
            <Stand {...args} />
          </div>
        </div>
      </div>
    );
  },
  args: {
    stand: standData[0],
  },
};

// import { withActions } from "@storybook/addon-actions/decorator";
import type { Meta, StoryObj } from "@storybook/react";
import { Faq, IntroAward, useToggle } from "ui";
import { useState } from "react";

// More on how to set up stories at: https://storybook.js.org/docs/react/writing-stories/introduction
const meta: Meta<typeof Faq> = {
  title: "ui/Atomic/Molecules/Faq/Faq",
  component: Faq,
  tags: ["ui", "molecules", "common"],
  parameters: {},
  argTypes: {},
};

export default meta;

type Story = StoryObj<typeof Faq>;

export const Default: Story = {
  render: (args) => {
    return (
      <div className="w-screen h-screen flex justify-center bg-gray-300 items-center">
        <div className="max-w-3xl mx-auto w-full flex justify-center items-center">
          <Faq position={'horizontal'}>
          </Faq>
        </div>
      </div>
    );
  },
  args: {
    // value: "E-mailadres",
  },
};

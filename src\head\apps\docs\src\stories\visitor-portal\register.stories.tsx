import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { WithSitecoreContextDecorator } from "@uxbee/storybook-mock-sc/lib/WithSitecoreContextDecorator";
import { RegisterComponent } from "visitor-portal";

// More on how to set up stories at: https://storybook.js.org/docs/react/writing-stories/introduction
const meta: Meta<typeof RegisterComponent.Default> = {
  title: "RAI/Visitor Portal/Canvas/Register",
  component: RegisterComponent.Default,
  tags: ["autodocs", "rai", "ep"],
  argTypes: {},
  args: {
    backgroundUrl: "https://assets-prd.raicore.com/-/media/project/rai-amsterdam/rai/header-home/newhomepagebanner.jpg?rev=1c27181b571448ac905e0bca800fcf57"
  },
  decorators: [
    (Story, context) =>
      WithSitecoreContextDecorator(
        () => (
          <div className="h-[40rem]">
            <Story />
          </div>
        ),
        context,
        RegisterComponent.Default,
        false
      ),
  ],
};

export default meta;

type Story = StoryObj<typeof RegisterComponent.Default>;

export const Default: Story = {
  args: {
    onRegister: async (
      firstName: string,
      lastName: string,
      email: string,
      confirmEmail: string,
      password: string
    ) => {
      console.log("Start register");
      await new Promise<void>((resolve) => {
        setTimeout(() => {
          resolve();
        }, 3000);
      });
      console.log("Success register");
    },
  },
};

export const Fail: Story = {
    args: {
      onRegister: async (
        firstName: string,
        lastName: string,
        email: string,
        confirmEmail: string,
        password: string
      ) => {
        console.log("Start register");
        await new Promise<void>((resolve) => {
          setTimeout(() => {
            resolve();
          }, 3000);
        });
        throw "Email already registered"
      },
      privacyUrl: "/privacy-policy",
      tosUrl: "/term-of-service"
    },
  };

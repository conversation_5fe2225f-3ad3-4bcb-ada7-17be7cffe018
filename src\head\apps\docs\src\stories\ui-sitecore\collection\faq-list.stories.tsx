import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { WithSitecoreContextDecorator } from "@uxbee/storybook-mock-sc";
import { GridContainer, GridItem } from "ui";
import { FAQListComponent } from "ui-sitecore";

const meta: Meta<typeof FAQListComponent.Default> = {
  title: "ui-sitecore/components/collection/FAQList",
  component: FAQListComponent.Default,
  tags: ["autodocs", "rai", "ep"],
  argTypes: {},
  decorators: [
    (Story, context) =>
      WithSitecoreContextDecorator(
        () => (
          <div className="bg-white">
            <div className="container py-10">
              <GridContainer rowSpacing="04" columnSpacing="04">
                <GridItem xs="12">
                  <Story />
                </GridItem>
              </GridContainer>
            </div>
          </div>
        ),
        context,
        FAQListComponent.Default,
        false
      ),
  ],
};

export default meta;

type Story = StoryObj<typeof FAQListComponent.Default>;

export const Default: Story = {
  args: {
    fields: {
      data: {
        section: {
          id: "123",
          title: {
            jsonValue: {
              value: "Parking at Rai Amsterdam",
            }
          },
          list: {
            questions: [
              {
                id: "1",
                question: {
                  jsonValue: {
                    value: "What is the meaning of life?",
                  }
                },
                answer: {
                  jsonValue: {
                    value: "lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum."
                  },
                },
              },
              {
                id: "1",
                question: {
                  jsonValue: {
                    value: "What is the meaning of life?",
                  }
                },
                answer: {
                  jsonValue: {
                    value: "lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum."
                  },
                },
              },
            ],
          },
        },
      },
    },
    params: {
      Styles: "h2-title",
      IsExpandedByDefault: "1",
    }
  }
};

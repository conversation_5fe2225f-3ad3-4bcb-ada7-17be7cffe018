[CmdletBinding()]
param(
    [string]
    [ValidateNotNullOrEmpty()]
    $Package,
    [string]
    [ValidateNotNullOrEmpty()]
    $Template = "turbo-ui-template"
)
# Define the script context
$CommandDir = Get-Location
Push-Location -Path ..
$ContextDir = Get-Location
Pop-Location
$TurboPath = Join-Path $ContextDir "src/head"
# Import the used powershell functions
. $CommandDir/.powershell/Foundation.IO.ps1
. $CommandDir/.powershell/Foundation.JSON.ps1
# Set some variables
$TemplatesRoot = Join-Path $TurboPath "templates"
$Source = Join-Path $TemplatesRoot $Template
$PackagesRoot = Join-Path $TurboPath "packages"
$Target = Join-Path $PackagesRoot $package
$PackageFile = Join-Path $Target "package.json"

Write-Host "Creating a new ui package with name $Package based on template"
# Ensure that the packages folder is created
if(!(Test-Path -PathType container -Path $Target)) {
    New-Item -ItemType "directory" -Path $Target
}
# Copy the template files to the target packages folder
Copy-Files -Source $Source -Target $Target
# Set the name of the package module in the package.json
Update-JSON-Scalar-Value -JSONFile $PackageFile -Property "name" -Value $Package
# Clean the source folder
Remove-Item $Target\src\* -Recurse -Force
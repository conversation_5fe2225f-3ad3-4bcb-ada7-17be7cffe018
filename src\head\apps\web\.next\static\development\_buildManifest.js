self.__BUILD_MANIFEST = (function(a){return {__rewrites:{afterFiles:[{has:a,source:"\u002F:nextInternalLocale(default|en|nl|es|tr)\u002Fsitecore\u002Fapi\u002F:path*",destination:a},{has:a,source:"\u002F:nextInternalLocale(default|en|nl|es|tr)\u002F-\u002F:path*",destination:a},{has:a,source:"\u002F:nextInternalLocale(default|en|nl|es|tr)\u002F:virtualfolder\u002F-\u002F:path*",destination:a},{has:a,source:"\u002F:nextInternalLocale(default|en|nl|es|tr)\u002Fhealthz",destination:"\u002F:nextInternalLocale\u002Fapi\u002Fhealthz"},{has:a,source:"\u002F:nextInternalLocale(default|en|nl|es|tr)\u002Fsitecore\u002Fservice\u002F:path*",destination:a},{has:a,source:"\u002F:nextInternalLocale(default|en|nl|es|tr)\u002Frobots.txt",destination:"\u002F:nextInternalLocale\u002Fapi\u002Frobots"},{has:a,source:"\u002F:nextInternalLocale(default|en|nl|es|tr)\u002Fsitemap.xml",destination:"\u002F:nextInternalLocale\u002Fapi\u002Fsitemap"},{has:a,source:"\u002F:nextInternalLocale(default|en|nl|es|tr)\u002F:virtualfolder\u002Fsitemap.xml",destination:"\u002F:nextInternalLocale\u002Fapi\u002F:virtualfolder\u002Fsitemap"},{has:a,source:"\u002F:nextInternalLocale(default|en|nl|es|tr)\u002Ffeaas-render",destination:"\u002F:nextInternalLocale\u002Fapi\u002Fediting\u002Ffeaas\u002Frender"}],beforeFiles:[],fallback:[]},__routerFilterStatic:a,__routerFilterDynamic:a,"/_error":["static\u002Fchunks\u002Fpages\u002F_error.js"],"/[[...path]]":["static\u002Fchunks\u002Fpages\u002F[[...path]].js"],sortedPages:["\u002F_app","\u002F_error","\u002F[[...path]]"]}}(void 0));self.__BUILD_MANIFEST_CB && self.__BUILD_MANIFEST_CB()
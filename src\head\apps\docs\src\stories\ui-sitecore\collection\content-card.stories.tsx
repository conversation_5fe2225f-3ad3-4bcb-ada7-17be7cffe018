import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { WithSitecoreContextDecorator } from "@uxbee/storybook-mock-sc";
import { GridContainer, GridItem } from "ui";
import { ContentCardComponent } from "ui-sitecore";

const meta: Meta<typeof ContentCardComponent.Default> = {
  title: "ui-sitecore/components/collection/ContentCard",
  component: ContentCardComponent.Default,
  tags: ["autodocs", "rai", "ep"],
  argTypes: {},
  decorators: [
    (Story, context) =>
      WithSitecoreContextDecorator(
        () => (
          <div className="min-h-screen bg-gray-300">
            <div className="container py-10">
              <GridContainer rowSpacing="04" columnSpacing="04">
                <GridItem xs="12" lg="4" className="bg-white rounded-2xl overflow-hidden">
                  <Story />
                </GridItem>
                <GridItem xs="12" lg="4" className="bg-white rounded-2xl overflow-hidden">
                  <Story />
                </GridItem>
                <GridItem xs="12" lg="4" className="bg-white rounded-2xl overflow-hidden">
                  <Story />
                </GridItem>
              </GridContainer>
            </div>
          </div>
        ),
        context,
        ContentCardComponent.Default,
        false
      ),
  ],
};

export default meta;

type Story = StoryObj<typeof ContentCardComponent.Default>;

export const Default: Story = {
  args: {
    fields: {
      Title:{
        value: "Title"
      },
      Date:{
        value: "Date"
      },
      Subtitle:{
        value: "Subtitle"
      },
      Content:{
        value: "Lorem ipsum dolor sit amet consectetur adipisicing elit. Animi hic voluptatibus ut odi minima, omnis adipisci quo dolorem sed necessitatibus minus veniam corporis! Voluptate eaque impedit repellendus quos odio tempora! Lorem ipsum dolor sit amet consectetur adipisicing elit. Animi hic"
      },
      Image:{
        value: {
          src: "https://thumbs.dreamstime.com/b/lonely-tree-empty-green-field-copy-space-34718407.jpg",
          alt: "Image",
          width: "1024",
          height: "768"
        }
      },
      Link:{
        value: {
          href: "/",
          text: "Link"
        }
      },
      Icon:{
        value: "Icon"
      }
    },
    params: {
      DynamicPlaceholderId: "1",
    }
  }
};

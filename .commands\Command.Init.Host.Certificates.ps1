[CmdletBinding()]
param(
    [array]
    $HostNames = @()
)
Push-Location $PSScriptRoot
# Define the script context
$CommandDir = Get-Location
Push-Location -Path ..
$ContextDir = Get-Location
Pop-Location

. $CommandDir/.powershell/Foundation.Docker.ps1
. $CommandDir/.powershell/Foundation.Certificates.ps1

$EnvPath = Join-Path $ContextDir ".env"
# Define a regular expression pattern to extract all Host() values
$pattern = 'Host\(`([^`)]+)`\)'

# Determine the right filter
Push-Location $ContextDir
$DevelopmentSetup = Get-EnvFileVariable "DEVELOPMENT_SETUP" -Path $EnvPath
Pop-Location
if($DevelopmentSetup -eq "docker") {
    $dockerFileFilter = "docker-compose.override.rendering*.yml"
} else {
    $dockerFileFilter = "docker-compose.yml"
}
# Loop through all docker yaml files
Get-ChildItem -Path $ContextDir -Recurse -Filter $dockerFileFilter | ForEach-Object {
    $yamlConfig = Get-Content -Path $_.FullName -Raw

    # Use Select-String to find all matches in the YAML configuration
    $matchesOnPattern = $yamlConfig | Select-String -Pattern $pattern -AllMatches

    # Extract, process, and print all matched Host() values
    if ($matchesOnPattern.Matches.Count -gt 0) {
        $matchedHosts = $matchesOnPattern.Matches | ForEach-Object {
            $hostString = $_.Groups[1].Value
            if ($hostString -match '\$\{([^}]+)\}') {
                $hostString = Get-EnvFileVariable $Matches[1] -Path $EnvPath 
            }  
            $hostString
        }        
        # Loop through the matchedHosts variable
        foreach ($HostName in $matchedHosts) {
            # Process each host value here
            Add-Certificate -HostName $HostName -ProjectPath $ContextDir
            # This line does not work if the host file is open in any other program
            Add-HostsEntry $HostName
        }
    } else {
        Write-Host "No matching hosts found for docker." -ForegroundColor DarkYellow
    }
}

foreach ($HostName in $HostNames) {
    # Process each host value here
    Add-Certificate -HostName $HostName -ProjectPath $ContextDir
    # This line does not work if the host file is open in any other program
    Add-HostsEntry $HostName
}

$PSSciptPath = Join-Path $CommandDir "Command.Certificates.Combine.ps1"
& $PSSciptPath
Update-Traefik-CertsConfigFile -ProjectPath $ContextDir
Pop-Location
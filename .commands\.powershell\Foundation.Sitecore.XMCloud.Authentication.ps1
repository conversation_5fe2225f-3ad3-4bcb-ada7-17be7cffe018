function Invoke-XMCloud-Login() {
    param(
        [string]
        $Client_Id,
        [string]
        $Client_Secret,
        [switch]
        $Force,
        [string]
        $UserFile
    )
    if($Force) {
        Invoke-XMCloud-Logout -UserFile $UserFile
    }
    if(Test-Path -PathType Leaf -Path $UserFile) {
        $authenticated = Confirm-XMCloud-Login -UserFile $UserFile
    } else {
        $authenticated = $false
    }
    if(!$authenticated) {
        if($Client_Id -AND $Client_Secret) {
            dotnet sitecore cloud login --client-credentials --client-id $Client_Id --client-secret $Client_Secret --allow-write
        } else {
            dotnet sitecore cloud login
        }
    } else {
        Write-Debug "User is still authenticated, no need to reauthenticate"
    }
    # Always execute this command during login to ensure that the right organization id and name are set in the local environment file
    $organization_info = (dotnet sitecore cloud organization info --json) | ConvertFrom-Json
    [System.Environment]::SetEnvironmentVariable("XMC_ORGANIZATION_ID", $organization_info.Id, [System.EnvironmentVariableTarget]::Process)
    [System.Environment]::SetEnvironmentVariable("XMC_ORGANIZATION_Name", $organization_info.Name, [System.EnvironmentVariableTarget]::Process)
}

function Confirm-XMCloud-Login() {
    param(
        [string]
        $UserFile,
        [bool]
        $Reauthenticate = $true
    )
    if(Test-Path -PathType Leaf -Path $UserFile) {
        $sitecoreUserJson = Get-Content -Raw -Path $UserFile | ConvertFrom-Json
        # Extract relevant information
        if($sitecoreUserJson.endpoints.xmCloud.lastUpdated) {
            $lastUpdatedUtc = [DateTime]::Parse($sitecoreUserJson.endpoints.xmCloud.lastUpdated, [System.Globalization.CultureInfo]::InvariantCulture)
        } else {
            $lastUpdatedUtc = [DateTime]::UtcNow
        }
        $lastUpdatedLocal = $lastUpdatedUtc.ToLocalTime()
        if($sitecoreUserJson.endpoints.xmCloud.expiresIn) {
            $expiresIn = $sitecoreUserJson.endpoints.xmCloud.expiresIn
        } else {
            $expiresIn = -1000
        }
        $organizationIdVariable = $ENV:XMC_ORGANIZATION_ID
        $organizationIdFile = $sitecoreUserJson.endpoints.xmCloud.refreshTokenParameters.organization_id

        if($organizationIdVariable) {
            if(!($organizationIdVariable -eq $organizationIdFile)) {
                Write-Debug "Organization id in file does not match the organization id in the variable, please reautenticate"
                return $false
            }
        }

        # Calculate expiration time by adding expiresIn seconds to lastUpdated
        $expirationTime = $lastUpdatedLocal.AddSeconds($expiresIn)

        # Check if the token is still valid
        if ($expirationTime -gt (Get-Date)) {
            return $true
        } else {
            Write-Debug "User validation has expired."
            # Remove the user file
            Invoke-XMCloud-Logout -UserFile $UserFile
        }
    } else {
        Write-Debug "User file was not found, therefor the user is marked as logged out."        
    }
    if($Reauthenticate) {
        Invoke-XMCloud-Login -Client_Id $ENV:XMC_CLIENT_ID -Client_Secret $ENV:XMC_CLIENT_SECRET -UserFile $UserFile -Force
        return $true
    }
    return $false
}

function Invoke-XMCloud-Logout() {
    param(
        [string]
        $UserFile
    )
    $fileExits = (Test-Path -PathType Leaf -Path $UserFile)
    if($fileExits) {
        # Removes the file from disk, this will logout the user
        Remove-Item $UserFile
    } else {
        # This opens a browser and logs the user out, removing the file from disk is a better option
        dotnet sitecore cloud logout
    }
}
[CmdletBinding()]
param(
    [string]
    $ProjectName
)
Push-Location $PSScriptRoot

$CommandDir = Get-Location
Push-Location -Path ..
$ContextDir = Get-Location
Pop-Location
. $CommandDir/.powershell/Foundation.Sitecore.XMCloud.ps1

$UserFile = Join-Path $ContextDir "/.sitecore/user.json"

$IsAuthenticated = Confirm-XMCloud-Login -UserFile $UserFile

if($IsAuthenticated) {
    $projectId = Remove-Project-From-Organization -ProjectName $ProjectName
    if($projectId) {
        Write-Host "The project $ProjectName ($projectId) could not be removed, check if all environments have been removed." -ForegroundColor Red
    }
} else {
    Write-Host "User is not authenticated, no actions can be executed" -ForegroundColor Red
}

Pop-Location
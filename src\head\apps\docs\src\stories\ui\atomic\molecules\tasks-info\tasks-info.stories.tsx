import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { TasksInfo } from "ui";
import { tasksInfoData } from "../../../data/tasks-info.data";
const meta: Meta<typeof TasksInfo> = {
  title: "ui/Atomic/Molecules/TasksInfo/TasksInfo",
  component: TasksInfo,
  tags: ["autodocs", "ui", "molecules", "task-info"],
  parameters: {},
  argTypes: {},
};
export default meta;
type Story = StoryObj<typeof TasksInfo>;
export const Schema: Story = {
  render: (args) => {
    return (
      <div className="px-4 py-6 lg:px-20 bg-neutral-50">
        <div className="grid gap-3 lg:grid-cols-6">
          <div className="lg:col-span-6">
            <TasksInfo {...args} />
          </div>
        </div>
      </div>
    );
  },
  args: {
    tasks: tasksInfoData,
    language: "en",
    hideCategories: false
  },
};

[CmdletBinding()]
param(
    [string]
    [ValidateNotNullOrEmpty()]
    $Workspace,
    [string]
    [ValidateNotNullOrEmpty()]
    $Package
)
# Define the script context
#$CommandDir = Get-Location
Push-Location -Path ..
$ContextDir = Get-Location
Pop-Location
$TurboPath = Join-Path $ContextDir "src/head"

Write-Host "Installing $Package on $Workspace" -ForegroundColor DarkYellow
Push-Location $TurboPath
# Check if the package is a ui package
if($Package.StartsWith("ui")) {
    # Do a normal npm install
    npm install $Package --workspace $Workspace
    # Copy the plugin
    # Ensure that the plugin is not installed on the design system

} else {
    # Do a normal npm install
    npm install $Package --workspace $Workspace
}
Pop-Location
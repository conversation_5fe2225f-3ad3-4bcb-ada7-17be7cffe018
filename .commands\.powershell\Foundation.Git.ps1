function Get-Repository-By-Cloning() {
	param(
		[ValidateNotNullOrEmpty()]
		[string]
		$GitLocation,
		[ValidateNotNullOrEmpty()]
		[string]
		$Destination
	)
	# Execute the cloning insde a process, to catch any succes output errors
	BEGIN {
		$WarningPreference = 'SilentlyContinue'
		$ErrorActionPreference = 'SilentlyContinue'
	}
	PROCESS {
		# Only execute the cloning when the target folder does not exist
		If(!(Test-Path -PathType container -Path $Destination)) {
			# Execute the actual cloning
			git clone $GitLocation $Destination
		}
	}
	END {
	}
}
function Add-Scoped-Repository() {
	param(
		[string]
		$Scope = "uxbee",
		[string]
		$RegistryUrl = "https://pkgs.dev.azure.com/uxbee/_packaging/uxbee-private.npm/npm/registry/",
		[string]
		$Email = "<EMAIL>",
		[string]
		$Username = "uxbee",
		[string]
		# Documentation on PatKey generation
		# Only needed when you do not have Windows machine, leaving this varaible empty will automatically authenticate you with vsts-npm-auth
		# https://learn.microsoft.com/en-us/azure/devops/organizations/accounts/use-personal-access-tokens-to-authenticate?view=azure-devops&tabs=Windows#create-a-pat
		$PatKey,
		[string]
		# Default is user
		$ContextFolder = $env:USERPROFILE,
		[string]
		$EnvFileLocation,
		[string]
		$NpmRegistryConfigFile = (Join-Path $ContextFolder ".npmrc.local")
	)
	# Ensure that the scoped repository is added
	if(!$PatKey) {
		$Scope = -join('@', $Scope , ":registry")
		npm config set $Scope $RegistryUrl
	}
	If(!(Test-Path -PathType Leaf -Path $NpmRegistryConfigFile)) {
		Write-Host "Creating" $NpmRegistryConfigFile -ForegroundColor DarkYellow
		New-Item $NpmRegistryConfigFile
		Set-Content $NpmRegistryConfigFile 'auto-install-peers=true'
	}

	If(Test-Path -PathType Leaf -Path $NpmRegistryConfigFile) {
		$content = get-content $NpmRegistryConfigFile
		$AlwaysAuth = "always-auth=true"
		if(!($content -Contains $alwaysAuth)) {			
			@($AlwaysAuth) + $content | Set-Content $NpmRegistryConfigFile
		}
		$BeginComment = "; begin auth token for scope registry $Scope"
		$EndComment = "; end auth token for scope registry $Scope"
		if($PatKey) {
			$base64Pat = [Convert]::ToBase64String([System.Text.Encoding]::UTF8.GetBytes($PatKey))
			# variables for writing to the registry config file when a pat token is supplied
			$RegistryPath = ([regex]"(?=((?<=https:).*)).*(?=.*\/registry\/)").matches($RegistryUrl).Value
			if($content -Contains $BeginComment) {
				# Remove the auth token for reauthoization
				(Get-Content -Path $NpmRegistryConfigFile -Raw) -replace "($BeginComment)(.*(\n))*($EndComment)", "" | Set-Content -Path $NpmRegistryConfigFile
				((Get-Content $NpmRegistryConfigFile -Raw) -replace "(?m)^\s*`r`n",'').trim() | Set-Content $NpmRegistryConfigFile
			}
			# https://learn.microsoft.com/en-us/azure/devops/artifacts/npm/scopes?view=azure-devops#set-up-credentials
			Add-Content -Path $NpmRegistryConfigFile -Value "`n"
			Add-Content -Path $NpmRegistryConfigFile -Value $BeginComment
			Add-Content -Path $NpmRegistryConfigFile -Value ("{0}={1}" -f $Scope, $RegistryUrl)
			Add-Content -Path $NpmRegistryConfigFile -Value ("{0}/registry/:username={1}" -f $RegistryPath, $Username)
			Add-Content -Path $NpmRegistryConfigFile -Value ("{0}/registry/:_password={1}" -f $RegistryPath, $base64Pat)
			Add-Content -Path $NpmRegistryConfigFile -Value ("{0}/registry/:email={1}"-f $RegistryPath, $Email)
			Add-Content -Path $NpmRegistryConfigFile -Value $EndComment
			((Get-Content $NpmRegistryConfigFile -Raw) -replace "(?m)^\s*`r`n",'').trim() | Set-Content $NpmRegistryConfigFile
		} else {
			# Default is user
			# https://docs.npmjs.com/cli/v8/commands/npm-config#location
			npm config get $Scope
			# Authentication
			npm -y install -g vsts-npm-auth
			Write-Host "Updating the file $NpmRegistryConfigFile" -ForegroundColor DarkYellow
			if(!($content -Contains $BeginComment)) {
				Add-Content -Path $NpmRegistryConfigFile -Value "`n"
				Add-Content -Path $NpmRegistryConfigFile -Value $BeginComment
				Add-Content -Path $NpmRegistryConfigFile -Value ("{0}={1}" -f $Scope, $RegistryUrl)
				Add-Content -Path $NpmRegistryConfigFile -Value $EndComment
				((Get-Content $NpmRegistryConfigFile -Raw) -replace "(?m)^\s*`r`n",'').trim() | Set-Content $NpmRegistryConfigFile
			}
			vsts-npm-auth -C $NpmRegistryConfigFile -T $NpmRegistryConfigFile
		}
		# Extract the username, password and email from the local file and add it to the .env file
		$ExtractedUsername = Get-RegistryValueFromFile -Key "username" -ContextFolder $ContextFolder -NpmRegistryConfigFile $NpmRegistryConfigFile
		$ExtractedPassword = Get-RegistryValueFromFile -Key "_password" -ContextFolder $ContextFolder -NpmRegistryConfigFile $NpmRegistryConfigFile
		$ExtractedEmail = Get-RegistryValueFromFile -Key "email" -ContextFolder $ContextFolder -NpmRegistryConfigFile $NpmRegistryConfigFile

		Set-EnvFileVariable "XMV_UXBEE_REGISTRY_USERNAME" -Value $ExtractedUsername -Path $EnvFileLocation
		Set-EnvFileVariable "XMV_UXBEE_REGISTRY_EMAIL" -Value $ExtractedEmail -Path $EnvFileLocation
		Set-EnvFileVariable "XMV_UXBEE_REGISTRY_PATKEY" -Value $ExtractedPassword -Path $EnvFileLocation

		Write-Host "Authentication for private repository is completed" -ForegroundColor DarkYellow
	} else {
		Write-Host "npm regirstry configuration file was not found, authentication could not be completed." -ForegroundColor Red
	}
}

function Get-RegistryValueFromFile() {
	param(
		[string]
		# Default is user
		$ContextFolder = $env:USERPROFILE,
		[string]
		$NpmRegistryConfigFile = (Join-Path $ContextFolder ".npmrc.local"),
		[string]
		$Key = "_password"
	)
	Write-Host $NpmRegistryConfigFile -ForegroundColor DarkYellow
	$content = get-content $NpmRegistryConfigFile
	# Define a regex pattern to match the password
	$pattern = -join('(?<=', $Key, '=)[^\r\n]+')

	# Use Select-String to find the match in the text
	$match = $content | Select-String -Pattern $pattern

	# Extract the matched password
	return $match.Matches.Value.Trim()
}

function Invoke-Reinitialize-Project-GitHistory() {
    param(
        [ValidateNotNullOrEmpty()]
        [string]
        $ProjectPath
    )
    Push-Location (Join-Path "projects" $ProjectName)

    Write-Host "Remove existing project Git history"
    Remove-Item -LiteralPath ".git" -Force -Recurse

	Write-Host "Initialize Git"
    git init --initial-branch=master
    
    Pop-Location
}

function Invoke-CheckoutAndCommit-AllExistingFiles() {
	param(
		[ValidateNotNullOrEmpty()]
		[string]
		$ProjectPath,
		[ValidateNotNullOrEmpty()]
		[string]
		$CommitMessage
	)
	Push-Location (Join-Path "projects" $ProjectName)

	git add --all
    git commit -am $CommitMessage
    Start-Sleep -s 1

	Pop-Location
}

function Push-ToRemoteGitRepository(){
    param(
        [ValidateNotNullOrEmpty()]
        [string]
        $ProjectPath,
		[ValidateNotNullOrEmpty()]
        [string]
        $RemoteGitUrl,
        [Object]
        $Config
    )
	Push-Location (Join-Path "projects" $ProjectName)

    $GitPat = $config.project.repository.gitPatKey
    $Base64Pat = [Convert]::ToBase64String([System.Text.Encoding]::UTF8.GetBytes("`:$GitPat"))

	Write-Host "Add remote Git repository url $RemoteGitUrl"
    git -c http.extraHeader="Authorization: Basic $Base64Pat" remote add origin $RemoteGitUrl.Trim()
    Start-Sleep -s 1

	Write-Host "Push changes to remote Git repository"
    git -c http.extraHeader="Authorization: Basic $Base64Pat" push -u origin --all --force

	Pop-Location
}
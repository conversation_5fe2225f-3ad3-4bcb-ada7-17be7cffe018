import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { Header, headerMock } from "ui";

// More on how to set up stories at: https://storybook.js.org/docs/react/writing-stories/introduction
const meta: Meta<typeof Header> = {
  title: "ui/Atomic/Organisms/web/Header/Header",
  component: Header,
  tags: ["autodocs", "ui"],
  args: {
    header: headerMock
  },
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/react/configure/story-layout
    actions: { argTypesRegex: "^on.*" },
    rootAttributesTooltip: true,
  },
};

export default meta;

type Story = StoryObj<typeof Header>;

export const Schema: Story = {
  render: (args) => {
    return (
      <>
        <div>
          <div className="flex flex-row  space-x-2 items-center">
            <Header {...args} />
          </div>
        </div>
      </>
    );
  },
};
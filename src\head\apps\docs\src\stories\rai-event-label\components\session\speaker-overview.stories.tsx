import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { WithSitecoreContextDecorator } from "@uxbee/storybook-mock-sc";
import { SpeakerOverviewComponent } from "rai-event-label";
import { SpeakerModel } from "rai-event-label/src/components/session/SpeakerModel";

const meta: Meta<typeof SpeakerOverviewComponent.Default> = {
    title: "RAI/Event Label/Session/SpeakerOverview",
    component: SpeakerOverviewComponent.Default,
    tags: ["autodocs", "rai", "ep"],
    argTypes: {},
    decorators: [
        (Story, context) =>
            WithSitecoreContextDecorator(
                () => (
                    <div className="min-h-screen bg-[#fafafa]">
                        <Story />
                    </div>
                ),
                context,
                SpeakerOverviewComponent.Default,
                false
            ),
    ],
};

export default meta;

type Story = StoryObj<typeof SpeakerOverviewComponent.Default>;

export const Default: Story = {
    args: {
        filters: [
            {
                groupName: "Locations", filters: [
                    "Hall 3",
                    "Underground 4"
                ]
            },
            {
                groupName: "Speaker", filters: [
                    "Mr. Yuu",
                    "Checkbox"
                ]
            }
        ],
        loadData: async (query: string, filters: string[]) => {
            return [
                {
                    image: "https://www.horecava.nl/-/media/41db06fd40294471a7f58a334d642b7e.jpg",
                    name: "Pernilla La Lau",
                    position: "Moderator",
                    organization: "Opening Horecava 2024",
                }, {
                    image: "https://www.horecava.nl/-/media/41db06fd40294471a7f58a334d642b7e.jpg",
                    name: "Pernilla La Lau",
                    position: "Moderator",
                    organization: "Opening Horecava 2024",
                },
                {
                    image: "https://www.horecava.nl/-/media/41db06fd40294471a7f58a334d642b7e.jpg",
                    name: "Pernilla La Lau",
                    position: "Moderator",
                    organization: "Opening Horecava 2024",
                },
                {
                    image: "https://www.horecava.nl/-/media/41db06fd40294471a7f58a334d642b7e.jpg",
                    name: "Pernilla La Lau",
                    position: "Moderator",
                    organization: "Opening Horecava 2024",
                },
                {
                    image: "https://www.horecava.nl/-/media/41db06fd40294471a7f58a334d642b7e.jpg",
                    name: "Pernilla La Lau",
                    position: "Moderator",
                    organization: "Opening Horecava 2024",
                }
            ] as SpeakerModel[]
        }
    },
};

function Get-Project-Id-by-Name() {
    param(
        [string]
        $ProjectName,
        [bool]
        $ExactMatch = $false,
        [bool]
        $Force = $false
    )
    $key = (-join('XMC_PROJECT_',$ProjectName) -replace ('\W', '_')).ToUpper().Trim()
    if(!$Force) {
        $projectId = [System.Environment]::GetEnvironmentVariable($key, [System.EnvironmentVariableTarget]::Process)
    }
    if($projectId) {
        Write-Debug "Matching project id for project name ($ProjectName) was found in your local environment variables, this project id ($projectId) is used."
        return $projectId
    } else {
        $Projects = (dotnet sitecore cloud project list --json) | ConvertFrom-Json
        foreach ($project in $Projects) {
            $projectMatches = $false
            if($ExactMatch) {
                $projectMatches = ($project.name -eq $ProjectName)
            } else {
                $projectMatches = ($project.name -match $ProjectName)
            }
            if($projectMatches) {
                # Add the variable to the local environment variables for faster retrievement
                $projectId = $project.id
                [System.Environment]::SetEnvironmentVariable($key, $projectId, [System.EnvironmentVariableTarget]::Process)
                return $projectId
            }
        }
    }
    [System.Environment]::SetEnvironmentVariable($key, '', [System.EnvironmentVariableTarget]::Process)
    Write-Debug "No project found with the name $ProjectName in the active organization"
}

function Add-Project-To-Organization() {
    param(
        [string]
        $ProjectName
    )
    $projectId = Get-Project-Id-by-Name -ProjectName $ProjectName -ExactMatch $true
    if(!$projectId) {
        # The project does not exist in the organization, it can be created
        $project = dotnet sitecore cloud project create --name $ProjectName --json | ConvertFrom-Json
        if(Assert-Response -JSON $project) {
            return $project.id
        } else {
            Write-Debug $project
        }
    } else {
        Write-Debug "Project with the name already exits, creation is skipped."
    }
    return $projectId
}

function Remove-Project-From-Organization() {
    param(
        [string]
        $ProjectName
    )
    $projectId = Get-Project-Id-by-Name -ProjectName $ProjectName -ExactMatch $true
    # First check if the project has environments, if so the project can not be deleted, the environments should be deleted first.
    $projects = dotnet sitecore cloud environment list --project-id $projectId --json | ConvertFrom-Json
    if($projects.Count -eq 0) {
        # If the projects has no environments, it can be deleted as it no longer have any meaning
        Write-Debug "This function has not been implemented yet, removal of the project is skipped"
        #dotnet sitecore cloud project delete --project-id $projectId
        # Force updating the project id as it should be removed
        $projectId = Get-Project-Id-by-Name -ProjectName $ProjectName -Force $true
    } else {
        Write-Debug "The project $ProjectName ($projectId) has active environments, remove all environments before the project can be deleted"
    }
    return $projectId
}
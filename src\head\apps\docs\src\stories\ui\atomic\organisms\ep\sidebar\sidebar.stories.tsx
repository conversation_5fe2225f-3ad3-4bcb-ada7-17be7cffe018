import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { LanguageItems, SessionProvider, Sidebar } from "ui";
import { EPSidebarData1 } from "../../../../data/ep-sidebar.data";
const meta: Meta<typeof Sidebar> = {
  title: "ui/Atomic/Organisms/Sidebar/Sidebar",
  component: Sidebar,
  tags: ["autodocs", "ui", "molecules", "sidebar"],
  parameters: {},
  argTypes: {},
};
export default meta;
type Story = StoryObj<typeof Sidebar>;
export const Schema: Story = {
  render: (args) => {
    const languageSwitch: LanguageItems = {
      currentLanguageCode: "en",
      languages: []
    }

    return (
      <div className="h-[40rem]">
        <SessionProvider>
          <Sidebar siteNavigation={args.siteNavigation} languageSwitch={languageSwitch} />
        </SessionProvider>
      </div>
    );
  },
  args: {
    siteNavigation: EPSidebarData1.fields.data.siteNavigation
  },
};

import type { <PERSON>a, StoryObj } from "@storybook/react";
import { HomePage } from "ui";
const meta: Meta<typeof HomePage> = {
  title: "ui/Atomic/Pages/Home/HomePage",
  component: HomePage,
  tags: ["autodocs", "ui", "molecules", "home-page"],
  parameters: {},
  argTypes: {},
};
export default meta;
type Story = StoryObj<typeof HomePage>;
export const Schema: Story = {
  render: (args) => {
    return (
      <div className="h-[40rem]">
        <HomePage {...args} />
      </div>
    );
  },
  args: {},
};

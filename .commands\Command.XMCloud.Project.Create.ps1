[CmdletBinding()]
param(
    [string]
    $ProjectName
)
Push-Location $PSScriptRoot

$CommandDir = Get-Location
Push-Location -Path ..
$ContextDir = Get-Location
Pop-Location
. $CommandDir/.powershell/Foundation.Sitecore.XMCloud.ps1

$UserFile = Join-Path $ContextDir "/.sitecore/user.json"

$IsAuthenticated = Confirm-XMCloud-Login -UserFile $UserFile

if($IsAuthenticated) {
    $projectId = Get-Project-Id-by-Name -ProjectName $ProjectName -ExactMatch $true
    if(!$projectId) {
        $projectId = Add-Project-To-Organization -ProjectName $ProjectName
    } else {
        Write-Host "Project already exists, creation has been skipped" -ForegroundColor Cyan
    }
    dotnet sitecore cloud project info --project-id $projectId
} else {
    Write-Host "User is not authenticated, no actions can be executed" -ForegroundColor Red
}


Pop-Location
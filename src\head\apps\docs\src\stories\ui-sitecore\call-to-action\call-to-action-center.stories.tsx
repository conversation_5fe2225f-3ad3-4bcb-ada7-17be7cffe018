import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { WithSitecoreContextDecorator } from "@uxbee/storybook-mock-sc/lib/WithSitecoreContextDecorator";
import { CallToActionComponent } from "ui-sitecore";
import {
  CallToActionDefaultData1,
  CallToActionDefaultDataNoDatasource
} from "./../data/call-to-action.data";

// More on how to set up stories at: https://storybook.js.org/docs/react/writing-stories/introduction
const meta: Meta<typeof CallToActionComponent.CtaCenter> = {
  title: "ui-sitecore/components/call-to-action/center",
  component: CallToActionComponent.CtaCenter,
  tags: ["autodocs", "ui-sitecore", "sitecore"],
  argTypes: {},
  decorators: [
    (Story, context) =>
      WithSitecoreContextDecorator(Story, context, CallToActionComponent.CtaCenter, false),
  ],
};
export default meta;

type Story = StoryObj<typeof CallToActionComponent.CtaCenter>;

// More on writing stories with args: https://storybook.js.org/docs/react/writing-stories/args
export const Default: Story = {
  args: CallToActionDefaultData1,
};

export const DefaultDataNoDatasource: Story = {
  args: CallToActionDefaultDataNoDatasource,
};

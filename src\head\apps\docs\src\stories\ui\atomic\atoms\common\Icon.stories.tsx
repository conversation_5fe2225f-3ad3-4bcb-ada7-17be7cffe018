import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { Icon } from "ui";

// More on how to set up stories at: https://storybook.js.org/docs/react/writing-stories/introduction
const meta: Meta<typeof Icon> = {
  title: "ui/Atomic/Atoms/Common/Icon",
  component: Icon,
  tags: ["autodocs", "ui", "atoms"],
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/react/configure/story-layout
    //layout: 'centered',
    rootAttributes: [
      {
        root: "html",
        attribute: "data-theme",
        defaultState: {
          name: "RAI",
          value: "rai",
        },
        states: [
          {
            name: "Greentech",
            value: "greentech",
          },
        ],
      },   
    ],
  },

  // Test
  //decorators: [withActions],
  argTypes: {

    IconName: {
      options: ["Home", "Folder", "Headset", "Lightbulb", "InformationCircle", "UserProfile", "Twitter", "CustomKit"],
      control: { type: "radio" },
      defaultValue: "PiHeadsetFill",
    },
    variant: {
      options: ["primary", "secondary"],
      control: { type: "radio" },
      defaultValue: "primary",
    },
    size: {
      options: ["small", "default", "large", "extralarge"],
      control: { type: "radio" },
      defaultValue: "default",
    },
  },
  //onClick: { action: 'clicked' }
  //},
};

export default meta;

type Story = StoryObj<typeof Icon>;

// More on writing stories with args: https://storybook.js.org/docs/react/writing-stories/args
export const Default: Story = {
  args: {
    variant: 'primary',
    size: 'default',
    IconName: 'Folder',
    className: ''
  },


};

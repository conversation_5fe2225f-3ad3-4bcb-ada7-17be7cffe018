import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { WithSitecoreContextDecorator } from "@uxbee/storybook-mock-sc";
import { SliderComponent } from "ui-sitecore";

const meta: Meta<typeof SliderComponent.Default> = {
  title: "ui-sitecore/components/composite/slider",
  component: SliderComponent.Default,
  tags: ["autodocs", "rai", "ep"],
  argTypes: {},
  decorators: [
    (Story, context) =>
      WithSitecoreContextDecorator(
        () => (
          <div className="min-h-screen bg-[#fafafa]">
            <Story />
          </div>
        ),
        context,
        SliderComponent.Default,
        false
      ),
  ],
};

export default meta;

type Story = StoryObj<typeof SliderComponent.Default>;

export const Default: Story = {
  args: {
    fields: {
      data: {
        datasource: {
          children: {
            results: [
              {
                id: "5D706580C5C6451FACB84E1DF7A2713E",
                template: {
                  name: "XBE_Image",
                },
                Image: {
                  jsonValue: {
                    value: {
                      src: "https://assets-prd.raicore.com/-/media/project/rai-amsterdam/negenmaandenbeurs/nmb/afbeeldingen/2024/selectie/fotograaf-damon-rigter---rigtercreatives---negenmaandenbeurs-dag-1---14_02_2024-078-min.jpg",
                      alt: "image",
                      width: "1380",
                      height: "1080",
                    },
                  },
                },
                ImageCaption: {
                  jsonValue: {
                    value: "Hello Image number 1",
                  },
                },
                ImageLink: {
                  jsonValue: {
                    value: {
                      href: "",
                    },
                  },
                },
              },
              {
                id: "5D706580C5C6451FACB84E1DF7A2713E",
                template: {
                  name: "XBE_Image",
                },
                Image: {
                  jsonValue: {
                    value: {
                      src: "https://assets-prd.raicore.com/-/media/project/rai-amsterdam/negenmaandenbeurs/nmb/afbeeldingen/2024/fotograaf-damon-rigter---rigtercreatives---negenmaandenbeurs-dag-2---15-02-2024-168-min.jpg?rev=-1&hash=7AD3F36284A847DA01C7154E9FFB54B3",
                      alt: "image",
                      width: "1380",
                      height: "1080",
                    },
                  },
                },
                ImageCaption: {
                  jsonValue: {
                    value: "",
                  },
                },
                ImageLink: {
                  jsonValue: {
                    value: {
                      href: "",
                    },
                  },
                },
              },
              {
                id: "5D706580C5C6451FACB84E1DF7A2713E",
                template: {
                  name: "XBE_Image",
                },
                Image: {
                  jsonValue: {
                    value: {
                      src: "https://assets-prd.raicore.com/-/media/project/rai-amsterdam/negenmaandenbeurs/nmb/afbeeldingen/2024/selectie/fotograaf-damon-rigter---rigtercreatives---negenmaandenbeurs-dag-2---15-02-2024-071-min.jpg?rev=-1&hash=7A3C649E33802FB5AD54FC39D678AC69",
                      alt: "image",
                      width: "1380",
                      height: "1080",
                    },
                  },
                },
                ImageCaption: {
                  jsonValue: {
                    value: "",
                  },
                },
                ImageLink: {
                  jsonValue: {
                    value: {
                      href: "",
                    },
                  },
                },
              },
              {
                id: "5D706580C5C6451FACB84E1DF7A2713E",
                template: {
                  name: "XBE_Image",
                },
                Image: {
                  jsonValue: {
                    value: {
                      src: "https://assets-prd.raicore.com/-/media/project/rai-amsterdam/negenmaandenbeurs/nmb/afbeeldingen/2024/slider/1.png?rev=bc8c8549be344b398c859629c61ae4f9&hash=13DE3C59AA3DA40417E9BB4BDD24CB8D",
                      alt: "image",
                      width: "1380",
                      height: "1080",
                    },
                  },
                },
                ImageCaption: {
                  jsonValue: {
                    value: "",
                  },
                },
                ImageLink: {
                  jsonValue: {
                    value: {
                      href: "",
                    },
                  },
                },
              },
            ],
          },
          field: {
            title: {
              value: "Example Slider",
            },
          },
        },
      },
    },
    params: {
      IsAdaptiveHeight: "0",
      IsAutoplay: "1",
      AutoplaySpeed: "2000",
      ShowArrows: "1",
      ShowDots: "1",
      IsFade: "0",
      IsInfiniteScroll: "1",
      PauseOnHover: "0",
      SlidesToShowDesktop: "4",
      SlidesToShowMobile: "1",
      SpaceBetweenSlides: "10",
      PerMove: "1",
      FieldNames: "Default",
      DynamicPlaceholderId: "13",
      ShowCTAButton: "1",
      PrevIconName: "fas fa-angle-left",
      NextIconName: "fas fa-angle-right",
      IconSize: "3xl",
      styles: " ",
    },
  },
};

import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { ContactPage } from "ui";
const meta: Meta<typeof ContactPage> = {
  title: "ui/Atomic/Pages/Contact/ContactPage",
  component: ContactPage,
  tags: ["autodocs", "ui", "molecules", "home-page"],
  parameters: {},
  argTypes: {},
};
export default meta;
type Story = StoryObj<typeof ContactPage>;
export const Schema: Story = {
  render: (args) => {
    return (
      <ContactPage {...args} />
    );
  },
  args: {},
};

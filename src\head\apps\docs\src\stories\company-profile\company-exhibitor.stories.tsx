import type { <PERSON>a, StoryObj } from "@storybook/react";
import { CompanyExhibitor } from "company-profile";
import { ExhibitorListModelMock, FirstLettersMock } from 'company-profile-data';

// More on how to set up stories at: https://storybook.js.org/docs/react/writing-stories/introduction
const meta: Meta = {
  title: "RAI/Company Profile/CompanyExhibitor",
  component: CompanyExhibitor,
  tags: ["autodocs", "company-profile"],
  args: {
    firstLetterFilter: FirstLettersMock,
    optionalFilterOptions: [
      { value: 'value1', label: 'Label1' }
    ],
    onRequestData: async (
      hostname: string,
      filter?: string,
      optionalFilter?: string,
      sort?: string,
      search?: string,
      pageToLoad?: number) => {
      await new Promise<void>(resolve => {
        setTimeout(() => {
          resolve();
        }, 3000);
      });
      return {
        ...ExhibitorListModelMock,
        exhibitors: ExhibitorListModelMock.exhibitors.map((e) => ({
          ...e,
          name: `PAGE: ${pageToLoad} - ${e.name}`
        }))
      };
    },
    premiumExhibitors: ExhibitorListModelMock.exhibitors,
    currentLanguage: 'en',
    placeholderLogo: 'https://assets-prd.raicore.com/amsterdam/-/media/project/rai-amsterdam/intertraffic/logo/itd-logo/internal/ita-intertraffic-logo.png?h=62&iar=0&w=300&rev=5a3701f6ef31471baf506db9c72e5660&hash=100134F472536BE089E36E3D354AB063',
    params: {
      ShowAllImages: true,
      ShowPremium: true,
      ShowSearch: true,
      ShowCategory: true,
      ShowAlphabetFilter: true,
      HideStandNumber: true,
      ShowCountry: true,
      ShowDescription: true,
      MobileLayout: 'two-column-mobile',
      ListingPage: 'Paging',
    }
  },
  parameters: {
    actions: { argTypesRegex: "^on.*" },
    rootAttributesTooltip: true,
  },
};

export default meta;

type Story = StoryObj<typeof CompanyExhibitor>;

export const Schema: Story = {
  render: (args) => {
    return (
      <>
        <div>
          <div className="flex flex-row  space-x-2 items-center">
            <CompanyExhibitor {...args} />
          </div>
        </div>
      </>
    );
  },
};
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { NotificationCard, useToggle } from "ui";
const meta: Meta<typeof NotificationCard> = {
  title: "ui/Atomic/Molecules/NotificationCard/NotificationCard",
  component: NotificationCard,
  tags: ["autodocs", "ui", "molecules", "NotificationCard"],
  parameters: {},
  argTypes: {},
};
export default meta;
type Story = StoryObj<typeof NotificationCard>;
export const Schema: Story = {
  render: (args) => {

    const productName = 'THOA portable wireless dim!'
    const productDate = 'September 15 2024'
    const awardName = 'DAME Design Awards'

    const [notificationOpen,handleNotificationOpen] = useToggle()

    const onClose=()=>{
        console.log('on close is running')
        handleNotificationOpen()
    }

    return (
      <div className="h-screen w-screen flex justify-center items-center bg-gray-300">
        <div className="max-w-4xl mx-auto w-full flex flex-col justify-center items-center">
            {
                !notificationOpen && (
                    <div onClick={handleNotificationOpen}>BUTTON OPEN</div>
                )
            }
            <NotificationCard {...args}
             onClose={onClose} 
             awardName={awardName} 
             productName={productName}
             productDate={productDate}
             notificationOpen={notificationOpen}
            />
        </div>
      </div>
    );
  },
  args: {
  },
};

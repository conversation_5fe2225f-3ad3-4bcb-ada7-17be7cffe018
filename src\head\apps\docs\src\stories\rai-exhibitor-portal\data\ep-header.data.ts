export const EPHeaderData1 = {
    params: {
      name: "EPHeader",
      componentName: "EPHeader",
      tag: "h1",
    },
    rendering: {
      uid: "{00000000-0000-0000-0000-000000000000}",
      componentName: "EPHeader",
      dataSource: "{00000000-0000-0000-0000-000000000000}",
    },
    fields: {
        data: {
            "siteNavigation": {
                "logos": {
                    "results": []
                },
                "links": {
                    "results": [
                        {
                            "name": "Profile",
                            "navigationDropDownStateClosedIconName": {
                                "value": "FaChevronDown"
                            },
                            "navigationDropDownStateOpenedIconName": {
                                "value": "FaChevronUp"
                            },
                            "children": {
                                "results": [
                                    {
                                        "link": {
                                            "jsonValue": {
                                                "value": {
                                                    "href": "https://ep.raicore.com",
                                                    "linktype": "external",
                                                    "url": "https://ep.raicore.com",
                                                    "anchor": "",
                                                    "target": ""
                                                }
                                            }
                                        },
                                        "navigationTitle": {
                                            "value": "Switch Event"
                                        },
                                        "navigationItemIconName": {
                                            "value": "fa-light fa-arrows-repeat"
                                        },
                                        "children": {
                                            "results": []
                                        }
                                    }
                                ]
                            }
                        }
                    ]
                }
            }
        }
    }
}
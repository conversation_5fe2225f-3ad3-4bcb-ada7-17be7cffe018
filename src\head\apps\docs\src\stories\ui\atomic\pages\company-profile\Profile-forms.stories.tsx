// import { withActions } from "@storybook/addon-actions/decorator";
import type { Meta, StoryObj } from "@storybook/react";
import { useEffect, useState } from "react";
import { ProfileForms } from "ui";

// More on how to set up stories at: https://storybook.js.org/docs/react/writing-stories/introduction
const meta: Meta<typeof ProfileForms> = {
  title: "ui/Atomic/Pages/Company profile/ProfileForms",
  component: ProfileForms,
  tags: ["ui", "pages", "company-page"],
  parameters: {},
  argTypes: {
    // label: { control: { type: "string" }, defaultValue: 'Firstname'},
    // errorMessage: { control: { type: "string" }, defaultValue: 'FOUT!'},
    // description: { control: { type: "string" }, defaultValue: 'fill in your firstname'},
    // isDisabled: { control: { type: "boolean" }, defaultValue: false},
    // isReadonly: { control: { type: "boolean" }, defaultValue: false},
    // defaultValue: { control: { type: "string" }, defaultValue: ''},
    // isRequired: { control: { type: "boolean" }, defaultValue: false},
    // type: {
    //   options: ["text", "email", "password"],
    //   control: { type: "select" },
    //   defaultValue: "text",
    // },
    // validationState: {
    //   options: ["valid", "invalid"],
    //   control: { type: "select" },
    //   defaultValue: "valid",
    // },
  },
};

export default meta;

type Story = StoryObj<typeof ProfileForms>;

export const Default: Story = {
  render: (args) => {
    return (
        <ProfileForms/>
    );
  },
  args: {
    label: "E-mailadres",
    // errorMessage: "Dit is een foutmelding",
    // description: "",
    // validationState: "valid",
    // isDisabled: false,
    // isReadOnly: false,
    // defaultValue: "",
    // isRequired: false,
    // type: "text",
  },
};

import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import {
    <PERSON>,
    <PERSON><PERSON>ultVariant,
    PageContentVariant,
    CTADefaultVariant,
    PlaceholderVariant,
    CTAOutlineVariant,
    ContentWithImageVariant,
    ContentWithImageNoTitleVariant,
    ContentWithImageTitleInlineVariant,
    BannerConfiguration,
    ContentWithPlaceholderVariant,
    ContentWithPlaceholderTitleInlineVariant
} from "ui";

const K_INNER_IMAGE = "https://assets-prd.raicore.com/-/media/project/rai-amsterdam/metstrade/metstrade/met/sliders/met_main-header_boat_1900x400.jpg"
const K_BACKGROUND_IMAGE = "https://assets-prd.raicore.com/-/media/project/rai-amsterdam/amsterdam-drone-week/adw/sliders/adw-header-animated.gif"
const K_RICH_TEXT_BODY = `
  <div class="p-4">
    <h3 class="text-2xl font-bold mb-4">Sample Rich Text Body</h3>
    <p class="text-base text-brand-primary-1">Lorem ipsum dolor sit amet, consectetur adipiscing elit. 
    Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
  </div>
`;


const K_CONFIGURATION_MOCK: BannerConfiguration = {
    titleStyling: {
        headerLevel: 'h2',
        alignment: 'Right',
        color: 'Primary'
    },
    decoration: {
        gradient: "None",
    },
    imagePosition: 'Center bottom',
    position: 'right-to-left',
    innerImageObjectFit: 'object-contain',
    bannerHeightInPixel: 300,
}

const meta: Meta<typeof Banner> = {
    title: "ui/Atomic/Atoms/Common/Banner",
    component: Banner,
    tags: ["ui", "molecules", "common"],
    parameters: {},
    argTypes: {},
    args: {
        backgroundImage: K_BACKGROUND_IMAGE,
        config: K_CONFIGURATION_MOCK
    }
};

export default meta;

type Story = StoryObj<typeof Banner>;

const renderBanner = (args) => (
    <div className="w-screen h-screen p-5 gap-y-[10px]">
        <Banner {...args} />
    </div>
);

export const Default: Story = {
    render: renderBanner,
    args: {
        variant: <DefaultVariant />
    }
};

export const Placeholder: Story = {
    render: renderBanner,
    args: {
        variant: <PlaceholderVariant>
            <div className="bg-white opacity-50 w-full h-full">
            </div>
        </PlaceholderVariant>
    }
};

export const PageContent: Story = {
    render: renderBanner,
    args: {
        variant: <PageContentVariant text="Text" />
    }
};

export const PageContentHtmlText: Story = {
    render: renderBanner,
    args: {
        backgroundImage: K_INNER_IMAGE,
        variant: <PageContentVariant htmlText='<h2 class="w-full h-full items-center text-white text-right text-3xl">METSTRADE 2024<br/>
        RAI AMSTERDAM | 19 - 20 - 21 NOV<h2>' />
    }
};

export const CTADefault: Story = {
    render: renderBanner,
    args: {
        variant: <CTADefaultVariant url="https://rai.nl" label="Launch" />
    }
};

export const CTAOutline: Story = {
    render: renderBanner,
    args: {
        variant: <CTAOutlineVariant url="https://rai.nl" label="Launch" />
    }
}

export const ContentWithPlaceholder: Story = {
    render: renderBanner,
    args: {
        variant: <ContentWithPlaceholderVariant
            placeholder={<div className="bg-white opacity-50 w-full h-full">
            </div>}
            title="My Title"
            body={K_RICH_TEXT_BODY}
        />
    }
}

export const ContentWithImage: Story = {
    render: renderBanner,
    args: {
        variant: <ContentWithImageVariant
            image={K_INNER_IMAGE}
            title="My Title"
            body={K_RICH_TEXT_BODY}
        />
    }
}


export const ContentWithImageNoTitle: Story = {
    render: renderBanner,
    args: {
        variant: <ContentWithImageNoTitleVariant
            image={K_INNER_IMAGE}
            body={K_RICH_TEXT_BODY}
        />
    }
}

export const ContentWithPlaceholderTitleInline: Story = {
    render: renderBanner,
    args: {
        variant: <ContentWithPlaceholderTitleInlineVariant
            placeholder={
                <div className="bg-white opacity-50 w-full h-full">
                </div>}
            title="My Title"
            body={K_RICH_TEXT_BODY}
        />
    }
}

export const ContentWithImageTitleInline: Story = {
    render: renderBanner,
    args: {
        variant: <ContentWithImageTitleInlineVariant
            image={K_INNER_IMAGE}
            title="My Title"
            body={K_RICH_TEXT_BODY}
        />
    }
}

function SampleSectionBlock() {
    return (
        <div className="bg-red-500 text-center h-48 flex flex-col justify-between">
            <div>Sample Section Block, You can put any other component here</div>
            <div>For example: Column Splitter with CTA</div>
            <button className="bg-blue-500" onClick={() => alert("Click")}>Test Clickable</button>
        </div>
    )
}

export const BannerWithSectionBlock: Story = {
    render: renderBanner,
    args: {
        variant: <DefaultVariant />,
        sectionBlock: SampleSectionBlock()
    }
}